#include "linenumberarea.h"
#include <QPainter>
#include <QTextBlock>

LineNumberArea::LineNumberArea(QPlainTextEdit *editor)
    : QWidget(editor), m_editor(editor)
{
    // 设置固定宽度
    setFixedWidth(fontMetrics().horizontalAdvance(QLatin1Char('9')) * 4 + 5);
}

QSize LineNumberArea::sizeHint() const
{
    return QSize(width(), 0);
}

void LineNumberArea::paintEvent(QPaintEvent *event)
{
    QPainter painter(this);
    painter.fillRect(event->rect(), Qt::lightGray);

    QTextBlock block = m_editor->firstVisibleBlock();
    int blockNumber = block.blockNumber();
    int top = (int)m_editor->blockBoundingGeometry(block).translated(m_editor->contentOffset()).top();
    int bottom = top + (int)m_editor->blockBoundingRect(block).height();

    while (block.isValid() && top <= event->rect().bottom()) {
        if (block.isVisible() && bottom >= event->rect().top()) {
            QString number = QString::number(blockNumber + 1);
            painter.setPen(Qt::black);
            painter.drawText(0, top, width() - 5, fontMetrics().height(),
                             Qt::AlignRight, number);
        }

        block = block.next();
        top = bottom;
        bottom = top + (int)m_editor->blockBoundingRect(block).height();
        ++blockNumber;
    }
}
