<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ProgramWidget</class>
 <widget class="QWidget" name="ProgramWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>824</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>ProgramWidget</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QTabWidget" name="scriptTabWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="coord1Tab">
      <attribute name="title">
       <string>坐标系 1 脚本</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="QPlainTextEdit" name="scriptEditorCoord1"/>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="coord2Tab">
      <attribute name="title">
       <string>坐标系 2 脚本</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_3">
       <item>
        <widget class="QPlainTextEdit" name="scriptEditorCoord2"/>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="commandLayout">
     <item>
      <widget class="QComboBox" name="commandComboBox"/>
     </item>
     <item>
      <widget class="QPushButton" name="insertCommandButton">
       <property name="text">
        <string>插入指令</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QPushButton" name="loadButtonCoord1">
       <property name="text">
        <string>加载脚本 1</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="saveButtonCoord1">
       <property name="text">
        <string>保存脚本 1</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="startButtonCoord1">
       <property name="text">
        <string>启动脚本 1</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="stopButtonCoord1">
       <property name="text">
        <string>停止脚本 1</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="loadButtonCoord2">
       <property name="text">
        <string>加载脚本 2</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="saveButtonCoord2">
       <property name="text">
        <string>保存脚本 2</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="startButtonCoord2">
       <property name="text">
        <string>启动脚本 2</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="stopButtonCoord2">
       <property name="text">
        <string>停止脚本 2</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="startAllButton">
       <property name="text">
        <string>全部启动</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="stopAllButton">
       <property name="text">
        <string>全部停止</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QTableWidget" name="sharedVariablesTable">
     <column>
      <property name="text">
       <string>变量名</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>值</string>
      </property>
     </column>
    </widget>
   </item>
   <item>
    <widget class="QTextEdit" name="outputWindow"/>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
