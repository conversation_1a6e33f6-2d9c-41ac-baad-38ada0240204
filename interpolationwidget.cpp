#include "interpolationwidget.h"
#include "ui_interpolationwidget.h"
#include <QMessageBox>
#include <QDebug> // For debug output
#include "admc_api_wrapper.h" // Include the API wrapper

InterpolationWidget::InterpolationWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::InterpolationWidget),
    m_unitConverter(UnitConverter::getInstance()),
    m_cooldownPeriod(2000) // 设置冷却时间为2秒
{
    ui->setupUi(this);
    // Connect signals and slots for buttons
    connect(ui->setCrdPrmButton, &QPushButton::clicked, this, &InterpolationWidget::on_setCrdPrmButton_clicked);
    connect(ui->setLnPrmButton, &QPushButton::clicked, this, &InterpolationWidget::on_setLnPrmButton_clicked);
    connect(ui->setArcPrmButton, &QPushButton::clicked, this, &InterpolationWidget::on_setArcPrmButton_clicked);
    connect(ui->setArc3PointButton, &QPushButton::clicked, this, &InterpolationWidget::on_setArc3PointButton_clicked);
    connect(ui->setArcRadiusButton, &QPushButton::clicked, this, &InterpolationWidget::on_setArcRadiusButton_clicked);
    connect(ui->setArcCenterButton, &QPushButton::clicked, this, &InterpolationWidget::on_setArcCenterButton_clicked);
    connect(ui->startCrdButton, &QPushButton::clicked, this, &InterpolationWidget::on_startCrdButton_clicked);

    // 启动计时器
    m_lastClickTimer.start();

    // 连接单位转换器的信号
    connect(m_unitConverter, &UnitConverter::unitTypeChanged, this, &InterpolationWidget::onUnitTypeChanged);

    // 创建单位切换按钮
    QGroupBox* unitGroupBox = new QGroupBox("单位设置");
    QVBoxLayout* unitLayout = new QVBoxLayout(unitGroupBox);

    QComboBox* unitComboBox = new QComboBox();
    unitComboBox->addItem("pulse");
    unitComboBox->addItem("mm");
    unitComboBox->setCurrentIndex(m_unitConverter->getCurrentUnitType());

    QPushButton* unitSwitchButton = new QPushButton("切换单位");
    connect(unitSwitchButton, &QPushButton::clicked, this, &InterpolationWidget::onUnitSwitchClicked);

    unitLayout->addWidget(new QLabel("选择单位类型："));
    unitLayout->addWidget(unitComboBox);
    unitLayout->addWidget(unitSwitchButton);

    // 将单位设置模块添加到主布局
    ui->verticalLayout_3->addWidget(unitGroupBox);

    connect(unitComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), [this, unitComboBox](int index) {
        m_unitConverter->setCurrentUnitType(static_cast<UnitType>(index));
    });

    // 设置默认参数值
    setDefaultParameters();

    // 更新单位显示
    updateUnitDisplay();
}

InterpolationWidget::~InterpolationWidget()
{
    delete ui;
}

void InterpolationWidget::on_setCrdPrmButton_clicked()
{
    // Get the singleton instance of the wrapper
    AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();

    // 0. Check if the wrapper indicates connection
    if (!wrapper->isConnected()) {
         QMessageBox::critical(this, "错误", "设备未连接。请先连接设备。");
         return;
    }

    // 1. 获取 UI 输入值
    short crd = static_cast<short>(ui->crdComboBox->currentText().toInt());
    bool okVel, okAcc, okPosX, okPosY;

    // 处理带单位的文本，提取数值部分
    QString synVelMaxText = ui->synVelMaxLineEdit->text().split(" ")[0]; // 去除可能存在的单位
    QString synAccMaxText = ui->synAccMaxLineEdit->text().split(" ")[0];
    QString originPosXText = ui->originPosXLineEdit->text().split(" ")[0];
    QString originPosYText = ui->originPosYLineEdit->text().split(" ")[0];

    double synVelMax = synVelMaxText.toDouble(&okVel);
    double synAccMax = synAccMaxText.toDouble(&okAcc);
    // 原点设置标志默认为1
    bool setOriginFlag = true;
    double originPosX = originPosXText.toDouble(&okPosX);
    double originPosY = originPosYText.toDouble(&okPosY);

    // 如果当前单位是mm，需要转换为pulse再传给API
    if (m_unitConverter->getCurrentUnitType() == UNIT_MM) {
        // 位置需要转换，因为1mm = 1000pulse
        originPosX = m_unitConverter->mmToPulse(originPosX);
        originPosY = m_unitConverter->mmToPulse(originPosY);
        // 速度保持不变，因为1mm/s = 1pulse/ms
        // 加速度需要转换，因为1mm/s^2 = 0.001pulse/ms^2
        synAccMax = m_unitConverter->mmPerSSquaredToPulsePerMsSquared(synAccMax);
    }

    // Basic input validation
    if (!okVel || !okAcc || !okPosX || !okPosY) {
        QMessageBox::warning(this, "输入错误", "速度、加速度或原点位置包含无效数值。");
        return;
    }

    // 2. 准备参数 - 新接口直接传递参数
    // 注意：新接口只支持synVelMax和synAccMax参数，原点设置等功能可能需要其他API

    // 3. 通过 AdmcApiWrapper 调用 setCrdPrm 函数
    qDebug() << "Calling AdmcApiWrapper::setCrdPrm for crd:" << crd;
    qDebug() << " synVelMax:" << synVelMax
             << " synAccMax:" << synAccMax
             << " 注意：新接口不支持原点设置，setOriginFlag和originPos参数被忽略";

    short result = wrapper->setCrdPrm(crd, synVelMax, synAccMax);

    // 4. 检查返回值并提示用户
    if (result == 0) { // Assuming 0 means success
        emit apiStatusChanged(QString("坐标系 %1 参数设置成功！").arg(crd), true);
    } else {
        // Use the wrapper's error string function
        QMessageBox::warning(this, "失败", QString("坐标系 %1 参数设置失败: %2").arg(crd).arg(wrapper->getErrorString(result)));
        emit apiStatusChanged(QString("坐标系 %1 参数设置失败: %2").arg(crd).arg(wrapper->getErrorString(result)), false);
    }
}

void InterpolationWidget::on_setArcPrmButton_clicked()
{
    // Get the singleton instance of the wrapper
    AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();

    // 0. Check if the wrapper indicates connection
    if (!wrapper->isConnected()) {
         QMessageBox::critical(this, "错误", "设备未连接。请先连接设备。");
         return;
    }

    // 1. 获取 UI 输入值
    // short crd = static_cast<short>(ui->crdComboBox->currentText().toInt()); // 从坐标系参数设置组获取 crd
    bool okX, okY, okRadius, okVel, okAcc;

    // 处理带单位的文本，提取数值部分
    QString xText = ui->arc_xLineEdit->text().split(" ")[0];
    QString yText = ui->arc_yLineEdit->text().split(" ")[0];
    QString radiusText = ui->arc_radiusLineEdit->text().split(" ")[0];
    QString synVelText = ui->arc_synVelLineEdit->text().split(" ")[0];
    QString synAccText = ui->arc_synAccLineEdit->text().split(" ")[0];

    int32_t x = xText.toInt(&okX);
    int32_t y = yText.toInt(&okY);
    double radius = radiusText.toDouble(&okRadius);
    // short circleDir = static_cast<short>(ui->arc_circleDirSpinBox->value()); // 暂时未使用
    double synVel = synVelText.toDouble(&okVel);
    double synAcc = synAccText.toDouble(&okAcc);
    // 末速度默认为0
    // double velEnd = 0.0; // 暂时未使用

    // 如果当前单位是mm，需要转换为pulse再传给API
    if (m_unitConverter->getCurrentUnitType() == UNIT_MM) {
        // 位置需要转换，因为1mm = 1000pulse
        x = static_cast<int32_t>(m_unitConverter->mmToPulse(x));
        y = static_cast<int32_t>(m_unitConverter->mmToPulse(y));
        radius = m_unitConverter->mmToPulse(radius);
        // 速度保持不变，因为1mm/s = 1pulse/ms
        // 加速度需要转换，因为1mm/s^2 = 0.001pulse/ms^2
        synAcc = m_unitConverter->mmPerSSquaredToPulsePerMsSquared(synAcc);
    }

    // Basic input validation
    if (!okX || !okY || !okRadius || !okVel || !okAcc) {
        QMessageBox::warning(this, "输入错误", "圆弧插补参数包含无效数值。请检查 X, Y, 半径, 速度和加速度。");
        return;
    }

    QMessageBox::information(this, "提示", "请使用具体的圆弧插补功能：三点圆弧、半径圆弧或中心圆弧。");
}

void InterpolationWidget::on_startCrdButton_clicked()
{
    // 检查是否在冷却时间内
    qint64 elapsedMs = m_lastClickTimer.elapsed();
    if (elapsedMs < m_cooldownPeriod) {
        qDebug() << "按钮点击冷却中，还需等待" << (m_cooldownPeriod - elapsedMs) << "毫秒，忽略此次点击";
        return;
    }

    // 获取API包装器实例
    AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();

    // 检查设备连接状态
    if (!wrapper->isConnected()) {
        QMessageBox::critical(this, "错误", "设备未连接。请先连接设备。");
        return;
    }

    // 获取当前选中的坐标系
    short crd = static_cast<short>(ui->crdComboBox->currentText().toInt());

    // 调用API启动坐标系插补运动
    qDebug() << "Calling AdmcApiWrapper::crdStart for crd:" << crd;
    short result = wrapper->crdStart(crd);

    // 检查返回值并提示用户
    if (result == 0) {
        emit apiStatusChanged(QString("坐标系 %1 插补运动已启动！").arg(crd), true);

        // 操作成功，重置计时器开始冷却时间
        m_lastClickTimer.restart();

        // 临时禁用按钮，提供视觉反馈
        ui->startCrdButton->setEnabled(false);
        QTimer::singleShot(500, this, [this]() {
            ui->startCrdButton->setEnabled(true);
        });
    } else {
        QMessageBox::warning(this, "失败", QString("启动坐标系 %1 插补运动失败: %2").arg(crd).arg(wrapper->getErrorString(result)));
        emit apiStatusChanged(QString("启动坐标系 %1 插补运动失败: %2").arg(crd).arg(wrapper->getErrorString(result)), false);
    }
}

void InterpolationWidget::on_setLnPrmButton_clicked()
{
    // Get the singleton instance of the wrapper
    AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();

    // 0. Check if the wrapper indicates connection
    if (!wrapper->isConnected()) {
         QMessageBox::critical(this, "错误", "设备未连接。请先连接设备。");
         return;
    }

    // 1. 获取 UI 输入值
    short crd = static_cast<short>(ui->crdComboBox->currentText().toInt()); // 从坐标系参数设置组获取 crd
    bool okX, okY, okVel, okAcc;

    // 处理带单位的文本，提取数值部分
    QString xText = ui->ln_xLineEdit->text().split(" ")[0];
    QString yText = ui->ln_yLineEdit->text().split(" ")[0];
    QString synVelText = ui->ln_synVelLineEdit->text().split(" ")[0];
    QString synAccText = ui->ln_synAccLineEdit->text().split(" ")[0];

    int32_t x = xText.toInt(&okX);
    int32_t y = yText.toInt(&okY);
    double synVel = synVelText.toDouble(&okVel);
    double synAcc = synAccText.toDouble(&okAcc);
    // 末速度默认为0
    double velEnd = 0.0; // 这个变量在后面的API调用中使用

    // 如果当前单位是mm，需要转换为pulse再传给API
    if (m_unitConverter->getCurrentUnitType() == UNIT_MM) {
        // 位置需要转换，因为1mm = 1000pulse
        x = static_cast<int32_t>(m_unitConverter->mmToPulse(x));
        y = static_cast<int32_t>(m_unitConverter->mmToPulse(y));
        // 速度保持不变，因为1mm/s = 1pulse/ms
        // 加速度需要转换，因为1mm/s^2 = 0.001pulse/ms^2
        synAcc = m_unitConverter->mmPerSSquaredToPulsePerMsSquared(synAcc);
    }

    // Basic input validation
    if (!okX || !okY || !okVel || !okAcc) {
        QMessageBox::warning(this, "输入错误", "直线插补参数包含无效数值。请检查 X, Y, 速度和加速度。");
        return;
    }

    // 2. 调用 AdmcApiWrapper::ln 函数
    qDebug() << "Calling AdmcApiWrapper::ln for crd:" << crd << " x:" << x << " y:" << y
             << " synVel:" << synVel << " synAcc:" << synAcc << " velEnd:" << velEnd;

    short result = wrapper->ln(crd, x, y, synVel, synAcc, velEnd);

    // 3. 检查返回值并提示用户
    if (result == 0) {
        emit apiStatusChanged(QString("坐标系 %1 直线插补参数设置成功！").arg(crd), true);
    } else {
        // Use the wrapper's error string function
        QMessageBox::warning(this, "失败", QString("坐标系 %1 直线插补参数设置失败: %2").arg(crd).arg(wrapper->getErrorString(result)));
        emit apiStatusChanged(QString("坐标系 %1 直线插补参数设置失败: %2").arg(crd).arg(wrapper->getErrorString(result)), false);
    }
}

void InterpolationWidget::on_setButton_clicked()
{
    // TODO: Implement the functionality for setButton clicked
    QMessageBox::information(this, "功能未实现", "Set Button 功能尚未实现。");
}

void InterpolationWidget::updateUnitDisplay()
{
    // 清除旧的单位标签
    // 查找并删除所有带有"unitLabel"对象名的标签
    QList<QLabel*> oldLabels = findChildren<QLabel*>(QRegExp(".*unitLabel.*"));
    for (QLabel* label : oldLabels) {
        label->deleteLater();
    }

    // 获取单位字符串
    QString velUnit = m_unitConverter->getVelocityUnitString();
    QString accUnit = m_unitConverter->getAccelerationUnitString();
    QString posUnit = m_unitConverter->getPositionUnitString();

    // 更新坐标系参数设置的单位显示
    ui->synVelMaxLabel->setText("同步最大速度:");
    ui->synAccMaxLabel->setText("同步最大加速度:");
    ui->originPosLabel->setText("原点位置 (X, Y):");

    // 设置文本框的最小宽度以保持对齐
    ui->synVelMaxLineEdit->setMinimumWidth(120);
    ui->synAccMaxLineEdit->setMinimumWidth(120);
    ui->originPosXLineEdit->setMinimumWidth(120);
    ui->originPosYLineEdit->setMinimumWidth(120);

    // 更新直线插补参数设置的单位显示
    ui->ln_xLabel->setText("X:");
    ui->ln_yLabel->setText("Y:");
    ui->ln_synVelLabel->setText("同步速度:");
    ui->ln_synAccLabel->setText("同步加速度:");

    // 设置文本框的最小宽度以保持对齐
    ui->ln_xLineEdit->setMinimumWidth(120);
    ui->ln_yLineEdit->setMinimumWidth(120);
    ui->ln_synVelLineEdit->setMinimumWidth(120);
    ui->ln_synAccLineEdit->setMinimumWidth(120);

    // 更新新的圆弧插补参数设置的单位显示
    // 三点圆弧插补
    ui->arc3p_p1Label->setText("起点 (X1, Y1):");
    ui->arc3p_p2Label->setText("中间点 (X2, Y2):");
    ui->arc3p_p3Label->setText("终点 (X3, Y3):");
    ui->arc3p_radiusLabel->setText("半径:");
    ui->arc3p_synVelLabel->setText("同步速度:");
    ui->arc3p_synAccLabel->setText("同步加速度:");

    // 半径圆弧插补
    ui->arcR_xLabel->setText("目标X:");
    ui->arcR_yLabel->setText("目标Y:");
    ui->arcR_radiusLabel->setText("半径:");
    ui->arcR_synVelLabel->setText("同步速度:");
    ui->arcR_synAccLabel->setText("同步加速度:");

    // 中心圆弧插补
    ui->arcC_xLabel->setText("目标X:");
    ui->arcC_yLabel->setText("目标Y:");
    ui->arcC_centerXLabel->setText("圆心X:");
    ui->arcC_centerYLabel->setText("圆心Y:");
    ui->arcC_synVelLabel->setText("同步速度:");
    ui->arcC_synAccLabel->setText("同步加速度:");

    // 为坐标系参数设置的输入框添加单位标签
    // 获取当前文本并处理单位
    QString synVelMaxText = ui->synVelMaxLineEdit->text();
    synVelMaxText = synVelMaxText.split(" ")[0]; // 去除可能存在的单位
    ui->synVelMaxLineEdit->setText(synVelMaxText + " " + velUnit);

    QString synAccMaxText = ui->synAccMaxLineEdit->text();
    synAccMaxText = synAccMaxText.split(" ")[0];
    ui->synAccMaxLineEdit->setText(synAccMaxText + " " + accUnit);

    QString originPosXText = ui->originPosXLineEdit->text();
    originPosXText = originPosXText.split(" ")[0];
    ui->originPosXLineEdit->setText(originPosXText + " " + posUnit);

    QString originPosYText = ui->originPosYLineEdit->text();
    originPosYText = originPosYText.split(" ")[0];
    ui->originPosYLineEdit->setText(originPosYText + " " + posUnit);

    // 为直线插补参数输入框添加单位标签
    QString ln_xText = ui->ln_xLineEdit->text();
    ln_xText = ln_xText.split(" ")[0];
    ui->ln_xLineEdit->setText(ln_xText + " " + posUnit);

    QString ln_yText = ui->ln_yLineEdit->text();
    ln_yText = ln_yText.split(" ")[0];
    ui->ln_yLineEdit->setText(ln_yText + " " + posUnit);

    QString ln_synVelText = ui->ln_synVelLineEdit->text();
    ln_synVelText = ln_synVelText.split(" ")[0];
    ui->ln_synVelLineEdit->setText(ln_synVelText + " " + velUnit);

    QString ln_synAccText = ui->ln_synAccLineEdit->text();
    ln_synAccText = ln_synAccText.split(" ")[0];
    ui->ln_synAccLineEdit->setText(ln_synAccText + " " + accUnit);

    // 为新的圆弧插补参数输入框添加单位标签
    // 三点圆弧插补
    QString arc3p_p1XText = ui->arc3p_p1XLineEdit->text();
    arc3p_p1XText = arc3p_p1XText.split(" ")[0];
    ui->arc3p_p1XLineEdit->setText(arc3p_p1XText + " " + posUnit);

    QString arc3p_p1YText = ui->arc3p_p1YLineEdit->text();
    arc3p_p1YText = arc3p_p1YText.split(" ")[0];
    ui->arc3p_p1YLineEdit->setText(arc3p_p1YText + " " + posUnit);

    QString arc3p_p2XText = ui->arc3p_p2XLineEdit->text();
    arc3p_p2XText = arc3p_p2XText.split(" ")[0];
    ui->arc3p_p2XLineEdit->setText(arc3p_p2XText + " " + posUnit);

    QString arc3p_p2YText = ui->arc3p_p2YLineEdit->text();
    arc3p_p2YText = arc3p_p2YText.split(" ")[0];
    ui->arc3p_p2YLineEdit->setText(arc3p_p2YText + " " + posUnit);

    QString arc3p_p3XText = ui->arc3p_p3XLineEdit->text();
    arc3p_p3XText = arc3p_p3XText.split(" ")[0];
    ui->arc3p_p3XLineEdit->setText(arc3p_p3XText + " " + posUnit);

    QString arc3p_p3YText = ui->arc3p_p3YLineEdit->text();
    arc3p_p3YText = arc3p_p3YText.split(" ")[0];
    ui->arc3p_p3YLineEdit->setText(arc3p_p3YText + " " + posUnit);

    QString arc3p_radiusText = ui->arc3p_radiusLineEdit->text();
    arc3p_radiusText = arc3p_radiusText.split(" ")[0];
    ui->arc3p_radiusLineEdit->setText(arc3p_radiusText + " " + posUnit);

    QString arc3p_synVelText = ui->arc3p_synVelLineEdit->text();
    arc3p_synVelText = arc3p_synVelText.split(" ")[0];
    ui->arc3p_synVelLineEdit->setText(arc3p_synVelText + " " + velUnit);

    QString arc3p_synAccText = ui->arc3p_synAccLineEdit->text();
    arc3p_synAccText = arc3p_synAccText.split(" ")[0];
    ui->arc3p_synAccLineEdit->setText(arc3p_synAccText + " " + accUnit);

    // 半径圆弧插补
    QString arcR_xText = ui->arcR_xLineEdit->text();
    arcR_xText = arcR_xText.split(" ")[0];
    ui->arcR_xLineEdit->setText(arcR_xText + " " + posUnit);

    QString arcR_yText = ui->arcR_yLineEdit->text();
    arcR_yText = arcR_yText.split(" ")[0];
    ui->arcR_yLineEdit->setText(arcR_yText + " " + posUnit);

    QString arcR_radiusText = ui->arcR_radiusLineEdit->text();
    arcR_radiusText = arcR_radiusText.split(" ")[0];
    ui->arcR_radiusLineEdit->setText(arcR_radiusText + " " + posUnit);

    QString arcR_synVelText = ui->arcR_synVelLineEdit->text();
    arcR_synVelText = arcR_synVelText.split(" ")[0];
    ui->arcR_synVelLineEdit->setText(arcR_synVelText + " " + velUnit);

    QString arcR_synAccText = ui->arcR_synAccLineEdit->text();
    arcR_synAccText = arcR_synAccText.split(" ")[0];
    ui->arcR_synAccLineEdit->setText(arcR_synAccText + " " + accUnit);

    // 中心圆弧插补
    QString arcC_xText = ui->arcC_xLineEdit->text();
    arcC_xText = arcC_xText.split(" ")[0];
    ui->arcC_xLineEdit->setText(arcC_xText + " " + posUnit);

    QString arcC_yText = ui->arcC_yLineEdit->text();
    arcC_yText = arcC_yText.split(" ")[0];
    ui->arcC_yLineEdit->setText(arcC_yText + " " + posUnit);

    QString arcC_centerXText = ui->arcC_centerXLineEdit->text();
    arcC_centerXText = arcC_centerXText.split(" ")[0];
    ui->arcC_centerXLineEdit->setText(arcC_centerXText + " " + posUnit);

    QString arcC_centerYText = ui->arcC_centerYLineEdit->text();
    arcC_centerYText = arcC_centerYText.split(" ")[0];
    ui->arcC_centerYLineEdit->setText(arcC_centerYText + " " + posUnit);

    QString arcC_synVelText = ui->arcC_synVelLineEdit->text();
    arcC_synVelText = arcC_synVelText.split(" ")[0];
    ui->arcC_synVelLineEdit->setText(arcC_synVelText + " " + velUnit);

    QString arcC_synAccText = ui->arcC_synAccLineEdit->text();
    arcC_synAccText = arcC_synAccText.split(" ")[0];
    ui->arcC_synAccLineEdit->setText(arcC_synAccText + " " + accUnit);
}

void InterpolationWidget::setDefaultParameters()
{
    // 设置坐标系参数的默认值
    if (m_unitConverter->getCurrentUnitType() == UNIT_PULSE) {
        // pulse单位下的默认值
        ui->synVelMaxLineEdit->setText("2000");    // 2000 pulse/ms
        ui->synAccMaxLineEdit->setText("20");      // 20 pulse/ms^2
        ui->originPosXLineEdit->setText("0");      // 0 pulse
        ui->originPosYLineEdit->setText("0");      // 0 pulse
    } else {
        // mm单位下的默认值
        ui->synVelMaxLineEdit->setText("2000");    // 2000 mm/s
        ui->synAccMaxLineEdit->setText("20000");   // 20000 mm/s^2
        ui->originPosXLineEdit->setText("0");      // 0 mm
        ui->originPosYLineEdit->setText("0");      // 0 mm
    }

    // 设置直线插补参数的默认值
    if (m_unitConverter->getCurrentUnitType() == UNIT_PULSE) {
        // pulse单位下的默认值
        ui->ln_xLineEdit->setText("0");           // 0 pulse
        ui->ln_yLineEdit->setText("0");           // 0 pulse
        ui->ln_synVelLineEdit->setText("100");     // 100 pulse/ms
        ui->ln_synAccLineEdit->setText("10");      // 10 pulse/ms^2
    } else {
        // mm单位下的默认值
        ui->ln_xLineEdit->setText("0");           // 0 mm
        ui->ln_yLineEdit->setText("0");           // 0 mm
        ui->ln_synVelLineEdit->setText("100");     // 100 mm/s
        ui->ln_synAccLineEdit->setText("10000");   // 10000 mm/s^2
    }

    // 设置三点圆弧插补参数的默认值
    if (m_unitConverter->getCurrentUnitType() == UNIT_PULSE) {
        // pulse单位下的默认值
        ui->arc3p_p1XLineEdit->setText("0");      // 0 pulse
        ui->arc3p_p1YLineEdit->setText("0");      // 0 pulse
        ui->arc3p_p2XLineEdit->setText("50");     // 50 pulse
        ui->arc3p_p2YLineEdit->setText("50");     // 50 pulse
        ui->arc3p_p3XLineEdit->setText("100");    // 100 pulse
        ui->arc3p_p3YLineEdit->setText("0");      // 0 pulse
        ui->arc3p_radiusLineEdit->setText("50");  // 50 pulse
        ui->arc3p_synVelLineEdit->setText("100"); // 100 pulse/ms
        ui->arc3p_synAccLineEdit->setText("10");  // 10 pulse/ms^2
    } else {
        // mm单位下的默认值
        ui->arc3p_p1XLineEdit->setText("0");      // 0 mm
        ui->arc3p_p1YLineEdit->setText("0");      // 0 mm
        ui->arc3p_p2XLineEdit->setText("0.05");   // 0.05 mm
        ui->arc3p_p2YLineEdit->setText("0.05");   // 0.05 mm
        ui->arc3p_p3XLineEdit->setText("0.1");    // 0.1 mm
        ui->arc3p_p3YLineEdit->setText("0");      // 0 mm
        ui->arc3p_radiusLineEdit->setText("0.05"); // 0.05 mm
        ui->arc3p_synVelLineEdit->setText("100"); // 100 mm/s
        ui->arc3p_synAccLineEdit->setText("10000"); // 10000 mm/s^2
    }

    // 设置半径圆弧插补参数的默认值
    if (m_unitConverter->getCurrentUnitType() == UNIT_PULSE) {
        // pulse单位下的默认值
        ui->arcR_xLineEdit->setText("100");       // 100 pulse
        ui->arcR_yLineEdit->setText("0");         // 0 pulse
        ui->arcR_radiusLineEdit->setText("50");   // 50 pulse
        ui->arcR_synVelLineEdit->setText("100");  // 100 pulse/ms
        ui->arcR_synAccLineEdit->setText("10");   // 10 pulse/ms^2
    } else {
        // mm单位下的默认值
        ui->arcR_xLineEdit->setText("0.1");       // 0.1 mm
        ui->arcR_yLineEdit->setText("0");         // 0 mm
        ui->arcR_radiusLineEdit->setText("0.05"); // 0.05 mm
        ui->arcR_synVelLineEdit->setText("100");  // 100 mm/s
        ui->arcR_synAccLineEdit->setText("10000"); // 10000 mm/s^2
    }

    // 设置中心圆弧插补参数的默认值
    if (m_unitConverter->getCurrentUnitType() == UNIT_PULSE) {
        // pulse单位下的默认值
        ui->arcC_xLineEdit->setText("100");       // 100 pulse
        ui->arcC_yLineEdit->setText("0");         // 0 pulse
        ui->arcC_centerXLineEdit->setText("50");  // 50 pulse
        ui->arcC_centerYLineEdit->setText("0");   // 0 pulse
        ui->arcC_synVelLineEdit->setText("100");  // 100 pulse/ms
        ui->arcC_synAccLineEdit->setText("10");   // 10 pulse/ms^2
    } else {
        // mm单位下的默认值
        ui->arcC_xLineEdit->setText("0.1");       // 0.1 mm
        ui->arcC_yLineEdit->setText("0");         // 0 mm
        ui->arcC_centerXLineEdit->setText("0.05"); // 0.05 mm
        ui->arcC_centerYLineEdit->setText("0");   // 0 mm
        ui->arcC_synVelLineEdit->setText("100");  // 100 mm/s
        ui->arcC_synAccLineEdit->setText("10000"); // 10000 mm/s^2
    }

    // 设置完默认参数后更新单位显示
    updateUnitDisplay();
}

void InterpolationWidget::onUnitTypeChanged(UnitType type)
{
    // 转换坐标系参数的值
    bool /*okVelMax,*/ okAccMax, okPosX, okPosY;
    // 获取文本并处理单位
    QString synAccMaxText = ui->synAccMaxLineEdit->text().split(" ")[0];
    QString originPosXText = ui->originPosXLineEdit->text().split(" ")[0];
    QString originPosYText = ui->originPosYLineEdit->text().split(" ")[0];

    double synAccMax = synAccMaxText.toDouble(&okAccMax);
    double originPosX = originPosXText.toDouble(&okPosX);
    double originPosY = originPosYText.toDouble(&okPosY);

    if (type == UNIT_PULSE) {
        // 从 mm 转换到 pulse
        if (okPosX) ui->originPosXLineEdit->setText(QString::number(originPosX * 1000)); // 从 mm 转换到 pulse
        if (okPosY) ui->originPosYLineEdit->setText(QString::number(originPosY * 1000)); // 从 mm 转换到 pulse
        // 速度保持不变
        // 加速度需要转换
        if (okAccMax) ui->synAccMaxLineEdit->setText(QString::number(synAccMax * 0.001)); // 从 mm/s^2 转换到 pulse/ms^2
    } else {
        // 从 pulse 转换到 mm
        if (okPosX) ui->originPosXLineEdit->setText(QString::number(originPosX * 0.001)); // 从 pulse 转换到 mm
        if (okPosY) ui->originPosYLineEdit->setText(QString::number(originPosY * 0.001)); // 从 pulse 转换到 mm
        // 速度保持不变
        // 加速度需要转换
        if (okAccMax) ui->synAccMaxLineEdit->setText(QString::number(synAccMax * 1000)); // 从 pulse/ms^2 转换到 mm/s^2
    }

    // 转换直线插补参数的值
    bool okLnX, okLnY, /*okLnVel,*/ okLnAcc;
    QString lnXText = ui->ln_xLineEdit->text().split(" ")[0];
    QString lnYText = ui->ln_yLineEdit->text().split(" ")[0];
    QString lnAccText = ui->ln_synAccLineEdit->text().split(" ")[0];

    double lnX = lnXText.toDouble(&okLnX);
    double lnY = lnYText.toDouble(&okLnY);
    double lnAcc = lnAccText.toDouble(&okLnAcc);

    if (type == UNIT_PULSE) {
        // 从 mm 转换到 pulse
        if (okLnX) ui->ln_xLineEdit->setText(QString::number(static_cast<int>(lnX * 1000))); // 从 mm 转换到 pulse
        if (okLnY) ui->ln_yLineEdit->setText(QString::number(static_cast<int>(lnY * 1000))); // 从 mm 转换到 pulse
        // 速度保持不变
        // 加速度需要转换
        if (okLnAcc) ui->ln_synAccLineEdit->setText(QString::number(lnAcc * 0.001)); // 从 mm/s^2 转换到 pulse/ms^2
    } else {
        // 从 pulse 转换到 mm
        if (okLnX) ui->ln_xLineEdit->setText(QString::number(lnX * 0.001, 'f', 3)); // 从 pulse 转换到 mm
        if (okLnY) ui->ln_yLineEdit->setText(QString::number(lnY * 0.001, 'f', 3)); // 从 pulse 转换到 mm
        // 速度保持不变
        // 加速度需要转换
        if (okLnAcc) ui->ln_synAccLineEdit->setText(QString::number(lnAcc * 1000)); // 从 pulse/ms^2 转换到 mm/s^2
    }

    // 转换新的圆弧插补参数的值
    // 三点圆弧插补参数转换
    bool okArc3pP1X, okArc3pP1Y, okArc3pP2X, okArc3pP2Y, okArc3pP3X, okArc3pP3Y, okArc3pRadius, okArc3pAcc;
    QString arc3pP1XText = ui->arc3p_p1XLineEdit->text().split(" ")[0];
    QString arc3pP1YText = ui->arc3p_p1YLineEdit->text().split(" ")[0];
    QString arc3pP2XText = ui->arc3p_p2XLineEdit->text().split(" ")[0];
    QString arc3pP2YText = ui->arc3p_p2YLineEdit->text().split(" ")[0];
    QString arc3pP3XText = ui->arc3p_p3XLineEdit->text().split(" ")[0];
    QString arc3pP3YText = ui->arc3p_p3YLineEdit->text().split(" ")[0];
    QString arc3pRadiusText = ui->arc3p_radiusLineEdit->text().split(" ")[0];
    QString arc3pAccText = ui->arc3p_synAccLineEdit->text().split(" ")[0];

    double arc3pP1X = arc3pP1XText.toDouble(&okArc3pP1X);
    double arc3pP1Y = arc3pP1YText.toDouble(&okArc3pP1Y);
    double arc3pP2X = arc3pP2XText.toDouble(&okArc3pP2X);
    double arc3pP2Y = arc3pP2YText.toDouble(&okArc3pP2Y);
    double arc3pP3X = arc3pP3XText.toDouble(&okArc3pP3X);
    double arc3pP3Y = arc3pP3YText.toDouble(&okArc3pP3Y);
    double arc3pRadius = arc3pRadiusText.toDouble(&okArc3pRadius);
    double arc3pAcc = arc3pAccText.toDouble(&okArc3pAcc);

    // 半径圆弧插补参数转换
    bool okArcRX, okArcRY, okArcRRadius, okArcRAcc;
    QString arcRXText = ui->arcR_xLineEdit->text().split(" ")[0];
    QString arcRYText = ui->arcR_yLineEdit->text().split(" ")[0];
    QString arcRRadiusText = ui->arcR_radiusLineEdit->text().split(" ")[0];
    QString arcRAccText = ui->arcR_synAccLineEdit->text().split(" ")[0];

    double arcRX = arcRXText.toDouble(&okArcRX);
    double arcRY = arcRYText.toDouble(&okArcRY);
    double arcRRadius = arcRRadiusText.toDouble(&okArcRRadius);
    double arcRAcc = arcRAccText.toDouble(&okArcRAcc);

    // 中心圆弧插补参数转换
    bool okArcCX, okArcCY, okArcCCenterX, okArcCCenterY, okArcCAcc;
    QString arcCXText = ui->arcC_xLineEdit->text().split(" ")[0];
    QString arcCYText = ui->arcC_yLineEdit->text().split(" ")[0];
    QString arcCCenterXText = ui->arcC_centerXLineEdit->text().split(" ")[0];
    QString arcCCenterYText = ui->arcC_centerYLineEdit->text().split(" ")[0];
    QString arcCAccText = ui->arcC_synAccLineEdit->text().split(" ")[0];

    double arcCX = arcCXText.toDouble(&okArcCX);
    double arcCY = arcCYText.toDouble(&okArcCY);
    double arcCCenterX = arcCCenterXText.toDouble(&okArcCCenterX);
    double arcCCenterY = arcCCenterYText.toDouble(&okArcCCenterY);
    double arcCAcc = arcCAccText.toDouble(&okArcCAcc);

    if (type == UNIT_PULSE) {
        // 从 mm 转换到 pulse
        // 三点圆弧插补
        if (okArc3pP1X) ui->arc3p_p1XLineEdit->setText(QString::number(static_cast<int>(arc3pP1X * 1000)));
        if (okArc3pP1Y) ui->arc3p_p1YLineEdit->setText(QString::number(static_cast<int>(arc3pP1Y * 1000)));
        if (okArc3pP2X) ui->arc3p_p2XLineEdit->setText(QString::number(static_cast<int>(arc3pP2X * 1000)));
        if (okArc3pP2Y) ui->arc3p_p2YLineEdit->setText(QString::number(static_cast<int>(arc3pP2Y * 1000)));
        if (okArc3pP3X) ui->arc3p_p3XLineEdit->setText(QString::number(static_cast<int>(arc3pP3X * 1000)));
        if (okArc3pP3Y) ui->arc3p_p3YLineEdit->setText(QString::number(static_cast<int>(arc3pP3Y * 1000)));
        if (okArc3pRadius) ui->arc3p_radiusLineEdit->setText(QString::number(arc3pRadius * 1000));
        if (okArc3pAcc) ui->arc3p_synAccLineEdit->setText(QString::number(arc3pAcc * 0.001));

        // 半径圆弧插补
        if (okArcRX) ui->arcR_xLineEdit->setText(QString::number(static_cast<int>(arcRX * 1000)));
        if (okArcRY) ui->arcR_yLineEdit->setText(QString::number(static_cast<int>(arcRY * 1000)));
        if (okArcRRadius) ui->arcR_radiusLineEdit->setText(QString::number(arcRRadius * 1000));
        if (okArcRAcc) ui->arcR_synAccLineEdit->setText(QString::number(arcRAcc * 0.001));

        // 中心圆弧插补
        if (okArcCX) ui->arcC_xLineEdit->setText(QString::number(static_cast<int>(arcCX * 1000)));
        if (okArcCY) ui->arcC_yLineEdit->setText(QString::number(static_cast<int>(arcCY * 1000)));
        if (okArcCCenterX) ui->arcC_centerXLineEdit->setText(QString::number(static_cast<int>(arcCCenterX * 1000)));
        if (okArcCCenterY) ui->arcC_centerYLineEdit->setText(QString::number(static_cast<int>(arcCCenterY * 1000)));
        if (okArcCAcc) ui->arcC_synAccLineEdit->setText(QString::number(arcCAcc * 0.001));
    } else {
        // 从 pulse 转换到 mm
        // 三点圆弧插补
        if (okArc3pP1X) ui->arc3p_p1XLineEdit->setText(QString::number(arc3pP1X * 0.001, 'f', 3));
        if (okArc3pP1Y) ui->arc3p_p1YLineEdit->setText(QString::number(arc3pP1Y * 0.001, 'f', 3));
        if (okArc3pP2X) ui->arc3p_p2XLineEdit->setText(QString::number(arc3pP2X * 0.001, 'f', 3));
        if (okArc3pP2Y) ui->arc3p_p2YLineEdit->setText(QString::number(arc3pP2Y * 0.001, 'f', 3));
        if (okArc3pP3X) ui->arc3p_p3XLineEdit->setText(QString::number(arc3pP3X * 0.001, 'f', 3));
        if (okArc3pP3Y) ui->arc3p_p3YLineEdit->setText(QString::number(arc3pP3Y * 0.001, 'f', 3));
        if (okArc3pRadius) ui->arc3p_radiusLineEdit->setText(QString::number(arc3pRadius * 0.001, 'f', 3));
        if (okArc3pAcc) ui->arc3p_synAccLineEdit->setText(QString::number(arc3pAcc * 1000));

        // 半径圆弧插补
        if (okArcRX) ui->arcR_xLineEdit->setText(QString::number(arcRX * 0.001, 'f', 3));
        if (okArcRY) ui->arcR_yLineEdit->setText(QString::number(arcRY * 0.001, 'f', 3));
        if (okArcRRadius) ui->arcR_radiusLineEdit->setText(QString::number(arcRRadius * 0.001, 'f', 3));
        if (okArcRAcc) ui->arcR_synAccLineEdit->setText(QString::number(arcRAcc * 1000));

        // 中心圆弧插补
        if (okArcCX) ui->arcC_xLineEdit->setText(QString::number(arcCX * 0.001, 'f', 3));
        if (okArcCY) ui->arcC_yLineEdit->setText(QString::number(arcCY * 0.001, 'f', 3));
        if (okArcCCenterX) ui->arcC_centerXLineEdit->setText(QString::number(arcCCenterX * 0.001, 'f', 3));
        if (okArcCCenterY) ui->arcC_centerYLineEdit->setText(QString::number(arcCCenterY * 0.001, 'f', 3));
        if (okArcCAcc) ui->arcC_synAccLineEdit->setText(QString::number(arcCAcc * 1000));
    }

    // 在所有转换完成后更新单位显示
    updateUnitDisplay();
}

void InterpolationWidget::onUnitSwitchClicked()
{
    // 切换单位类型
    UnitType currentType = m_unitConverter->getCurrentUnitType();
    UnitType newType = (currentType == UNIT_PULSE) ? UNIT_MM : UNIT_PULSE;

    // 设置新的单位类型
    m_unitConverter->setCurrentUnitType(newType);
}

// 三点圆弧插补参数设置
void InterpolationWidget::on_setArc3PointButton_clicked()
{
    // 获取API包装器实例
    AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();

    // 检查设备连接状态
    if (!wrapper->isConnected()) {
        QMessageBox::critical(this, "错误", "设备未连接。请先连接设备。");
        return;
    }

    // 获取当前选中的坐标系
    short crd = static_cast<short>(ui->crdComboBox->currentText().toInt());

    // 获取三点圆弧插补参数
    bool okP1X, okP1Y, okP2X, okP2Y, okP3X, okP3Y, okRadius, okVel, okAcc;

    // 获取三个点的坐标
    QString p1XText = ui->arc3p_p1XLineEdit->text().split(" ")[0];
    QString p1YText = ui->arc3p_p1YLineEdit->text().split(" ")[0];
    QString p2XText = ui->arc3p_p2XLineEdit->text().split(" ")[0];
    QString p2YText = ui->arc3p_p2YLineEdit->text().split(" ")[0];
    QString p3XText = ui->arc3p_p3XLineEdit->text().split(" ")[0];
    QString p3YText = ui->arc3p_p3YLineEdit->text().split(" ")[0];
    QString radiusText = ui->arc3p_radiusLineEdit->text().split(" ")[0];
    QString velText = ui->arc3p_synVelLineEdit->text().split(" ")[0];
    QString accText = ui->arc3p_synAccLineEdit->text().split(" ")[0];

    int32_t p1[3], p2[3], p3[3]; // API需要3维数组，第三维通常为0
    p1[0] = static_cast<int32_t>(p1XText.toDouble(&okP1X));
    p1[1] = static_cast<int32_t>(p1YText.toDouble(&okP1Y));
    p1[2] = 0;
    p2[0] = static_cast<int32_t>(p2XText.toDouble(&okP2X));
    p2[1] = static_cast<int32_t>(p2YText.toDouble(&okP2Y));
    p2[2] = 0;
    p3[0] = static_cast<int32_t>(p3XText.toDouble(&okP3X));
    p3[1] = static_cast<int32_t>(p3YText.toDouble(&okP3Y));
    p3[2] = 0;

    double radius = radiusText.toDouble(&okRadius);
    double synVel = velText.toDouble(&okVel);
    double synAcc = accText.toDouble(&okAcc);
    short circleDir = static_cast<short>(ui->arc3p_dirSpinBox->value());

    // 如果当前单位是mm，需要转换为pulse再传给API
    if (m_unitConverter->getCurrentUnitType() == UNIT_MM) {
        // 位置需要转换，因为1mm = 1000pulse
        p1[0] = static_cast<int32_t>(m_unitConverter->mmToPulse(p1[0]));
        p1[1] = static_cast<int32_t>(m_unitConverter->mmToPulse(p1[1]));
        p2[0] = static_cast<int32_t>(m_unitConverter->mmToPulse(p2[0]));
        p2[1] = static_cast<int32_t>(m_unitConverter->mmToPulse(p2[1]));
        p3[0] = static_cast<int32_t>(m_unitConverter->mmToPulse(p3[0]));
        p3[1] = static_cast<int32_t>(m_unitConverter->mmToPulse(p3[1]));
        radius = m_unitConverter->mmToPulse(radius);
        // 速度保持不变，因为1mm/s = 1pulse/ms
        // 加速度需要转换，因为1mm/s^2 = 0.001pulse/ms^2
        synAcc = m_unitConverter->mmPerSSquaredToPulsePerMsSquared(synAcc);
    }

    // 参数验证
    if (!okP1X || !okP1Y || !okP2X || !okP2Y || !okP3X || !okP3Y || !okRadius || !okVel || !okAcc) {
        QMessageBox::warning(this, "输入错误", "三点圆弧插补参数包含无效数值。请检查所有输入参数。");
        return;
    }

    // 调用API执行三点圆弧插补
    qDebug() << "Calling AdmcApiWrapper::ArcXY_3point for crd:" << crd;
    short result = wrapper->ArcXY_3point(crd, p1, p2, p3, radius, circleDir, synVel, synAcc);

    // 检查返回值并提示用户
    if (result == 0) {
        emit apiStatusChanged(QString("坐标系 %1 三点圆弧插补参数设置成功！").arg(crd), true);
    } else {
        QMessageBox::warning(this, "失败", QString("坐标系 %1 三点圆弧插补参数设置失败: %2").arg(crd).arg(wrapper->getErrorString(result)));
        emit apiStatusChanged(QString("坐标系 %1 三点圆弧插补参数设置失败: %2").arg(crd).arg(wrapper->getErrorString(result)), false);
    }
}

// 半径圆弧插补参数设置
void InterpolationWidget::on_setArcRadiusButton_clicked()
{
    // 获取API包装器实例
    AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();

    // 检查设备连接状态
    if (!wrapper->isConnected()) {
        QMessageBox::critical(this, "错误", "设备未连接。请先连接设备。");
        return;
    }

    // 获取当前选中的坐标系
    short crd = static_cast<short>(ui->crdComboBox->currentText().toInt());

    // 获取半径圆弧插补参数
    bool okX, okY, okRadius, okVel, okAcc;
    QString xText = ui->arcR_xLineEdit->text().split(" ")[0];
    QString yText = ui->arcR_yLineEdit->text().split(" ")[0];
    QString radiusText = ui->arcR_radiusLineEdit->text().split(" ")[0];
    QString velText = ui->arcR_synVelLineEdit->text().split(" ")[0];
    QString accText = ui->arcR_synAccLineEdit->text().split(" ")[0];

    int32_t x = static_cast<int32_t>(xText.toDouble(&okX));
    int32_t y = static_cast<int32_t>(yText.toDouble(&okY));
    double radius = radiusText.toDouble(&okRadius);
    double synVel = velText.toDouble(&okVel);
    double synAcc = accText.toDouble(&okAcc);
    short circleDir = static_cast<short>(ui->arcR_dirSpinBox->value());

    // 如果当前单位是mm，需要转换为pulse再传给API
    if (m_unitConverter->getCurrentUnitType() == UNIT_MM) {
        // 位置需要转换，因为1mm = 1000pulse
        x = static_cast<int32_t>(m_unitConverter->mmToPulse(x));
        y = static_cast<int32_t>(m_unitConverter->mmToPulse(y));
        radius = m_unitConverter->mmToPulse(radius);
        // 速度保持不变，因为1mm/s = 1pulse/ms
        // 加速度需要转换，因为1mm/s^2 = 0.001pulse/ms^2
        synAcc = m_unitConverter->mmPerSSquaredToPulsePerMsSquared(synAcc);
    }

    // 参数验证
    if (!okX || !okY || !okRadius || !okVel || !okAcc) {
        QMessageBox::warning(this, "输入错误", "半径圆弧插补参数包含无效数值。请检查所有输入参数。");
        return;
    }

    // 调用API执行半径圆弧插补
    qDebug() << "Calling AdmcApiWrapper::ArcXYR for crd:" << crd;
    double velEnd = 0.0; // 保持兼容性
    short result = wrapper->ArcXYR(crd, x, y, radius, circleDir, synVel, synAcc, velEnd);

    // 检查返回值并提示用户
    if (result == 0) {
        emit apiStatusChanged(QString("坐标系 %1 半径圆弧插补参数设置成功！").arg(crd), true);
    } else {
        QMessageBox::warning(this, "失败", QString("坐标系 %1 半径圆弧插补参数设置失败: %2").arg(crd).arg(wrapper->getErrorString(result)));
        emit apiStatusChanged(QString("坐标系 %1 半径圆弧插补参数设置失败: %2").arg(crd).arg(wrapper->getErrorString(result)), false);
    }
}

// 中心圆弧插补参数设置
void InterpolationWidget::on_setArcCenterButton_clicked()
{
    // 获取API包装器实例
    AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();

    // 检查设备连接状态
    if (!wrapper->isConnected()) {
        QMessageBox::critical(this, "错误", "设备未连接。请先连接设备。");
        return;
    }

    // 获取当前选中的坐标系
    short crd = static_cast<short>(ui->crdComboBox->currentText().toInt());

    // 获取中心圆弧插补参数
    bool okX, okY, okCenterX, okCenterY, okVel, okAcc;
    QString xText = ui->arcC_xLineEdit->text().split(" ")[0];
    QString yText = ui->arcC_yLineEdit->text().split(" ")[0];
    QString centerXText = ui->arcC_centerXLineEdit->text().split(" ")[0];
    QString centerYText = ui->arcC_centerYLineEdit->text().split(" ")[0];
    QString velText = ui->arcC_synVelLineEdit->text().split(" ")[0];
    QString accText = ui->arcC_synAccLineEdit->text().split(" ")[0];

    int32_t x = static_cast<int32_t>(xText.toDouble(&okX));
    int32_t y = static_cast<int32_t>(yText.toDouble(&okY));
    double xCenter = centerXText.toDouble(&okCenterX);
    double yCenter = centerYText.toDouble(&okCenterY);
    double synVel = velText.toDouble(&okVel);
    double synAcc = accText.toDouble(&okAcc);
    short circleDir = static_cast<short>(ui->arcC_dirSpinBox->value());

    // 如果当前单位是mm，需要转换为pulse再传给API
    if (m_unitConverter->getCurrentUnitType() == UNIT_MM) {
        // 位置需要转换，因为1mm = 1000pulse
        x = static_cast<int32_t>(m_unitConverter->mmToPulse(x));
        y = static_cast<int32_t>(m_unitConverter->mmToPulse(y));
        xCenter = m_unitConverter->mmToPulse(xCenter);
        yCenter = m_unitConverter->mmToPulse(yCenter);
        // 速度保持不变，因为1mm/s = 1pulse/ms
        // 加速度需要转换，因为1mm/s^2 = 0.001pulse/ms^2
        synAcc = m_unitConverter->mmPerSSquaredToPulsePerMsSquared(synAcc);
    }

    // 参数验证
    if (!okX || !okY || !okCenterX || !okCenterY || !okVel || !okAcc) {
        QMessageBox::warning(this, "输入错误", "中心圆弧插补参数包含无效数值。请检查所有输入参数。");
        return;
    }

    // 调用API执行中心圆弧插补
    qDebug() << "Calling AdmcApiWrapper::ArcXYC for crd:" << crd;
    short result = wrapper->ArcXYC(crd, x, y, xCenter, yCenter, circleDir, synVel, synAcc);

    // 检查返回值并提示用户
    if (result == 0) {
        emit apiStatusChanged(QString("坐标系 %1 中心圆弧插补参数设置成功！").arg(crd), true);
    } else {
        QMessageBox::warning(this, "失败", QString("坐标系 %1 中心圆弧插补参数设置失败: %2").arg(crd).arg(wrapper->getErrorString(result)));
        emit apiStatusChanged(QString("坐标系 %1 中心圆弧插补参数设置失败: %2").arg(crd).arg(wrapper->getErrorString(result)), false);
    }
}