#include "axisinitwidget.h"
#include <QDebug>

AxisInitWidget::AxisInitWidget(QWidget *parent) : QWidget(parent),
    m_unitConverter(UnitConverter::getInstance())
{
    setupUI();

    // 连接单位转换器的信号
    connect(m_unitConverter, &UnitConverter::unitTypeChanged, this, &AxisInitWidget::onUnitTypeChanged);

    // 设置默认参数值
    setDefaultParameters();

    // 更新单位显示
    updateUnitDisplay();
}

AxisInitWidget::~AxisInitWidget()
{
}

void AxisInitWidget::setupUI()
{
    // 创建主布局
    QVBoxLayout* mainLayout = new QVBoxLayout(this);

    // 创建分组框
    QGroupBox* groupBox = new QGroupBox("轴参数初始化");
    mainLayout->addWidget(groupBox);

    // 创建分组框内的网格布局
    QGridLayout* gridLayout = new QGridLayout(groupBox);

    // 坐标系选择
    m_labelCrd = new QLabel("坐标系:");
    m_comboCrd = new QComboBox();
    m_comboCrd->addItem("0");
    m_comboCrd->addItem("1");
    gridLayout->addWidget(m_labelCrd, 0, 0);
    gridLayout->addWidget(m_comboCrd, 0, 1);

    // 添加标题行
    QLabel* labelX = new QLabel("X轴");
    QLabel* labelY = new QLabel("Y轴");
    gridLayout->addWidget(labelX, 1, 1);
    gridLayout->addWidget(labelY, 1, 2);

    // AxisMap (轴映射)
    m_labelAxisMap = new QLabel("轴映射:");
    m_spinAxisMapX = new QSpinBox();
    m_spinAxisMapY = new QSpinBox();
    m_spinAxisMapX->setRange(0, 10);
    m_spinAxisMapY->setRange(0, 10);
    m_spinAxisMapX->setValue(0);
    m_spinAxisMapY->setValue(1);
    // 设置固定宽度以保持对齐
    m_spinAxisMapX->setMinimumWidth(120);
    m_spinAxisMapY->setMinimumWidth(120);
    gridLayout->addWidget(m_labelAxisMap, 2, 0);
    gridLayout->addWidget(m_spinAxisMapX, 2, 1);
    gridLayout->addWidget(m_spinAxisMapY, 2, 2);

    // AxisDir (轴方向)
    m_labelAxisDir = new QLabel("轴方向:");
    m_spinAxisDirX = new QSpinBox();
    m_spinAxisDirY = new QSpinBox();
    m_spinAxisDirX->setRange(-1, 1);
    m_spinAxisDirY->setRange(-1, 1);
    m_spinAxisDirX->setValue(1);
    m_spinAxisDirY->setValue(1);
    // 设置固定宽度以保持对齐
    m_spinAxisDirX->setMinimumWidth(120);
    m_spinAxisDirY->setMinimumWidth(120);
    gridLayout->addWidget(m_labelAxisDir, 3, 0);
    gridLayout->addWidget(m_spinAxisDirX, 3, 1);
    gridLayout->addWidget(m_spinAxisDirY, 3, 2);

    // VelMax (最大速度)
    m_labelVelMax = new QLabel("最大速度:");
    m_spinVelMaxX = new QSpinBox();
    m_spinVelMaxY = new QSpinBox();
    m_spinVelMaxX->setRange(0, 1000000);
    m_spinVelMaxY->setRange(0, 1000000);
    m_spinVelMaxX->setValue(3000);
    m_spinVelMaxY->setValue(3000);
    // 设置固定宽度以保持对齐
    m_spinVelMaxX->setMinimumWidth(120);
    m_spinVelMaxY->setMinimumWidth(120);
    gridLayout->addWidget(m_labelVelMax, 4, 0);
    gridLayout->addWidget(m_spinVelMaxX, 4, 1);
    gridLayout->addWidget(m_spinVelMaxY, 4, 2);

    // AccMax (最大加速度)
    m_labelAccMax = new QLabel("最大加速度:");
    m_spinAccMaxX = new QSpinBox();
    m_spinAccMaxY = new QSpinBox();
    m_spinAccMaxX->setRange(0, 1000000);
    m_spinAccMaxY->setRange(0, 1000000);
    m_spinAccMaxX->setValue(30);
    m_spinAccMaxY->setValue(30);
    // 设置固定宽度以保持对齐
    m_spinAccMaxX->setMinimumWidth(120);
    m_spinAccMaxY->setMinimumWidth(120);
    gridLayout->addWidget(m_labelAccMax, 5, 0);
    gridLayout->addWidget(m_spinAccMaxX, 5, 1);
    gridLayout->addWidget(m_spinAccMaxY, 5, 2);

    // Positive (正向限位)
    m_labelPositive = new QLabel("正向限位:");
    m_spinPositiveX = new QSpinBox();
    m_spinPositiveY = new QSpinBox();
    m_spinPositiveX->setRange(-1000000, 1000000);
    m_spinPositiveY->setRange(-1000000, 1000000);
    m_spinPositiveX->setValue(1000000);
    m_spinPositiveY->setValue(1000000);
    // 设置固定宽度以保持对齐
    m_spinPositiveX->setMinimumWidth(120);
    m_spinPositiveY->setMinimumWidth(120);
    gridLayout->addWidget(m_labelPositive, 6, 0);
    gridLayout->addWidget(m_spinPositiveX, 6, 1);
    gridLayout->addWidget(m_spinPositiveY, 6, 2);

    // Negative (负向限位)
    m_labelNegative = new QLabel("负向限位:");
    m_spinNegativeX = new QSpinBox();
    m_spinNegativeY = new QSpinBox();
    m_spinNegativeX->setRange(-1000000, 1000000);
    m_spinNegativeY->setRange(-1000000, 1000000);
    m_spinNegativeX->setValue(0);
    m_spinNegativeY->setValue(0);
    // 设置固定宽度以保持对齐
    m_spinNegativeX->setMinimumWidth(120);
    m_spinNegativeY->setMinimumWidth(120);
    gridLayout->addWidget(m_labelNegative, 7, 0);
    gridLayout->addWidget(m_spinNegativeX, 7, 1);
    gridLayout->addWidget(m_spinNegativeY, 7, 2);

    // 设置按钮
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    m_btnSet = new QPushButton("设置参数");
    buttonLayout->addStretch();
    buttonLayout->addWidget(m_btnSet);
    mainLayout->addLayout(buttonLayout);

    // 连接信号槽
    connect(m_btnSet, &QPushButton::clicked, this, &AxisInitWidget::onSetButtonClicked);
}

void AxisInitWidget::updateUnitDisplay()
{
    // 获取单位字符串
    QString velUnit = m_unitConverter->getVelocityUnitString();
    QString accUnit = m_unitConverter->getAccelerationUnitString();
    QString posUnit = m_unitConverter->getPositionUnitString();

    // 更新速度和加速度输入框的单位显示
    m_spinVelMaxX->setSuffix(QString(" %1").arg(velUnit));
    m_spinVelMaxY->setSuffix(QString(" %1").arg(velUnit));
    m_spinAccMaxX->setSuffix(QString(" %1").arg(accUnit));
    m_spinAccMaxY->setSuffix(QString(" %1").arg(accUnit));
    m_spinPositiveX->setSuffix(QString(" %1").arg(posUnit));
    m_spinPositiveY->setSuffix(QString(" %1").arg(posUnit));
    m_spinNegativeX->setSuffix(QString(" %1").arg(posUnit));
    m_spinNegativeY->setSuffix(QString(" %1").arg(posUnit));

    // 设置固定宽度以保持对齐
    m_spinVelMaxX->setMinimumWidth(120);
    m_spinVelMaxY->setMinimumWidth(120);
    m_spinAccMaxX->setMinimumWidth(120);
    m_spinAccMaxY->setMinimumWidth(120);
    m_spinPositiveX->setMinimumWidth(120);
    m_spinPositiveY->setMinimumWidth(120);
    m_spinNegativeX->setMinimumWidth(120);
    m_spinNegativeY->setMinimumWidth(120);
}

void AxisInitWidget::setDefaultParameters()
{
    // 设置轴参数初始化模块的默认参数值
    if (m_unitConverter->getCurrentUnitType() == UNIT_PULSE) {
        // pulse单位下的默认值
        m_spinVelMaxX->setValue(3000);     // 3000 pulse/ms
        m_spinVelMaxY->setValue(3000);     // 3000 pulse/ms
        m_spinAccMaxX->setValue(30);       // 30 pulse/ms^2
        m_spinAccMaxY->setValue(30);       // 30 pulse/ms^2
        m_spinPositiveX->setValue(1000000); // 1000000 pulse
        m_spinPositiveY->setValue(1000000); // 1000000 pulse
        m_spinNegativeX->setValue(0);      // 0 pulse
        m_spinNegativeY->setValue(0);      // 0 pulse
    } else {
        // mm单位下的默认值
        m_spinVelMaxX->setValue(3000);     // 3000 mm/s
        m_spinVelMaxY->setValue(3000);     // 3000 mm/s
        m_spinAccMaxX->setValue(30000);    // 30000 mm/s^2
        m_spinAccMaxY->setValue(30000);    // 30000 mm/s^2
        m_spinPositiveX->setValue(1000);   // 1000 mm
        m_spinPositiveY->setValue(1000);   // 1000 mm
        m_spinNegativeX->setValue(0);      // 0 mm
        m_spinNegativeY->setValue(0);      // 0 mm
    }
}

void AxisInitWidget::onUnitTypeChanged(UnitType type)
{
    // 当单位类型变化时更新界面显示
    updateUnitDisplay();

    // 转换轴参数初始化模块的参数值
    if (type == UNIT_PULSE) {
        // 从 mm 转换到 pulse
        // 速度保持不变，因为1mm/s = 1pulse/ms
        // 加速度需要转换，因为1mm/s^2 = 0.001pulse/ms^2
        m_spinAccMaxX->setValue(m_spinAccMaxX->value() * 0.001);
        m_spinAccMaxY->setValue(m_spinAccMaxY->value() * 0.001);
        // 位置需要转换，因为1mm = 1000pulse
        m_spinPositiveX->setValue(m_spinPositiveX->value() * 1000);
        m_spinPositiveY->setValue(m_spinPositiveY->value() * 1000);
        m_spinNegativeX->setValue(m_spinNegativeX->value() * 1000);
        m_spinNegativeY->setValue(m_spinNegativeY->value() * 1000);
    } else {
        // 从 pulse 转换到 mm
        // 速度保持不变，因为1pulse/ms = 1mm/s
        // 加速度需要转换，因为1pulse/ms^2 = 1000mm/s^2
        m_spinAccMaxX->setValue(m_spinAccMaxX->value() * 1000);
        m_spinAccMaxY->setValue(m_spinAccMaxY->value() * 1000);
        // 位置需要转换，因为1pulse = 0.001mm
        m_spinPositiveX->setValue(m_spinPositiveX->value() * 0.001);
        m_spinPositiveY->setValue(m_spinPositiveY->value() * 0.001);
        m_spinNegativeX->setValue(m_spinNegativeX->value() * 0.001);
        m_spinNegativeY->setValue(m_spinNegativeY->value() * 0.001);
    }
}

void AxisInitWidget::onSetButtonClicked()
{
    // 获取参数值
    short crd = static_cast<short>(m_comboCrd->currentIndex());

    // 创建参数数组
    short axisMap[2] = {static_cast<short>(m_spinAxisMapX->value()), static_cast<short>(m_spinAxisMapY->value())};
    short axisDir[2] = {static_cast<short>(m_spinAxisDirX->value()), static_cast<short>(m_spinAxisDirY->value())};
    int32_t velMax[2] = {static_cast<int32_t>(m_spinVelMaxX->value()), static_cast<int32_t>(m_spinVelMaxY->value())};
    int32_t accMax[2] = {static_cast<int32_t>(m_spinAccMaxX->value()), static_cast<int32_t>(m_spinAccMaxY->value())};
    int32_t positive[2] = {static_cast<int32_t>(m_spinPositiveX->value()), static_cast<int32_t>(m_spinPositiveY->value())};
    int32_t negative[2] = {static_cast<int32_t>(m_spinNegativeX->value()), static_cast<int32_t>(m_spinNegativeY->value())};

    // 如果当前单位是mm，需要转换为pulse再传给API
    if (m_unitConverter->getCurrentUnitType() == UNIT_MM) {
        // 速度保持不变，因为1mm/s = 1pulse/ms
        // 加速度需要转换，因为1mm/s^2 = 0.001pulse/ms^2
        accMax[0] = static_cast<int32_t>(m_unitConverter->mmPerSSquaredToPulsePerMsSquared(accMax[0]));
        accMax[1] = static_cast<int32_t>(m_unitConverter->mmPerSSquaredToPulsePerMsSquared(accMax[1]));
        // 位置需要转换，因为1mm = 1000pulse
        positive[0] = static_cast<int32_t>(m_unitConverter->mmToPulse(positive[0]));
        positive[1] = static_cast<int32_t>(m_unitConverter->mmToPulse(positive[1]));
        negative[0] = static_cast<int32_t>(m_unitConverter->mmToPulse(negative[0]));
        negative[1] = static_cast<int32_t>(m_unitConverter->mmToPulse(negative[1]));
    }

    // 获取API包装器实例
    AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();

    // 调用API设置轴参数
    if (wrapper && wrapper->isConnected()) {
        // 调用封装的API设置轴参数
        short result = wrapper->setAxisPrm(crd, axisMap, axisDir, velMax, accMax, positive, negative);

        // 处理结果
        if (result == 0) {
            // 成功
            emit apiStatusChanged(QString("设置坐标系%1轴参数初始化成功").arg(crd), true);
        } else {
            // 失败
            emit apiStatusChanged(QString("设置坐标系%1轴参数初始化错误，返回值为%2").arg(crd).arg(result), false);
        }
    } else {
        // 未连接
        QMessageBox::warning(this, "错误", "未连接到控制器，请先连接设备");
        emit apiStatusChanged("未连接到控制器，无法设置轴参数", false);
    }
}
