#ifndef TRAPWIDGET_H
#define TRAPWIDGET_H

#include <QWidget>
#include <QTimer>
//#include "includeMotion/admc_info.h"
#include "admc_api_wrapper.h"
#include "unitconverter.h"

namespace Ui {
class TrapWidget;
}

class TrapWidget : public QWidget
{
    Q_OBJECT

public:
    explicit TrapWidget(QWidget *parent = nullptr);
    ~TrapWidget();

signals:
    void apiStatusChanged(const QString& message, bool isSuccess);

private slots:
    // 轴点位模式槽函数
    void onAxisTrapModeSelected();
    void setAxisTrapParameters();
    void startAxisTrapMotion();

    // 坐标系点位模式槽函数
    void onCrdTrapModeSelected();
    void setCrdTrapParameters();
    void startCrdTrapMotion();

    // 通用槽函数
    void onTabChanged(int index);
    void onAxisChanged(int index);
    void onCrdChanged(int index);
    void updateStatus(const QString &message);

    // 单位转换相关槽函数
    void onUnitTypeChanged(UnitType type);
    void onUnitSwitchClicked();

private:
    // 初始化UI和连接信号槽
    void initUI();
    void connectSignals();

    // 更新UI状态
    void updateAxisTrapUI();
    void updateCrdTrapUI();

    // 更新单位显示
    void updateUnitDisplay();
    // 设置默认参数值
    void setDefaultParameters();

    // 注意：以下方法已废弃，因为新接口不再使用结构体
    // TTrapPrm getAxisTrapParameters();
    // TTrapPrm getCrdTrapParameters();

    Ui::TrapWidget *ui;
    QTimer *m_updateTimer;
    int m_currentAxis;  // 当前选择的轴
    int m_currentCrd;   // 当前选择的坐标系

    // 点位运动相关
    bool m_isAxisTrapMode;  // 是否为轴点位模式
    // 注意：以下成员变量已废弃，因为新接口不再使用结构体
    // TTrapPrm m_axisTrapPrm; // 轴点位参数
    // TTrapPrm m_crdTrapPrm;  // 坐标系点位参数

    // API包装器实例
    AdmcApiWrapper* m_apiWrapper;

    // 单位转换器
    UnitConverter* m_unitConverter;
};

#endif // TRAPWIDGET_H
