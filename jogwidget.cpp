#include "jogwidget.h"
#include "ui_jogwidget.h"
#include <QMessageBox>
#include <QRandomGenerator>
#include <QDebug>
#include <QDebug>

JogWidget::JogWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::JogWidget),
    m_updateTimer(new QTimer(this)),
    m_currentAxis(0),
    m_currentCrd(0),
    m_isJogging(false),
    m_jogDirection(0),
    m_isAxisTrapMode(true),
    m_apiWrapper(AdmcApiWrapper::getInstance())
{
    ui->setupUi(this);

    // 初始化UI和连接信号槽
    initUI();
    connectSignals();

    // 设置定时器，用于更新状态
    connect(m_updateTimer, &QTimer::timeout, [this]() {
        // 获取当前轴位置
        if (m_apiWrapper->isConnected()) {
            // 轴点位模式下更新轴位置
            if (ui->tabWidget->currentIndex() == 0) {
                double position = 0.0;
                short result = m_apiWrapper->getAxisPosition(m_currentAxis, position);
                if (result == 0) {
                    ui->lcdAxisPosition->display(position);
                }
            }
            // 坐标系点位模式下更新坐标系位置
            else if (ui->tabWidget->currentIndex() == 1) {
                double pos[2] = {0.0, 0.0};
                short result = m_apiWrapper->getCrdPos(m_currentCrd, pos);
                if (result == 0) {
                    ui->lcdCrdPositionX->display(pos[0]);
                    ui->lcdCrdPositionY->display(pos[1]);
                }
            }
        }
        // 更新单位显示
        UnitType currentUnit = UnitConverter::getInstance()->getCurrentUnitType();
        if (currentUnit == UNIT_PULSE) {
            ui->labelVelMaxUnit->setText("pulse/ms");
            ui->labelAccUnit->setText("pulse/ms²");
            ui->labelCrdVelMaxUnit->setText("pulse/ms");
            ui->labelCrdAccUnit->setText("pulse/ms²");
        } else {
            ui->labelVelMaxUnit->setText("mm/s");
            ui->labelAccUnit->setText("mm/s²");
            ui->labelCrdVelMaxUnit->setText("mm/s");
            ui->labelCrdAccUnit->setText("mm/s²");
        }
    });
    m_updateTimer->start(100); // 100ms更新一次

    // 初始化状态
    updateStatus("就绪");
}

JogWidget::~JogWidget()
{
    m_updateTimer->stop();
    delete ui;
}

void JogWidget::initUI()
{
    // 初始化轴选择下拉框
    for (int i = 0; i < 8; ++i) {
        ui->comboAxis->addItem(QString("轴 %1").arg(i));
    }

    // 初始化坐标系选择下拉框
    for (int i = 0; i < 5; ++i) {
        ui->comboCrd->addItem(QString("坐标系 %1").arg(i));
        ui->comboCrdSelect->addItem(QString("坐标系 %1").arg(i));
    }

    // 默认选择轴点位模式
    ui->tabWidget->setCurrentIndex(0);
    m_isAxisTrapMode = true;
}

void JogWidget::connectSignals()
{
    // 连接Tab切换信号
    connect(ui->tabWidget, &QTabWidget::currentChanged, this, &JogWidget::onTabChanged);

    // 轴点位模式下的信号连接
    connect(ui->comboAxis, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &JogWidget::onAxisChanged);
    connect(ui->comboCrd, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &JogWidget::onCrdChanged);
    connect(ui->btnSetAxisTrapParams, &QPushButton::clicked, this, &JogWidget::setAxisTrapParameters);
    connect(ui->btnStartAxisTrap, &QPushButton::clicked, this, &JogWidget::startAxisTrapMotion);

    // 坐标系点位模式下的信号连接
    connect(ui->comboCrdSelect, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &JogWidget::onCrdChanged);
    connect(ui->btnSetCrdTrapParams, &QPushButton::clicked, this, &JogWidget::setCrdTrapParameters);
    connect(ui->btnStartCrdTrap, &QPushButton::clicked, this, &JogWidget::startCrdTrapMotion);
}

void JogWidget::onTabChanged(int index)
{
    // 切换模式
    m_isAxisTrapMode = (index == 0);

    // 更新状态
    if (m_isAxisTrapMode) {
        updateStatus(QString("已切换到轴点位模式"));
    } else {
        updateStatus(QString("已切换到坐标系点位模式"));
    }
}

void JogWidget::onAxisChanged(int index)
{
    m_currentAxis = index;
    updateStatus(QString("当前选择轴: %1").arg(index));
}

void JogWidget::onCrdChanged(int index)
{
    m_currentCrd = index;
    updateStatus(QString("当前选择坐标系: %1").arg(index));
}

void JogWidget::setAxisTrapParameters()
{
    if (!m_apiWrapper->isConnected()) {
        QMessageBox::warning(this, "错误", "设备未连接，无法设置参数");
        return;
    }

    // 从界面获取参数
    double posTarget = ui->spinPosTarget->value();
    double velMax = ui->spinVelMax->value();
    double acc = ui->spinAcc->value();
    short rat = ui->spinRat->value();

    // 设置轴点位模式 - 新接口只需要轴号
    short result = m_apiWrapper->setAxisTrapMode(m_currentAxis);
    if (result != 0) {
        QMessageBox::warning(this, "错误",
                           QString("设置轴点位模式失败，错误码: %1").arg(result));
        return;
    }

    // 设置轴点位参数 - 新接口直接传递参数
    result = m_apiWrapper->setAxisTrapParameters(m_currentAxis, posTarget, velMax, acc, rat);
    if (result != 0) {
        QMessageBox::warning(this, "错误",
                           QString("设置轴点位参数失败，错误码: %1").arg(result));
        return;
    }

    updateStatus(QString("轴%1的点位参数设置成功").arg(m_currentAxis));
}

void JogWidget::updateStatus(const QString &message)
{
    ui->labelStatus->setText(message);
}

void JogWidget::startAxisTrapMotion()
{
    if (!m_apiWrapper->isConnected()) {
        QMessageBox::warning(this, "错误", "设备未连接，无法启动运动");
        return;
    }

    // 启动轴点位运动 - 新接口使用轴号
    short result = m_apiWrapper->axisTrapUpdate(m_currentAxis);
    if (result != 0) {
        QMessageBox::warning(this, "错误",
                           QString("启动轴点位运动失败，错误码: %1").arg(result));
        return;
    }

    double posTarget = ui->spinPosTarget->value();
    updateStatus(QString("轴%1的点位运动已启动，目标位置: %2")
               .arg(m_currentAxis)
               .arg(posTarget));
}

void JogWidget::setCrdTrapParameters()
{
    if (!m_apiWrapper->isConnected()) {
        QMessageBox::warning(this, "错误", "设备未连接，无法设置参数");
        return;
    }

    // 从界面获取参数
    double posTargetX = ui->spinPosTargetX->value();
    double posTargetY = ui->spinPosTargetY->value();
    double velMax = ui->spinCrdVelMax->value();
    double acc = ui->spinCrdAcc->value();
    short rat = ui->spinCrdRat->value();

    // 设置坐标系点位模式
    short result = m_apiWrapper->setCrdTrapMode(m_currentCrd);
    if (result != 0) {
        QMessageBox::warning(this, "错误",
                           QString("设置坐标系点位模式失败，错误码: %1").arg(result));
        return;
    }

    // 获取当前坐标系位置作为起始位置
    double startPos[2] = {0.0, 0.0};
    if (m_apiWrapper->isConnected()) {
        short posResult = m_apiWrapper->getCrdPos(m_currentCrd, startPos);
        if (posResult != 0) {
            qDebug() << "Failed to get coordinate system position, using default start position";
        }
    }

    // 设置目标位置
    double posTarget[2] = {posTargetX, posTargetY};

    // 设置坐标系点位参数 - 新接口直接传递参数
    result = m_apiWrapper->setCrdTrapParameters(m_currentCrd, startPos, posTarget, velMax, acc, rat);
    if (result != 0) {
        QMessageBox::warning(this, "错误",
                           QString("设置坐标系点位参数失败，错误码: %1").arg(result));
        return;
    }

    updateStatus(QString("坐标系%1的点位参数设置成功").arg(m_currentCrd));
}

void JogWidget::startCrdTrapMotion()
{
    if (!m_apiWrapper->isConnected()) {
        QMessageBox::warning(this, "错误", "设备未连接，无法启动运动");
        return;
    }

    // 启动坐标系点位运动
    short result = m_apiWrapper->crdTrapUpdate(m_currentCrd);
    if (result != 0) {
        QMessageBox::warning(this, "错误",
                           QString("启动坐标系点位运动失败，错误码: %1").arg(result));
        return;
    }

    double posTargetX = ui->spinPosTargetX->value();
    double posTargetY = ui->spinPosTargetY->value();
    updateStatus(QString("坐标系%1的点位运动已启动，目标位置: X=%2, Y=%3")
               .arg(m_currentCrd)
               .arg(posTargetX)
               .arg(posTargetY));
}

// 注意：这些方法已彻底删除，因为新接口不再使用结构体

// 实现缺失的槽函数
void JogWidget::startJogPositive()
{
    if (m_isJogging) {
        return;
    }

    m_isJogging = true;
    m_jogDirection = 1;

    // 在实际应用中，这里应该调用底层API执行点动操作
    if (m_apiWrapper->isConnected()) {
        m_apiWrapper->jogUpdate(m_currentAxis, 1); // 1表示正向
    }

    QString status = QString("轴%1正在正向点动").arg(m_currentAxis);
    updateStatus(status);
}

void JogWidget::startJogNegative()
{
    if (m_isJogging) {
        return;
    }

    m_isJogging = true;
    m_jogDirection = -1;

    // 在实际应用中，这里应该调用底层API执行点动操作
    if (m_apiWrapper->isConnected()) {
        m_apiWrapper->jogUpdate(m_currentAxis, -1); // -1表示负向
    }

    QString status = QString("轴%1正在负向点动").arg(m_currentAxis);
    updateStatus(status);
}

void JogWidget::stopJog()
{
    if (!m_isJogging) {
        return;
    }

    // 在实际应用中，这里应该调用底层API停止点动操作
    if (m_apiWrapper->isConnected()) {
        m_apiWrapper->jogUpdate(m_currentAxis, 0); // 0表示停止
    }

    m_isJogging = false;
    m_jogDirection = 0;

    QString status = QString("轴%1已停止点动").arg(m_currentAxis);
    updateStatus(status);
}

void JogWidget::updateJogSpeed()
{
    // 在实际应用中，这里应该调用底层API更新点动速度
    updateStatus("速度已更新");
}

void JogWidget::updateJogParams()
{
    // 在实际应用中，这里应该从底层API获取点动参数
    updateStatus("参数已更新");
}

void JogWidget::onAxisTrapModeSelected()
{
    m_isAxisTrapMode = true;
    updateStatus("已选择轴点位模式");
}

void JogWidget::onCrdTrapModeSelected()
{
    m_isAxisTrapMode = false;
    updateStatus("已选择坐标系点位模式");
}
