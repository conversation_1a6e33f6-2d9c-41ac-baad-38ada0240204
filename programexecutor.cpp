#include "programexecutor.h"
// Command 类已经在 programexecutor.h 中定义
#include "admc_api_wrapper.h" // Include API wrapper for hardware interaction
//#include "includeMotion/adconfig.h" // Include adconfig for error codes
#include "unitconverter.h" // Include unit converter for unit conversion

#include <QDebug> // Using QDebug for logging in the executor (can be replaced with a custom logger)

// --- Stub implementations for Command methods (These should ideally be in separate .cpp files) ---
// Moved here temporarily for compilation of ProgramExecutor

ExecutionStatus MotionCommand::execute(ExecutionContext& context) {
    // 如果收到停止请求，直接终止指令执行
    if (context.stopRequested.load()) {
        qDebug() << "MotionCommand::execute() - 收到停止请求，终止指令执行";
        currentStep = MotionStep::IDLE;
        return ExecutionStatus::ERROR_HALT;
    }

    bool success = false;
    switch (currentStep) {
        case MotionStep::IDLE:
            qDebug() << "MotionCommand::execute() - 开始执行运动指令，设置运动模式";
            success = apiSetMode();
            if (success) {
                currentStep = MotionStep::SET_PARAMS;
                return ExecutionStatus::EXECUTING_STEP;
            } else {
                qWarning() << "MotionCommand::execute() - 设置运动模式失败";
            }
            break;

        case MotionStep::SET_MODE:
            // 这个状态已经被合并到IDLE中处理，为了兼容性保留这个分支
            qDebug() << "MotionCommand::execute() - SET_MODE状态已废弃，切换到IDLE状态";
            currentStep = MotionStep::IDLE;
            return ExecutionStatus::EXECUTING_STEP;
            break;

        case MotionStep::SET_PARAMS:
            qDebug() << "MotionCommand::execute() - 设置运动参数";
            success = apiSetParams();
            if (success) {
                currentStep = MotionStep::START_MOVE;
                return ExecutionStatus::EXECUTING_STEP;
            } else {
                qWarning() << "MotionCommand::execute() - 设置运动参数失败";
            }
            break;

        case MotionStep::START_MOVE:
            qDebug() << "MotionCommand::execute() - 启动运动";
            success = apiStartMove();
            if (success) {
                qDebug() << "MotionCommand::execute() - 运动启动成功，进入异步等待";
                currentStep = MotionStep::IDLE; // 重置步骤，下次执行从头开始
                context.isMotionActive.store(true); // 设置运动标志，进入异步等待
                return ExecutionStatus::STARTED_ASYNC_WAIT;
            } else {
                qWarning() << "MotionCommand::execute() - 启动运动失败";
            }
            break;
    }

    // 如果执行到这里，说明出错了
    qWarning() << "MotionCommand::execute() - 执行运动指令失败，重置状态";
    currentStep = MotionStep::IDLE; // 重置步骤
    return ExecutionStatus::ERROR_HALT; // 返回错误状态，终止程序执行
}

void MotionCommand::saveParameters(std::ostream& os) const {
    os << crd;
}

bool MotionCommand::loadParameters(std::istream& is) {
    return bool(is >> crd);
}

std::string LinearMoveCommand::getDescription() const {
    return "直线插补 (crd:" + std::to_string(crd) + ", X:" + std::to_string(targetPos[0]) + ", Y:" + std::to_string(targetPos[1]) + ", Vel:" + std::to_string(speed) + ", Acc:" + std::to_string(acc) + ")";
}

void LinearMoveCommand::saveParameters(std::ostream& os) const {
    MotionCommand::saveParameters(os);
    os << " " << targetPos[0] << " " << targetPos[1] << " " << targetPos[2]
       << " " << speed << " " << acc;
}

bool LinearMoveCommand::loadParameters(std::istream& is) {
    if (!MotionCommand::loadParameters(is)) return false;
    return bool(is >> targetPos[0] >> targetPos[1] >> targetPos[2] >> speed >> acc);
}

bool LinearMoveCommand::apiSetMode() {
    // 获取API包装器实例
    AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();
    if (!wrapper || !wrapper->isConnected()) {
        qWarning() << "LinearMoveCommand::apiSetMode() - 设备未连接";
        return false;
    }

    // 设置坐标系参数 - 新接口直接传递参数
    double synVelMax = 1000.0;  // 默认最大速度
    double synAccMax = 10.0;    // 默认最大加速度

    qDebug() << "LinearMoveCommand::apiSetMode() - 设置坐标系" << crd << "参数";
    short result = wrapper->setCrdPrm(static_cast<short>(crd), synVelMax, synAccMax);

    if (result != 0) {
        qWarning() << "LinearMoveCommand::apiSetMode() - 设置坐标系参数失败，错误码:" << result;
        return false;
    }

    return true;
}

bool LinearMoveCommand::apiSetParams() {
    // 获取API包装器实例
    AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();
    if (!wrapper || !wrapper->isConnected()) {
        qWarning() << "LinearMoveCommand::apiSetParams() - 设备未连接";
        return false;
    }

    // 获取单位转换器实例
    UnitConverter* unitConverter = UnitConverter::getInstance();

    // 准备参数
    int32_t x = static_cast<int32_t>(targetPos[0]);
    int32_t y = static_cast<int32_t>(targetPos[1]);
    double synVel = speed;
    double synAcc = acc;
    double velEnd = 0.0; // 终点速度默认为0

    // 如果当前单位是mm，需要转换为pulse再传给API
    if (unitConverter->getCurrentUnitType() == UNIT_MM) {
        // 位置需要转换，因为1mm = 1000pulse
        x = static_cast<int32_t>(unitConverter->mmToPulse(targetPos[0]));
        y = static_cast<int32_t>(unitConverter->mmToPulse(targetPos[1]));
        // 速度保持不变，因为1mm/s = 1pulse/ms
        // 加速度需要转换，因为1mm/s^2 = 0.001pulse/ms^2
        synAcc = unitConverter->mmPerSSquaredToPulsePerMsSquared(acc);
    }

    qDebug() << "LinearMoveCommand::apiSetParams() - 设置直线插补参数 - 坐标系:" << crd
             << "X:" << x << "Y:" << y << "速度:" << synVel << "加速度:" << synAcc;

    // 调用API执行直线插补
    short result = wrapper->ln(static_cast<short>(crd), x, y, synVel, synAcc, velEnd);

    if (result != 0) {
        qWarning() << "LinearMoveCommand::apiSetParams() - 设置直线插补参数失败，错误码:" << result;
        return false;
    }

    return true;
}

bool LinearMoveCommand::apiStartMove() {
    // 获取API包装器实例
    AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();
    if (!wrapper || !wrapper->isConnected()) {
        qWarning() << "LinearMoveCommand::apiStartMove() - 设备未连接";
        return false;
    }

    qDebug() << "LinearMoveCommand::apiStartMove() - 启动坐标系" << crd << "插补运动";

    // 调用API启动坐标系插补运动
    short result = wrapper->crdStart(static_cast<short>(crd));

    if (result != 0) {
        qWarning() << "LinearMoveCommand::apiStartMove() - 启动坐标系插补运动失败，错误码:" << result;
        return false;
    }

    return true;
}


std::string DelayCommand::getDescription() const {
    return "延时 " + std::to_string(milliseconds) + " ms";
}

ExecutionStatus DelayCommand::execute(ExecutionContext& context) {
    // 如果收到停止请求，直接结束延时
    if (context.stopRequested.load()) {
        qDebug() << "DelayCommand::execute() - 收到停止请求，终止延时";
        waiting = false;
        checkingMotion = true;
        currentStep = DelayStep::CHECK_MOTION;
        return ExecutionStatus::COMPLETED_NEXT;
    }



    // 根据当前步骤执行不同的操作
    switch (currentStep) {
        case DelayStep::CHECK_MOTION:
            // 检查前序运动是否完成
            if (context.isMotionActive.load() || context.isStabilizing.load()) {
                qDebug() << "DelayCommand::execute() - 前序运动未完成或未稳定，等待...";
                // 前序运动未完成，等待
                std::this_thread::sleep_for(std::chrono::milliseconds(10)); // 短暂停，减少CPU使用
                return ExecutionStatus::EXECUTING_STEP;
            } else {
                qDebug() << "DelayCommand::execute() - 前序运动已完成并稳定，开始延时";
                // 前序运动已完成，进入延时状态
                currentStep = DelayStep::START_DELAY;
                return ExecutionStatus::EXECUTING_STEP;
            }
            break;

        case DelayStep::START_DELAY:
            // 开始延时
            startTime = std::chrono::steady_clock::now();
            waiting = true;

            // 如果延时时间小于等于0，直接完成
            if (milliseconds <= 0) {
                qDebug() << "DelayCommand::execute() - 延时时间小于等于0，直接完成";
                waiting = false;
                checkingMotion = true;
                currentStep = DelayStep::CHECK_MOTION; // 重置状态为初始状态
                return ExecutionStatus::COMPLETED_NEXT;
            }

            qDebug() << "DelayCommand::execute() - 开始延时" << milliseconds << "ms";
            currentStep = DelayStep::WAIT_DELAY;
            return ExecutionStatus::EXECUTING_STEP;

        case DelayStep::WAIT_DELAY:
            // 等待延时完成
            if (waiting) {
                auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                    std::chrono::steady_clock::now() - startTime).count();

                if (elapsed >= milliseconds) {
                    qDebug() << "DelayCommand::execute() - 延时完成，已等待" << elapsed << "ms";
                    waiting = false;
                    checkingMotion = true;
                    currentStep = DelayStep::CHECK_MOTION; // 重置状态为初始状态
                    return ExecutionStatus::COMPLETED_NEXT;
                } else {
                    // 延时未完成，继续等待
                    if (elapsed % 100 == 0) { // 每100ms输出一次日志，减少日志量
                        qDebug() << "DelayCommand::execute() - 延时中，已等待" << elapsed << "ms，总时间" << milliseconds << "ms";
                    }
                    std::this_thread::sleep_for(std::chrono::milliseconds(10)); // 短暂停，减少CPU使用
                    return ExecutionStatus::EXECUTING_STEP;
                }
            }
            break;
    }

    // 如果执行到这里，说明出错了
    qWarning() << "DelayCommand::execute() - 延时指令执行异常，重置状态";
    waiting = false;
    checkingMotion = true;
    currentStep = DelayStep::CHECK_MOTION;
    return ExecutionStatus::ERROR_HALT;
}

void DelayCommand::saveParameters(std::ostream& os) const {
    os << milliseconds;
}

bool DelayCommand::loadParameters(std::istream& is) {
    return bool(is >> milliseconds);
}

// --- Stub implementation for ErrorInfo constructor ---
// 已经在头文件中实现了构造函数，这里不需要再实现

// --- 实现 ExecutionContext 方法 ---

// 检查运动是否完成
// 返回值：true - 运动完成，false - 运动未完成或出错
// 注意：这个方法检查运动状态和到位状态，不处理稳定时间
// 如果需要等待稳定时间，请使用 checkMotionCompleteAndStable 方法
bool ExecutionContext::checkMotionComplete() {
    AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();
    if (!wrapper || !wrapper->isConnected()) {
        qWarning() << "ExecutionContext::checkMotionComplete() - 设备未连接";
        return false;
    }

    // 如果收到停止请求，直接返回完成
    if (stopRequested.load()) {
        qDebug() << "ExecutionContext::checkMotionComplete() - 收到停止请求，强制结束运动等待";
        return true;
    }

    // 检查坐标系0和1的轴状态
    bool allAxesComplete = true;

    // 检查坐标系0的轴状态（轴0和轴1）
    for (short axis = 0; axis < 2; ++axis) {
        short status = 0;
        short result = wrapper->getAxisStatus(axis, status);

        if (result != 0) {
            qWarning() << "ExecutionContext::checkMotionComplete() - 获取轴" << axis << "状态失败，错误码:" << result;
            return false;
        }

        // 检查运动中状态位（MOV，第5位）和到位状态位（INP，第4位）
        bool isMoving = (status & (1 << 5)) != 0;
        bool isInPosition = (status & (1 << 4)) != 0;

        // 只有当轴不在运动中且已到位时，才认为该轴完成
        if (isMoving || !isInPosition) {
            allAxesComplete = false;
            if (isMoving) {
                qDebug() << "ExecutionContext::checkMotionComplete() - 轴" << axis << "仍在运动中";
            } else if (!isInPosition) {
                qDebug() << "ExecutionContext::checkMotionComplete() - 轴" << axis << "未到位";
            }
            break;
        }
    }

    // 如果坐标系0的轴已经停止并到位，检查坐标系1的轴
    if (allAxesComplete) {
        // 检查坐标系1的轴状态（轴2和轴3）
        for (short axis = 2; axis < 4; ++axis) {
            short status = 0;
            short result = wrapper->getAxisStatus(axis, status);

            if (result != 0) {
                qWarning() << "ExecutionContext::checkMotionComplete() - 获取轴" << axis << "状态失败，错误码:" << result;
                return false;
            }

            // 检查运动中状态位（MOV，第5位）和到位状态位（INP，第4位）
            bool isMoving = (status & (1 << 5)) != 0;
            bool isInPosition = (status & (1 << 4)) != 0;

            // 只有当轴不在运动中且已到位时，才认为该轴完成
            if (isMoving || !isInPosition) {
                allAxesComplete = false;
                if (isMoving) {
                    qDebug() << "ExecutionContext::checkMotionComplete() - 轴" << axis << "仍在运动中";
                } else if (!isInPosition) {
                    qDebug() << "ExecutionContext::checkMotionComplete() - 轴" << axis << "未到位";
                }
                break;
            }
        }
    }

    // 如果所有轴都完成运动并到位，记录完成时间
    if (allAxesComplete && !isStabilizing.load()) {
        qDebug() << "ExecutionContext::checkMotionComplete() - 运动完成并到位，进入稳定期";
        setMotionCompleteTime();
        isStabilizing.store(true);
    }

    return allAxesComplete;
}

// 检查运动是否完成并稳定
// 返回值：true - 运动完成并稳定，false - 运动未完成或未稳定
bool ExecutionContext::checkMotionCompleteAndStable() {
    // 如果收到停止请求，直接返回完成
    if (stopRequested.load()) {
        qDebug() << "ExecutionContext::checkMotionCompleteAndStable() - 收到停止请求，强制结束运动等待";
        return true;
    }

    // 先检查运动是否完成
    bool motionComplete = checkMotionComplete();

    // 如果运动完成且处于稳定期，检查是否过了稳定时间
    if (motionComplete && isStabilizing.load()) {
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - motionCompleteTime).count();

        if (elapsed >= STABILIZATION_TIME_MS) {
            qDebug() << "ExecutionContext::checkMotionCompleteAndStable() - 运动完成并稳定，稳定时间:" << elapsed << "ms";
            isStabilizing.store(false);
            return true;
        } else {
            qDebug() << "ExecutionContext::checkMotionCompleteAndStable() - 运动完成，等待稳定，已等待:" << elapsed << "ms";
            return false;
        }
    }

    return false; // 运动未完成
}

// 重置运动状态
void ExecutionContext::resetMotionState() {
    isMotionActive.store(false);
    isStabilizing.store(false);
    qDebug() << "ExecutionContext::resetMotionState() - 重置运动状态";
}

// 设置运动完成时间点
void ExecutionContext::setMotionCompleteTime() {
    motionCompleteTime = std::chrono::steady_clock::now();
}

// --- Factory function for creating commands by type (needs proper implementation) ---
// --- 轴点位运动指令实现 ---
std::string AxisTrapCommand::getDescription() const {
    return "轴点位运动 (axis:" + std::to_string(axis) +
           ", pos:" + std::to_string(posTarget) + ", vel:" + std::to_string(velMax) +
           ", acc:" + std::to_string(acc) + ", rat:" + std::to_string(rat) + ")";
}

void AxisTrapCommand::saveParameters(std::ostream& os) const {
    MotionCommand::saveParameters(os);
    os << " " << axis << " " << posTarget << " " << velMax << " " << acc << " " << rat;
}

bool AxisTrapCommand::loadParameters(std::istream& is) {
    if (!MotionCommand::loadParameters(is)) return false;
    return bool(is >> axis >> posTarget >> velMax >> acc >> rat);
}

bool AxisTrapCommand::apiSetMode() {
    // 获取API包装器实例
    AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();
    if (!wrapper || !wrapper->isConnected()) {
        qWarning() << "AxisTrapCommand::apiSetMode() - 设备未连接";
        return false;
    }

    // 新接口只需要轴号
    short axisUI = static_cast<short>(axis);

    qDebug() << "AxisTrapCommand::apiSetMode() - 设置轴点位模式 - 轴:" << axisUI;
    short result = wrapper->setAxisTrapMode(axisUI);

    if (result != 0) {
        qWarning() << "AxisTrapCommand::apiSetMode() - 设置轴点位模式失败，错误码:" << result;
        return false;
    }

    return true;
}

bool AxisTrapCommand::apiSetParams() {
    // 获取API包装器实例
    AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();
    if (!wrapper || !wrapper->isConnected()) {
        qWarning() << "AxisTrapCommand::apiSetParams() - 设备未连接";
        return false;
    }

    // 获取单位转换器实例
    UnitConverter* unitConverter = UnitConverter::getInstance();

    // 获取轴号
    short axisUI = static_cast<short>(axis);

    // 获取当前轴位置作为起始位置
    double currentPos = 0.0;
    if (wrapper->isConnected()) {
        short result = wrapper->getAxisPosition(axisUI, currentPos);
        if (result != 0) {
            qWarning() << "AxisTrapCommand::apiSetParams() - 获取轴" << axisUI << "位置失败，错误码:" << result;
            // 如果获取失败，使用默认值0
        }
    }

    // 设置参数
    double pos = posTarget;
    double vel = velMax;
    double acceleration = acc;

    // 如果当前单位是mm，需要转换为pulse再传给API
    if (unitConverter->getCurrentUnitType() == UNIT_MM) {
        // 位置需要转换，因为1mm = 1000pulse
        pos = unitConverter->mmToPulse(posTarget);
        // 速度保持不变，因为1mm/s = 1pulse/ms
        // 加速度需要转换，因为1mm/s^2 = 0.001pulse/ms^2
        acceleration = unitConverter->mmPerSSquaredToPulsePerMsSquared(acc);
    }

    // 计算增量位置（目标位置 - 当前位置）
    double incrPos = pos - currentPos;

    qDebug() << "AxisTrapCommand::apiSetParams() - 设置轴点位参数 - 轴:" << axisUI
             << "增量位置:" << incrPos << "速度:" << vel << "加速度:" << acceleration << "加速度比例:" << rat;

    // 调用API设置轴点位参数 - 新接口直接传递参数
    short result = wrapper->setAxisTrapParameters(axisUI, incrPos, vel, acceleration, rat);

    if (result != 0) {
        qWarning() << "AxisTrapCommand::apiSetParams() - 设置轴点位参数失败，错误码:" << result;
        return false;
    }

    return true;
}

bool AxisTrapCommand::apiStartMove() {
    // 获取API包装器实例
    AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();
    if (!wrapper || !wrapper->isConnected()) {
        qWarning() << "AxisTrapCommand::apiStartMove() - 设备未连接";
        return false;
    }

    // 获取轴号
    short axisUI = static_cast<short>(axis);

    qDebug() << "AxisTrapCommand::apiStartMove() - 启动轴点位运动 - 轴:" << axisUI;

    // 调用API启动轴点位运动 - 新接口使用轴号
    short result = wrapper->axisTrapUpdate(axisUI);

    if (result != 0) {
        qWarning() << "AxisTrapCommand::apiStartMove() - 启动轴点位运动失败，错误码:" << result;
        return false;
    }

    return true;
}

bool ProgramExecutor::createCommandByType(const std::string& type, std::unique_ptr<Command>& cmd) {
    if (type == "LINEAR") {
        cmd = std::unique_ptr<LinearMoveCommand>(new LinearMoveCommand()); // Use default constructor for loading
        return true;
    } else if (type == "DELAY") {
        cmd = std::unique_ptr<DelayCommand>(new DelayCommand()); // Use default constructor for loading
        return true;
    } else if (type == "AXIS_TRAP") {
        cmd = std::unique_ptr<AxisTrapCommand>(new AxisTrapCommand()); // Use default constructor for loading
        return true;
    }
    // Add cases for other command types
    return false;
}


// --- ProgramExecutor Implementations ---

ProgramExecutor::ProgramExecutor(StatusUpdateCallback statusCb, ErrorCallback errorCb)
    : context(program), statusUpdateCallback(statusCb), errorCallback(errorCb)
{
    // Start the execution thread
    executionThread = std::thread(&ProgramExecutor::runLoop, this);
}

ProgramExecutor::~ProgramExecutor()
{
    // Ensure the thread is stopped and joined
    requestStop();
    if (executionThread.joinable()) {
        executionThread.join();
    }
}

bool ProgramExecutor::loadProgramFromFile(const std::string& filePath) {
    // This should ideally be handled by the UI layer or a dedicated file handler
    // For now, a basic stub
    qDebug() << "Executor loading program from (stub):" << QString::fromStdString(filePath);
    return true; // Assume success for stub
}

bool ProgramExecutor::saveProgramToFile(const std::string& filePath) {
     // This should ideally be handled by the UI layer or a dedicated file handler
    // For now, a basic stub
    qDebug() << "Executor saving program to (stub):" << QString::fromStdString(filePath);
    return true; // Assume success for stub
}

void ProgramExecutor::setProgram(std::vector<std::unique_ptr<Command>> newProgram) {
    std::lock_guard<std::mutex> lock(stateMutex);
    program = std::move(newProgram);
    context.programCounter = 0; // Reset PC
}

void ProgramExecutor::start(ExecutionMode mode) {
    std::lock_guard<std::mutex> lock(stateMutex);
    if (currentState == State::STOPPED || currentState == State::HALTED_ERROR) {
        currentMode = mode;
        currentState = State::RUNNING_AUTO;
        stopRequested = false;
        context.programCounter = 0; // Start from the beginning
        context.isMotionActive = false; // Reset motion state
        notifyUI(currentState, context.programCounter);
        stepCv.notify_one(); // Notify the runLoop to start/resume
    }
}

void ProgramExecutor::requestStop() {
    qDebug() << "ProgramExecutor::requestStop() - 请求停止程序执行";
    stopRequested = true;

    // 立即将状态设置为STOPPED，不等待执行线程结束
    std::lock_guard<std::mutex> lock(stateMutex);
    currentState = State::STOPPED;

    // 通知UI状态已更新
    notifyUI(currentState, context.programCounter);

    // 如果有运动正在进行，将强制结束等待
    // 这会导致checkMotionCompleteAndStable直接返回true
    context.stopRequested.store(true);

    stepCv.notify_one(); // 通知runLoop检查停止请求
}

ProgramExecutor::State ProgramExecutor::getCurrentState() const {
    return currentState;
}

size_t ProgramExecutor::getCurrentLine() const {
    return context.programCounter;
}

ProgramExecutor::ExecutionMode ProgramExecutor::getCurrentMode() const {
    return currentMode;
}

void ProgramExecutor::setExecutionMode(ExecutionMode mode) {
    std::lock_guard<std::mutex> lock(stateMutex);

    // 更新执行模式
    currentMode = mode;

    // 通知UI更新
    notifyUI(currentState, context.programCounter);
}

void ProgramExecutor::runLoop() {
    while (!stopRequested) {
        std::unique_lock<std::mutex> lock(stateMutex);

        // 等待条件
        stepCv.wait(lock, [&]{
            return stopRequested ||
                   (currentState == State::RUNNING_AUTO) ||
                   (currentState == State::HALTED_ERROR) ||
                   (currentState == State::STOPPED);
        });

        // 首先检查停止请求
        if (stopRequested) {
            qDebug() << "ProgramExecutor::runLoop() - 收到停止请求，退出执行循环";
            break;
        }

        // 检查错误状态
        if (currentState == State::HALTED_ERROR || currentState == State::STOPPED) {
            continue;
        }

        // 检查运动完成状态
        if (context.isMotionActive.load()) {
            context.stopRequested.store(stopRequested.load());

            if (context.checkMotionCompleteAndStable()) {
                qDebug() << "运动完成并稳定，继续执行";
                context.resetMotionState();
                // 运动完成后通知UI更新状态
                notifyUI(currentState, context.programCounter);
            } else {
                // 即使运动未完成，也定期通知UI更新状态
                static int updateCounter = 0;
                if (++updateCounter % 50 == 0) { // 每50次循环更新一次UI
                    notifyUI(currentState, context.programCounter);
                    updateCounter = 0;
                }

                lock.unlock();
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
                continue;
            }
        }

        // 执行程序逻辑
        if (context.programCounter < program.size()) {
            Command* currentCommand = program[context.programCounter].get();
            // 执行指令前通知UI更新当前行
            notifyUI(currentState, context.programCounter);

            ExecutionStatus status = currentCommand->execute(context);

            // 检查是否在执行过程中收到停止请求
            if (stopRequested) {
                qDebug() << "指令执行过程中收到停止请求";
                break;
            }

            switch (status) {
                case ExecutionStatus::COMPLETED_NEXT:
                    context.programCounter++;
                    // 程序计数器变化后立即通知UI更新
                    notifyUI(currentState, context.programCounter);
                    // 添加短暂延时，确保UI有足够时间更新
                    lock.unlock();
                    std::this_thread::sleep_for(std::chrono::milliseconds(50));
                    lock.lock();
                    break;
                case ExecutionStatus::STARTED_ASYNC_WAIT:
                    context.programCounter++;
                    // 程序计数器变化后立即通知UI更新
                    notifyUI(currentState, context.programCounter);
                    // 添加短暂延时，确保UI有足够时间更新
                    lock.unlock();
                    std::this_thread::sleep_for(std::chrono::milliseconds(50));
                    lock.lock();
                    break;
                case ExecutionStatus::EXECUTING_STEP:
                    // 即使是执行中的步骤，也通知UI更新当前状态
                    notifyUI(currentState, context.programCounter);
                    break;
                case ExecutionStatus::ERROR_HALT:
                    reportError(ErrorInfo(context.programCounter,
                        currentCommand->getDescription(), "指令执行失败"));
                    currentState = State::HALTED_ERROR;
                    notifyUI(currentState, context.programCounter);
                    break;
            }
        } else {
            // 程序执行完成，处理循环逻辑
            qDebug() << "程序执行完成，准备开始新的循环";

            // 确保所有状态都已经复位
            if (context.isMotionActive.load() || context.isStabilizing.load()) {
                lock.unlock();
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
                continue;
            }

            // 循环前等待一定时间
            lock.unlock();
            std::this_thread::sleep_for(std::chrono::seconds(1));
            lock.lock();

            // 再次检查是否在等待期间收到停止请求
            if (stopRequested) {
                qDebug() << "循环等待期间收到停止请求";
                break;
            }

            // 重置所有状态并开始新的循环
            context.programCounter = 0;
            context.resetMotionState();
            qDebug() << "开始新的程序循环";
            notifyUI(currentState, context.programCounter);
        }
    }

    // 执行线程结束处理
    qDebug() << "程序执行线程结束";

    // 确保状态设置为STOPPED
    {
        std::lock_guard<std::mutex> lock(stateMutex);
        currentState = State::STOPPED;
    }

    context.resetMotionState();

    // 再次通知UI状态已更新为STOPPED
    notifyUI(currentState, context.programCounter);
}

void ProgramExecutor::reportError(const ErrorInfo& errorInfo) {
    if (errorCallback) {
        errorCallback(errorInfo);
    }
}

void ProgramExecutor::notifyUI(State state, size_t pc) {
    if (statusUpdateCallback) {
        qDebug() << "ProgramExecutor::notifyUI - 发送状态更新到UI: 状态=" << static_cast<int>(state) << ", 行号=" << pc;

        // 直接调用回调函数
        try {
            statusUpdateCallback(state, pc);
            qDebug() << "ProgramExecutor::notifyUI - 状态更新回调执行成功";
        } catch (const std::exception& e) {
            qWarning() << "ProgramExecutor::notifyUI - 状态更新回调执行异常: " << e.what();
        } catch (...) {
            qWarning() << "ProgramExecutor::notifyUI - 状态更新回调执行未知异常";
        }
    } else {
        qWarning() << "ProgramExecutor::notifyUI - 状态更新回调未设置";
    }
}
