#include "axismappingwidget.h"
#include <QDebug>

AxisMappingWidget::AxisMappingWidget(QWidget *parent) : QWidget(parent)
{
    setupUI();
}

AxisMappingWidget::~AxisMappingWidget()
{
}

void AxisMappingWidget::setupUI()
{
    // 创建主布局
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(0, 0, 0, 0); // 减少边距，使其更紧凑

    // 创建网格布局
    QGridLayout* gridLayout = new QGridLayout();
    gridLayout->setSpacing(10); // 设置控件之间的间距
    gridLayout->setColumnStretch(0, 1); // 设置列的拉伸因子
    gridLayout->setColumnStretch(1, 2);
    gridLayout->setColumnStretch(2, 1);
    gridLayout->setColumnStretch(3, 2);

    // 添加标题
    QLabel* titleLabel = new QLabel("坐标系轴映射设置");
    QFont titleFont = titleLabel->font();
    titleFont.setBold(true);
    titleLabel->setFont(titleFont);
    mainLayout->addWidget(titleLabel);

    // 添加说明
    QLabel* descLabel = new QLabel("设置坐标系对应的物理轴映射关系");
    mainLayout->addWidget(descLabel);

    // 添加X1轴映射
    m_labelX1 = new QLabel("X1轴:");
    m_labelX1->setMinimumWidth(50); // 设置最小宽度确保标签可见
    m_labelX1->setAlignment(Qt::AlignRight | Qt::AlignVCenter); // 右对齐
    m_spinX1 = new QSpinBox();
    m_spinX1->setRange(0, 10);
    m_spinX1->setValue(0);
    m_spinX1->setMinimumWidth(80);
    gridLayout->addWidget(m_labelX1, 0, 0);
    gridLayout->addWidget(m_spinX1, 0, 1);

    // 添加Y1轴映射
    m_labelY1 = new QLabel("Y1轴:");
    m_labelY1->setMinimumWidth(50); // 设置最小宽度确保标签可见
    m_labelY1->setAlignment(Qt::AlignRight | Qt::AlignVCenter); // 右对齐
    m_spinY1 = new QSpinBox();
    m_spinY1->setRange(0, 10);
    m_spinY1->setValue(1);
    m_spinY1->setMinimumWidth(80);
    gridLayout->addWidget(m_labelY1, 0, 2);
    gridLayout->addWidget(m_spinY1, 0, 3);

    // 添加X2轴映射
    m_labelX2 = new QLabel("X2轴:");
    m_labelX2->setMinimumWidth(50); // 设置最小宽度确保标签可见
    m_labelX2->setAlignment(Qt::AlignRight | Qt::AlignVCenter); // 右对齐
    m_spinX2 = new QSpinBox();
    m_spinX2->setRange(0, 10);
    m_spinX2->setValue(2);
    m_spinX2->setMinimumWidth(80);
    gridLayout->addWidget(m_labelX2, 1, 0);
    gridLayout->addWidget(m_spinX2, 1, 1);

    // 添加Y2轴映射
    m_labelY2 = new QLabel("Y2轴:");
    m_labelY2->setMinimumWidth(50); // 设置最小宽度确保标签可见
    m_labelY2->setAlignment(Qt::AlignRight | Qt::AlignVCenter); // 右对齐
    m_spinY2 = new QSpinBox();
    m_spinY2->setRange(0, 10);
    m_spinY2->setValue(3);
    m_spinY2->setMinimumWidth(80);
    gridLayout->addWidget(m_labelY2, 1, 2);
    gridLayout->addWidget(m_spinY2, 1, 3);

    // 添加网格布局到主布局
    mainLayout->addLayout(gridLayout);

    // 添加设置按钮
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    m_btnSet = new QPushButton("设置轴映射");
    buttonLayout->addStretch();
    buttonLayout->addWidget(m_btnSet);
    mainLayout->addLayout(buttonLayout);

    // 连接信号槽
    connect(m_btnSet, &QPushButton::clicked, this, &AxisMappingWidget::onSetButtonClicked);
}

void AxisMappingWidget::onSetButtonClicked()
{
    // 获取轴映射参数
    short x1 = static_cast<short>(m_spinX1->value());
    short y1 = static_cast<short>(m_spinY1->value());
    short x2 = static_cast<short>(m_spinX2->value());
    short y2 = static_cast<short>(m_spinY2->value());

    // 获取API包装器实例
    AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();

    // 调用API设置轴映射
    if (wrapper && wrapper->isConnected()) {
        // 调用封装的API设置轴映射
        short result = wrapper->setAxisMapping(x1, y1, x2, y2);

        // 处理结果
        if (result == 0) {
            // 成功
            emit apiStatusChanged(QString("设置坐标系轴映射成功: X1=%1, Y1=%2, X2=%3, Y2=%4").arg(x1).arg(y1).arg(x2).arg(y2), true);
            QMessageBox::information(this, "成功", QString("设置坐标系轴映射成功\nX1=%1, Y1=%2, X2=%3, Y2=%4").arg(x1).arg(y1).arg(x2).arg(y2));
        } else {
            // 失败
            emit apiStatusChanged(QString("设置坐标系轴映射失败，错误码: %1").arg(result), false);
            QMessageBox::warning(this, "错误", QString("设置坐标系轴映射失败，错误码: %1").arg(result));
        }
    } else {
        // 未连接
        QMessageBox::warning(this, "错误", "未连接到控制器，请先连接设备");
        emit apiStatusChanged("未连接到控制器，无法设置轴映射", false);
    }
}
