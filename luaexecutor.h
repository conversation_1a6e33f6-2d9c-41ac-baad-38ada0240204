#ifndef LUAEXECUTOR_H
#define LUAEXECUTOR_H

#include <QObject>
#include <QString>
#include <QMap>
#include <QMutex>
#include <QThread>
#include <QWaitCondition>
#include <QMetaType>
#include <memory>
#include <atomic>
#include <functional>
#include "admc_api_wrapper.h"

// 包含Lua头文件
extern "C" {
#include "luadll/lua.h"
#include "luadll/lauxlib.h"
#include "luadll/lualib.h"
}

// Lua执行器类，用于执行Lua脚本
class LuaExecutor : public QObject
{
    Q_OBJECT

public:
    // 执行状态枚举
    enum class State {
        STOPPED,    // 停止状态
        RUNNING,    // 运行状态
        PAUSED,     // 暂停状态
        ERROR       // 错误状态
    };

    // 声明State为元类型，使其可以在信号槽中使用
    Q_ENUM(State)

    // 坐标系枚举
    enum class CoordSystem {
        COORD1 = 0,  // 坐标系1
        COORD2 = 1   // 坐标系2
    };

    // 声明CoordSystem为元类型，使其可以在信号槽中使用
    Q_ENUM(CoordSystem)

    // 获取单例实例
    static LuaExecutor* getInstance();

    // 删除拷贝构造和赋值操作符
    LuaExecutor(const LuaExecutor&) = delete;
    LuaExecutor& operator=(const LuaExecutor&) = delete;

    // 加载脚本
    bool loadScript(CoordSystem coord, const QString& script);

    // 启动脚本执行
    bool startExecution(CoordSystem coord);

    // 启动所有脚本执行
    bool startAllExecution();

    // 注意：暂停和恢复功能暂未实现
    // 暂停脚本执行
    bool pauseExecution(CoordSystem /*coord*/) { return false; }

    // 恢复脚本执行
    bool resumeExecution(CoordSystem /*coord*/) { return false; }

    // 停止脚本执行
    bool stopExecution(CoordSystem coord);

    // 停止所有脚本执行
    bool stopAllExecution();

    // 获取当前状态
    State getState(CoordSystem coord) const;

    // 获取错误信息
    QString getErrorMessage(CoordSystem coord) const;

    // 设置共享变量
    bool setSharedVariable(const QString& name, const QVariant& value);

    // 获取共享变量
    QVariant getSharedVariable(const QString& name);

    // 获取所有共享变量
    QMap<QString, QVariant> getAllSharedVariables();

signals:
    // 状态变化信号
    void stateChanged(CoordSystem coord, State state);

    // 错误信号
    void errorOccurred(CoordSystem coord, const QString& errorMessage);

    // 输出信号
    void outputProduced(CoordSystem coord, const QString& output);

private:
    // 单例实现
    explicit LuaExecutor(QObject* parent = nullptr);
    ~LuaExecutor();

    // Lua状态
    lua_State* m_luaState[2];  // 两个坐标系的Lua状态

    // 执行线程
    QThread* m_executionThread[2];  // 两个坐标系的执行线程

    // 当前状态
    std::atomic<State> m_state[2];  // 两个坐标系的状态

    // 错误信息
    QString m_errorMessage[2];  // 两个坐标系的错误信息

    // 共享变量
    QMap<QString, QVariant> m_sharedVariables;  // 共享变量映射表
    QMutex m_sharedVariablesMutex;  // 共享变量互斥锁

    // 停止标志
    std::atomic<bool> m_stopRequested[2];  // 两个坐标系的停止请求标志

    // 单例实例
    static LuaExecutor* m_instance;

    // 注册C++函数到Lua
    void registerFunctions(CoordSystem coord);

    // 执行线程函数
    void executionThreadFunc(CoordSystem coord);

    // 重置Lua状态
    void resetLuaState(CoordSystem coord);

    // API包装器实例
    AdmcApiWrapper* m_apiWrapper;

public:
    // 获取API包装器实例
    AdmcApiWrapper* getApiWrapper() const { return m_apiWrapper; }

    // 运动指令包实现函数
    // 直线插补
    static int lua_linearMove(lua_State* L);
    // 圆弧插补
    static int lua_arcMove(lua_State* L);
    // 轴点位运动
    static int lua_axisTrapMove(lua_State* L);
    // 延时
    static int lua_delay(lua_State* L);
    // 设置共享变量
    static int lua_setSharedVariable(lua_State* L);
    // 获取共享变量
    static int lua_getSharedVariable(lua_State* L);
    // 打印输出
    static int lua_print(lua_State* L);
    // 等待运动完成
    static int lua_waitMotionComplete(lua_State* L);
    // 等待条件满足
    static int lua_waitCondition(lua_State* L);
    // 设置IO输出
    static int lua_setIoOutput(lua_State* L);

    // 处理错误并停止脚本执行
    static void handleError(lua_State* L, const QString& errorMsg);

    // Lua钩子函数，用于检查停止标志
    static void luaHook(lua_State* L, lua_Debug* ar);
};

// 声明CoordSystem和State为元类型，使其可以在信号槽中使用
Q_DECLARE_METATYPE(LuaExecutor::CoordSystem)
Q_DECLARE_METATYPE(LuaExecutor::State)

#endif // LUAEXECUTOR_H
