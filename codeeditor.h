#ifndef CODEEDITOR_H
#define CODEEDITOR_H

#include <QPlainTextEdit>
#include <QObject>

QT_BEGIN_NAMESPACE
class QPaintEvent;
class QResizeEvent;
class QSize;
class QWidget;
QT_END_NAMESPACE

class LineNumberArea;

// 带行号显示的代码编辑器
class CodeEditor : public QPlainTextEdit
{
    Q_OBJECT

public:
    CodeEditor(QWidget *parent = nullptr);

    // 绘制行号区域
    void lineNumberAreaPaintEvent(QPaintEvent *event);
    // 计算行号区域宽度
    int lineNumberAreaWidth();

protected:
    // 重写事件处理函数
    void resizeEvent(QResizeEvent *event) override;

private slots:
    // 更新行号区域宽度
    void updateLineNumberAreaWidth(int newBlockCount);
    // 高亮当前行
    void highlightCurrentLine();
    // 更新行号区域
    void updateLineNumberArea(const QRect &rect, int dy);

private:
    QWidget *lineNumberArea;
};

// 行号区域类
class LineNumberArea : public QWidget
{
public:
    LineNumberArea(CodeEditor *editor) : QWidget(editor), codeEditor(editor)
    {}

    QSize sizeHint() const override
    {
        return QSize(codeEditor->lineNumberAreaWidth(), 0);
    }

protected:
    void paintEvent(QPaintEvent *event) override
    {
        codeEditor->lineNumberAreaPaintEvent(event);
    }

private:
    CodeEditor *codeEditor;
};

#endif // CODEEDITOR_H
