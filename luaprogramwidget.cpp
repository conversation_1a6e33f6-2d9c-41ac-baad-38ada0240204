#include "luaprogramwidget.h"
#include <QDebug>
#include <QFileDialog>
#include <QMessageBox>
#include <QInputDialog>
#include <QApplication>
#include <QFile>
#include <QTextStream>
#include <QTimer>
#include <QTableWidgetItem>
#include <QScrollBar>  // 添加QScrollBar头文件

// LuaSyntaxHighlighter 实现
LuaSyntaxHighlighter::LuaSyntaxHighlighter(QTextDocument *parent)
    : QSyntaxHighlighter(parent)
{
    // 设置关键字格式
    keywordFormat.setForeground(Qt::blue);
    keywordFormat.setFontWeight(QFont::Bold);

    // Lua关键字列表
    QStringList keywordPatterns;
    keywordPatterns << "\\bfunction\\b" << "\\bend\\b" << "\\bif\\b" << "\\bthen\\b"
                    << "\\belse\\b" << "\\belseif\\b" << "\\bfor\\b" << "\\bin\\b"
                    << "\\bdo\\b" << "\\bwhile\\b" << "\\brepeat\\b" << "\\buntil\\b"
                    << "\\blocal\\b" << "\\breturn\\b" << "\\bbreak\\b" << "\\bnil\\b"
                    << "\\btrue\\b" << "\\bfalse\\b" << "\\band\\b" << "\\bor\\b"
                    << "\\bnot\\b" << "\\bgoto\\b";

    // 添加关键字规则
    foreach (const QString &pattern, keywordPatterns) {
        HighlightingRule rule;
        rule.pattern = QRegularExpression(pattern);
        rule.format = keywordFormat;
        highlightingRules.append(rule);
    }

    // 设置函数格式
    functionFormat.setForeground(Qt::darkGreen);
    functionFormat.setFontWeight(QFont::Bold);

    // 添加函数规则
    HighlightingRule rule;
    rule.pattern = QRegularExpression("\\b[A-Za-z0-9_]+(?=\\()");
    rule.format = functionFormat;
    highlightingRules.append(rule);

    // 设置字符串格式
    stringFormat.setForeground(Qt::darkRed);
    rule.pattern = QRegularExpression("\".*\"");
    rule.format = stringFormat;
    highlightingRules.append(rule);

    rule.pattern = QRegularExpression("'.*'");
    rule.format = stringFormat;
    highlightingRules.append(rule);

    // 设置注释格式
    commentFormat.setForeground(Qt::gray);
    rule.pattern = QRegularExpression("--[^\n]*");
    rule.format = commentFormat;
    highlightingRules.append(rule);

    // 设置数字格式
    numberFormat.setForeground(Qt::darkCyan);
    rule.pattern = QRegularExpression("\\b\\d+\\.?\\d*\\b");
    rule.format = numberFormat;
    highlightingRules.append(rule);
}

void LuaSyntaxHighlighter::highlightBlock(const QString &text)
{
    // 应用所有高亮规则
    foreach (const HighlightingRule &rule, highlightingRules) {
        QRegularExpressionMatchIterator matchIterator = rule.pattern.globalMatch(text);
        while (matchIterator.hasNext()) {
            QRegularExpressionMatch match = matchIterator.next();
            setFormat(match.capturedStart(), match.capturedLength(), rule.format);
        }
    }
}

// LuaProgramWidget 实现
LuaProgramWidget::LuaProgramWidget(QWidget *parent)
    : QWidget(parent),
      m_luaExecutor(LuaExecutor::getInstance()),
      m_currentCoord(LuaExecutor::CoordSystem::COORD1),
      m_unitConverter(UnitConverter::getInstance())
{
    // 初始化UI
    setupUI();

    // 连接信号和槽
    connect(m_insertTemplateButton, &QPushButton::clicked, this, &LuaProgramWidget::onInsertTemplateButtonClicked);
    connect(m_templateTypeComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &LuaProgramWidget::onTemplateTypeChanged);
    connect(m_loadButton, &QPushButton::clicked, this, &LuaProgramWidget::onLoadButtonClicked);
    connect(m_saveButton, &QPushButton::clicked, this, &LuaProgramWidget::onSaveButtonClicked);
    connect(m_startButton, &QPushButton::clicked, this, &LuaProgramWidget::onStartButtonClicked);
    connect(m_stopButton, &QPushButton::clicked, this, &LuaProgramWidget::onStopButtonClicked);
    connect(m_startAllButton, &QPushButton::clicked, this, &LuaProgramWidget::onStartAllButtonClicked);
    connect(m_stopAllButton, &QPushButton::clicked, this, &LuaProgramWidget::onStopAllButtonClicked);
    connect(m_coordTabWidget, &QTabWidget::currentChanged, this, &LuaProgramWidget::onCoordTabChanged);

    // 连接Lua执行器信号
    connect(m_luaExecutor, &LuaExecutor::stateChanged, this, &LuaProgramWidget::onStateChanged);
    connect(m_luaExecutor, &LuaExecutor::errorOccurred, this, &LuaProgramWidget::onErrorOccurred);
    connect(m_luaExecutor, &LuaExecutor::outputProduced, this, &LuaProgramWidget::onOutputProduced);

    // 连接单位转换器信号
    if (m_unitConverter) {
        connect(m_unitConverter, &UnitConverter::unitTypeChanged, this, &LuaProgramWidget::onUnitTypeChanged);
    }

    // 初始化UI状态
    updateUIState(LuaExecutor::CoordSystem::COORD1, m_luaExecutor->getState(LuaExecutor::CoordSystem::COORD1));
    updateUIState(LuaExecutor::CoordSystem::COORD2, m_luaExecutor->getState(LuaExecutor::CoordSystem::COORD2));
}

LuaProgramWidget::~LuaProgramWidget()
{
    // 析构函数
}

void LuaProgramWidget::setupUI()
{
    // 创建主布局
    QVBoxLayout* mainLayout = new QVBoxLayout(this);

    // 创建上下分割器
    QSplitter* splitter = new QSplitter(Qt::Vertical);
    mainLayout->addWidget(splitter);

    // 创建坐标系选项卡
    m_coordTabWidget = new QTabWidget();
    m_coordTabWidget->addTab(new QWidget(), "坐标系1");
    m_coordTabWidget->addTab(new QWidget(), "坐标系2");
    splitter->addWidget(m_coordTabWidget);

    // 为每个坐标系创建脚本编辑器
    for (int i = 0; i < 2; i++) {
        QWidget* tab = m_coordTabWidget->widget(i);
        QVBoxLayout* tabLayout = new QVBoxLayout(tab);

        // 创建脚本编辑器
        m_scriptEditors[i] = new QPlainTextEdit();
        m_scriptEditors[i]->setFont(QFont("Courier New", 10));
        m_scriptEditors[i]->setLineWrapMode(QPlainTextEdit::NoWrap);
        tabLayout->addWidget(m_scriptEditors[i]);

        // 创建语法高亮器
        m_highlighters[i] = new LuaSyntaxHighlighter(m_scriptEditors[i]->document());

        // 创建状态和错误标签
        QHBoxLayout* statusLayout = new QHBoxLayout();
        m_statusLabel[i] = new QLabel("状态: 停止");
        m_errorLabel[i] = new QLabel("");
        m_errorLabel[i]->setStyleSheet("color: red;");
        statusLayout->addWidget(m_statusLabel[i]);
        statusLayout->addWidget(m_errorLabel[i]);
        statusLayout->addStretch();
        tabLayout->addLayout(statusLayout);
    }

    // 创建输出窗口
    m_outputTextEdit = new QTextEdit();
    m_outputTextEdit->setReadOnly(true);
    m_outputTextEdit->setFont(QFont("Courier New", 10));
    splitter->addWidget(m_outputTextEdit);

    // 创建控制面板
    QWidget* controlPanel = new QWidget();
    QHBoxLayout* controlLayout = new QHBoxLayout(controlPanel);
    mainLayout->addWidget(controlPanel);

    // 创建模板插入控件
    QGroupBox* templateGroup = new QGroupBox("指令模板");
    QVBoxLayout* templateLayout = new QVBoxLayout(templateGroup);
    m_templateTypeComboBox = new QComboBox();
    m_insertTemplateButton = new QPushButton("插入");
    templateLayout->addWidget(m_templateTypeComboBox);
    templateLayout->addWidget(m_insertTemplateButton);
    controlLayout->addWidget(templateGroup);

    // 创建文件操作控件
    QGroupBox* fileGroup = new QGroupBox("文件操作");
    QVBoxLayout* fileLayout = new QVBoxLayout(fileGroup);
    m_loadButton = new QPushButton("加载");
    m_saveButton = new QPushButton("保存");
    fileLayout->addWidget(m_loadButton);
    fileLayout->addWidget(m_saveButton);
    controlLayout->addWidget(fileGroup);

    // 创建执行控制控件
    QGroupBox* execGroup = new QGroupBox("执行控制");
    QVBoxLayout* execLayout = new QVBoxLayout(execGroup);
    m_startButton = new QPushButton("启动");
    m_stopButton = new QPushButton("停止");
    execLayout->addWidget(m_startButton);
    execLayout->addWidget(m_stopButton);
    controlLayout->addWidget(execGroup);

    // 创建全局执行控制控件
    QGroupBox* globalExecGroup = new QGroupBox("全局执行控制");
    QVBoxLayout* globalExecLayout = new QVBoxLayout(globalExecGroup);
    m_startAllButton = new QPushButton("启动所有");
    m_stopAllButton = new QPushButton("停止所有");
    globalExecLayout->addWidget(m_startAllButton);
    globalExecLayout->addWidget(m_stopAllButton);
    controlLayout->addWidget(globalExecGroup);

    // 创建模板类型下拉框
    createTemplateTypeComboBox();

    // 设置初始分割器大小
    splitter->setSizes(QList<int>() << 300 << 100);
}

void LuaProgramWidget::createTemplateTypeComboBox()
{
    m_templateTypeComboBox->clear();
    m_templateTypeComboBox->addItem("直线插补", "linear");
    m_templateTypeComboBox->addItem("圆弧插补", "arc");
    m_templateTypeComboBox->addItem("轴点位运动", "axis");
    m_templateTypeComboBox->addItem("延时", "delay");
    m_templateTypeComboBox->addItem("设置共享变量", "set_var");
    m_templateTypeComboBox->addItem("获取共享变量", "get_var");
    m_templateTypeComboBox->addItem("条件判断", "if");
    m_templateTypeComboBox->addItem("循环", "loop");
    m_templateTypeComboBox->addItem("函数定义", "function");
    m_templateTypeComboBox->addItem("打印输出", "print");

    // 设置当前模板类型
    m_currentTemplateType = "linear";
}

QString LuaProgramWidget::getTemplateCode(const QString& templateType)
{
    if (templateType == "linear") {
        return "coord.move_linear(0, 0, 100, 10) -- X, Y, 速度, 加速度";
    } else if (templateType == "arc") {
        return "coord.move_arc(0, 0, 10, 1, 100, 10, 0) -- X, Y, 半径, 方向, 速度, 加速度, 终点速度";
    } else if (templateType == "axis") {
        return "axis.move_trap(0, 1000, 100, 10, 1) -- 轴号, 目标位置, 速度, 加速度, 加速度比例";
    } else if (templateType == "delay") {
        return "delay(1000) -- 延时毫秒数";
    } else if (templateType == "set_var") {
        return "set_shared_variable(\"变量名\", 值) -- 设置共享变量";
    } else if (templateType == "get_var") {
        return "local value = get_shared_variable(\"变量名\") -- 获取共享变量";
    } else if (templateType == "if") {
        return "if 条件 then\n    -- 代码块\nelse\n    -- 代码块\nend";
    } else if (templateType == "loop") {
        return "for i = 1, 10 do\n    -- 代码块\nend";
    } else if (templateType == "function") {
        return "function 函数名(参数1, 参数2)\n    -- 代码块\n    return 返回值\nend";
    } else if (templateType == "print") {
        return "print(\"输出内容\") -- 打印输出";
    } else {
        return "";
    }
}

void LuaProgramWidget::onInsertTemplateButtonClicked()
{
    // 获取当前坐标系
    int tabIndex = m_coordTabWidget->currentIndex();
    QPlainTextEdit* editor = m_scriptEditors[tabIndex];

    // 获取模板代码
    QString templateCode = getTemplateCode(m_currentTemplateType);

    // 插入模板代码
    if (!templateCode.isEmpty()) {
        editor->insertPlainText(templateCode + "\n");
    }
}

void LuaProgramWidget::onTemplateTypeChanged(int index)
{
    // 更新当前模板类型
    m_currentTemplateType = m_templateTypeComboBox->itemData(index).toString();
}

void LuaProgramWidget::onLoadButtonClicked()
{
    // 获取当前坐标系
    int tabIndex = m_coordTabWidget->currentIndex();

    // 打开文件对话框
    QString filePath = QFileDialog::getOpenFileName(this, "加载Lua脚本", "", "Lua脚本 (*.lua);;所有文件 (*)");
    if (!filePath.isEmpty()) {
        if (loadScriptFromFile(static_cast<LuaExecutor::CoordSystem>(tabIndex), filePath)) {
            m_currentFilePaths[tabIndex] = filePath;
            QMessageBox::information(this, "加载成功", "脚本加载成功");
        }
    }
}

void LuaProgramWidget::onSaveButtonClicked()
{
    // 获取当前坐标系
    int tabIndex = m_coordTabWidget->currentIndex();

    // 打开文件对话框
    QString filePath = QFileDialog::getSaveFileName(this, "保存Lua脚本", "", "Lua脚本 (*.lua);;所有文件 (*)");
    if (!filePath.isEmpty()) {
        if (saveScriptToFile(static_cast<LuaExecutor::CoordSystem>(tabIndex), filePath)) {
            m_currentFilePaths[tabIndex] = filePath;
            QMessageBox::information(this, "保存成功", "脚本保存成功");
        }
    }
}

bool LuaProgramWidget::loadScriptFromFile(LuaExecutor::CoordSystem coord, const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QMessageBox::warning(this, "加载失败", "无法打开文件: " + filePath);
        return false;
    }

    QTextStream in(&file);
    QString script = in.readAll();
    file.close();

    // 设置脚本编辑器内容
    int index = static_cast<int>(coord);
    m_scriptEditors[index]->setPlainText(script);

    return true;
}

bool LuaProgramWidget::saveScriptToFile(LuaExecutor::CoordSystem coord, const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QMessageBox::warning(this, "保存失败", "无法写入文件: " + filePath);
        return false;
    }

    QTextStream out(&file);
    int index = static_cast<int>(coord);
    out << m_scriptEditors[index]->toPlainText();
    file.close();

    return true;
}

void LuaProgramWidget::onStartButtonClicked()
{
    // 获取当前坐标系
    int tabIndex = m_coordTabWidget->currentIndex();
    LuaExecutor::CoordSystem coord = static_cast<LuaExecutor::CoordSystem>(tabIndex);

    // 获取脚本内容
    QString script = m_scriptEditors[tabIndex]->toPlainText();
    if (script.isEmpty()) {
        QMessageBox::warning(this, "启动失败", "脚本内容为空");
        return;
    }

    // 加载并执行脚本
    if (m_luaExecutor->loadScript(coord, script)) {
        m_luaExecutor->startExecution(coord);
    } else {
        QMessageBox::warning(this, "启动失败", "脚本加载失败: " + m_luaExecutor->getErrorMessage(coord));
    }
}

void LuaProgramWidget::onStopButtonClicked()
{
    // 获取当前坐标系
    int tabIndex = m_coordTabWidget->currentIndex();
    LuaExecutor::CoordSystem coord = static_cast<LuaExecutor::CoordSystem>(tabIndex);

    // 停止执行
    m_luaExecutor->stopExecution(coord);
}

void LuaProgramWidget::onStartAllButtonClicked()
{
    // 检查两个坐标系的脚本是否都为空
    bool hasScript = false;
    for (int i = 0; i < 2; i++) {
        if (!m_scriptEditors[i]->toPlainText().isEmpty()) {
            hasScript = true;
            break;
        }
    }

    if (!hasScript) {
        QMessageBox::warning(this, "启动失败", "两个坐标系的脚本都为空");
        return;
    }

    // 加载并执行两个坐标系的脚本
    for (int i = 0; i < 2; i++) {
        LuaExecutor::CoordSystem coord = static_cast<LuaExecutor::CoordSystem>(i);
        QString script = m_scriptEditors[i]->toPlainText();
        if (!script.isEmpty()) {
            m_luaExecutor->loadScript(coord, script);
        }
    }

    // 启动所有脚本
    m_luaExecutor->startAllExecution();
}

void LuaProgramWidget::onStopAllButtonClicked()
{
    // 停止所有脚本
    m_luaExecutor->stopAllExecution();
}

void LuaProgramWidget::onCoordTabChanged(int index)
{
    // 更新当前坐标系
    m_currentCoord = static_cast<LuaExecutor::CoordSystem>(index);
}

void LuaProgramWidget::onStateChanged(LuaExecutor::CoordSystem coord, LuaExecutor::State state)
{
    // 更新UI状态
    updateUIState(coord, state);
}

void LuaProgramWidget::onErrorOccurred(LuaExecutor::CoordSystem coord, const QString& errorMessage)
{
    // 显示错误信息
    int index = static_cast<int>(coord);
    m_errorLabel[index]->setText(errorMessage);

    // 添加错误输出
    QString prefix = (coord == LuaExecutor::CoordSystem::COORD1) ? "[坐标系1]" : "[坐标系2]";
    addOutputText(prefix + " 错误: " + errorMessage, Qt::red);
}

void LuaProgramWidget::onOutputProduced(LuaExecutor::CoordSystem coord, const QString& output)
{
    qDebug() << "LuaProgramWidget::onOutputProduced被调用，坐标系:" << static_cast<int>(coord) << "输出:" << output;
    // 添加输出文本
    QString prefix = (coord == LuaExecutor::CoordSystem::COORD1) ? "[坐标系1]" : "[坐标系2]";
    addOutputText(prefix + " " + output);
    qDebug() << "输出已添加到m_outputTextEdit";
}

void LuaProgramWidget::onUnitTypeChanged(UnitType type)
{
    // 处理单位类型变化
    qDebug() << "单位类型变化为:" << (type == UNIT_MM ? "毫米" : "脉冲");
}

void LuaProgramWidget::updateUIState(LuaExecutor::CoordSystem coord, LuaExecutor::State state)
{
    int index = static_cast<int>(coord);

    // 更新状态标签
    QString stateText;
    switch (state) {
        case LuaExecutor::State::STOPPED:
            stateText = "停止";
            break;
        case LuaExecutor::State::RUNNING:
            stateText = "运行中";
            break;
        case LuaExecutor::State::PAUSED:
            stateText = "暂停";
            break;
        case LuaExecutor::State::ERROR:
            stateText = "错误";
            break;
    }
    m_statusLabel[index]->setText("状态: " + stateText);

    // 调试输出当前状态
    qDebug() << "LuaProgramWidget::updateUIState - 坐标系" << index + 1 << "状态=" << stateText;

    // 更新按钮状态
    bool isRunning = (state == LuaExecutor::State::RUNNING);
    bool isAnyRunning = (m_luaExecutor->getState(LuaExecutor::CoordSystem::COORD1) == LuaExecutor::State::RUNNING) ||
                        (m_luaExecutor->getState(LuaExecutor::CoordSystem::COORD2) == LuaExecutor::State::RUNNING);

    // 当前坐标系的按钮
    if (m_currentCoord == coord) {
        m_startButton->setEnabled(!isRunning);
        // 停止按钮在有轴运动时也应该可用
        bool isAnyAxisMoving = false;
        for (short axis = 0; axis < 4; ++axis) {
            short status = 0;
            short result = m_luaExecutor->getApiWrapper()->getAxisStatus(axis, status);
            if (result == 0) {
                // 检查运动中状态位（MOV，第5位）
                if ((status & (1 << 5)) != 0) {
                    isAnyAxisMoving = true;
                    break;
                }
            }
        }
        m_stopButton->setEnabled(isRunning || isAnyAxisMoving);
    }

    // 全局按钮
    m_startAllButton->setEnabled(!isAnyRunning);

    // 检查是否有轴在运动
    bool isAnyAxisMoving = false;
    for (short axis = 0; axis < 4; ++axis) {
        short status = 0;
        short result = m_luaExecutor->getApiWrapper()->getAxisStatus(axis, status);
        if (result == 0) {
            // 检查运动中状态位（MOV，第5位）
            if ((status & (1 << 5)) != 0) {
                isAnyAxisMoving = true;
                break;
            }
        }
    }

    m_stopAllButton->setEnabled(isAnyRunning || isAnyAxisMoving);

    // 编辑器状态
    m_scriptEditors[index]->setReadOnly(isRunning);

    // 如果状态为错误，清除错误信息
    if (state != LuaExecutor::State::ERROR) {
        m_errorLabel[index]->clear();
    }

    // 如果状态是STOPPED或ERROR，强制更新按钮状态
    if (state == LuaExecutor::State::STOPPED || state == LuaExecutor::State::ERROR) {
        // 当前坐标系的按钮
        if (m_currentCoord == coord) {
            m_startButton->setEnabled(true);
            m_stopButton->setEnabled(false);
        }

        // 编辑器状态
        m_scriptEditors[index]->setReadOnly(false);

        // 如果两个坐标系都停止了，启用全局按钮
        if (m_luaExecutor->getState(LuaExecutor::CoordSystem::COORD1) != LuaExecutor::State::RUNNING &&
            m_luaExecutor->getState(LuaExecutor::CoordSystem::COORD2) != LuaExecutor::State::RUNNING) {
            m_startAllButton->setEnabled(true);
            m_stopAllButton->setEnabled(false);
        }
    }

    // 强制更新UI
    QApplication::processEvents();
}

void LuaProgramWidget::addOutputText(const QString& text, const QColor& color)
{
    // 设置文本颜色
    m_outputTextEdit->setTextColor(color);

    // 添加文本
    m_outputTextEdit->append(text);

    // 滚动到底部
    QScrollBar* scrollBar = m_outputTextEdit->verticalScrollBar();
    if (scrollBar) {
        scrollBar->setValue(scrollBar->maximum());
    }
}

void LuaProgramWidget::clearOutput()
{
    m_outputTextEdit->clear();
}
