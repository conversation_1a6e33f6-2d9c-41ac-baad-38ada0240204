<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>AxisControlWidget</class>
 <widget class="QWidget" name="AxisControlWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>640</width>
    <height>480</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>JOG运动</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QGroupBox" name="groupAxis">
     <property name="title">
      <string>轴选择与控制</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <widget class="QLabel" name="label">
        <property name="text">
         <string>轴:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QComboBox" name="comboAxis"/>
      </item>
     </layout>
    </widget>
   </item>

   <item>
    <widget class="QGroupBox" name="groupBox_2">
     <property name="title">
      <string>实时状态</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="0" column="0">
       <widget class="QLabel" name="label_5">
        <property name="text">
         <string>当前位置:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLCDNumber" name="lcdPosition">
        <property name="digitCount">
         <number>8</number>
        </property>
       </widget>
      </item>
      <item row="0" column="2">
       <widget class="QLabel" name="label_7">
        <property name="text">
         <string>mm</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>

   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>