# 安达运动控制系统

## 项目概述
安达运动控制系统是一款基于Qt开发的多轴运动控制上位机软件，用于实现对运动控制器和多轴伺服系统的操作与监控。软件提供了友好的图形界面，支持轴回零、点动、状态监控和高级运动控制等功能。

## 技术栈
- C++11
- Qt 5.12
- Qt Widgets
- QMake构建系统

## 核心功能
- **设备管理**：支持通过TCP/IP连接运动控制器，管理多个伺服轴
- **轴基本操作**：轴使能/禁用控制，轴复位操作
- **回零功能**：支持单轴和多轴回零操作
- **点动控制**：提供基于速度的手动点动功能
- **Jog控制**：实现带加减速的精确Jog控制
- **高级运动**：支持绝对位置和相对位置运动
- **状态监控**：实时显示轴位置、速度和状态信息

## 项目结构
```
MotionControlApp/
├── main.cpp                  # 程序入口
├── mainwindow.cpp/h          # 主窗口实现
├── mainwindow.ui             # 主窗口UI文件
├── axiscontrolwidget.cpp/h   # 轴高级控制界面
├── axiscontrolwidget.ui      # 轴高级控制UI文件
├── homewidget.cpp/h          # 回零控制界面
├── homewidget.ui             # 回零控制UI文件
├── jogwidget.cpp/h           # 点动控制界面
├── jogwidget.ui              # 点动控制UI文件
├── statuswidget.cpp/h        # 轴状态界面
├── statuswidget.ui           # 轴状态UI文件
├── connectiondialog.cpp/h    # 设备连接对话框
├── MotionControlApp.pro      # 项目文件
└── README.md                 # 项目说明
```

## 界面介绍

### 主界面
- 设备列表：左侧显示控制器及轴设备树形结构
- 功能选项卡：右侧提供多个功能页面
- 状态栏：底部显示连接状态和系统时间

### 功能模块
1. **回零**：轴回零控制，支持单轴和多轴回零
2. **点动**：基本点动控制，提供正向和负向点动
3. **Jog控制**：精确速度和加速度控制的Jog操作
4. **轴状态**：显示各轴的实时状态和参数
5. **高级控制**：提供绝对运动和相对运动功能

## 开发与构建
### 环境要求
- Qt 5.12或更高版本
- MinGW 7.3.0或MSVC 2017/2019 (Windows)
- GCC 5.3.0或更高版本 (Linux)

### 构建步骤
1. 打开Qt Creator
2. 加载`MotionControlApp.pro`
3. 配置项目构建设置
4. 点击构建按钮编译项目
5. 运行程序

## 使用说明
请参阅[ChangeLog.md](./ChangeLog.md)文件中的详细功能说明和使用方法。

## 版权信息
© 2023 安达自动化科技有限公司。保留所有权利。 