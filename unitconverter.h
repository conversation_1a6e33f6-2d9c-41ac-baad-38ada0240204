#ifndef UNITCONVERTER_H
#define UNITCONVERTER_H

#include <QString>
#include <QObject>

// 单位类型枚举
enum UnitType {
    UNIT_PULSE,  // 脉冲单位
    UNIT_MM      // 毫米单位
};

// 单位转换类，用于处理不同单位之间的转换
class UnitConverter : public QObject
{
    Q_OBJECT

public:
    // 获取单例实例
    static UnitConverter* getInstance();

    // 删除拷贝构造和赋值操作符
    UnitConverter(const UnitConverter&) = delete;
    UnitConverter& operator=(const UnitConverter&) = delete;

    // 获取和设置当前单位类型
    UnitType getCurrentUnitType() const;
    void setCurrentUnitType(UnitType type);

    // 单位转换函数
    // 位置转换
    double pulseToMm(double pulse) const;
    double mmToPulse(double mm) const;

    // 速度转换
    double pulsePerMsToMmPerS(double pulsePerMs) const;
    double mmPerSToPulsePerMs(double mmPerS) const;

    // 加速度转换
    double pulsePerMsSquaredToMmPerSSquared(double pulsePerMsSquared) const;
    double mmPerSSquaredToPulsePerMsSquared(double mmPerSSquared) const;

    // 获取单位字符串
    QString getPositionUnitString() const;
    QString getVelocityUnitString() const;
    QString getAccelerationUnitString() const;

    // 转换并格式化数值显示
    QString formatPosition(double value) const;
    QString formatVelocity(double value) const;
    QString formatAcceleration(double value) const;

signals:
    void unitTypeChanged(UnitType type);

private:
    // 单例实现
    explicit UnitConverter(QObject* parent = nullptr);
    ~UnitConverter();
    static UnitConverter* m_instance;
    UnitType m_currentUnitType;

    // 转换常数
    const double PULSE_TO_MM = 0.001;  // 1 pulse = 0.001 mm
    const double MM_TO_PULSE = 1000.0; // 1 mm = 1000 pulse
};

#endif // UNITCONVERTER_H
