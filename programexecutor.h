#ifndef PROGRAMEXECUTOR_H
#define PROGRAMEXECUTOR_H

#include <vector>
#include <string>
#include <memory>         // std::unique_ptr
#include <fstream>        // std::ofstream, std::ifstream
#include <chrono>         // std::chrono::*
#include <atomic>         // std::atomic<*>
#include <mutex>          // std::mutex
#include <condition_variable> // std::condition_variable
#include <thread>         // std::thread
#include <functional>     // std::function
// C++11 不支持 std::optional，使用自定义实现

// --- 前置声明 ---
class ExecutionContext;
class Command;
struct ErrorInfo;

// --- 执行状态枚举 ---
// 指令执行后返回给 ProgramExecutor 的状态
enum class ExecutionStatus {
    COMPLETED_NEXT,     // 指令完成，请执行下一条 (PC++)
    STARTED_ASYNC_WAIT, // 启动了需要等待的异步操作 (如运动)，PC++
    EXECUTING_STEP,     // 指令内部步骤/等待中，请再次调用本指令 execute (PC不变)
    ERROR_HALT          // 发生错误，停止执行
};

// --- 指令基类 ---
class Command {
public:
    virtual ~Command() = default;
    // 执行指令的核心方法
    virtual ExecutionStatus execute(ExecutionContext& context) = 0;
    // 获取指令的易读描述，用于 UI 列表显示
    virtual std::string getDescription() const = 0;
    // 获取指令的类型标识符，用于文件保存和加载时的识别
    virtual std::string getType() const = 0;
    // 将指令自身的参数序列化到输出流
    virtual void saveParameters(std::ostream& os) const = 0;
    // 从输入流反序列化参数到指令对象
    virtual bool loadParameters(std::istream& is) = 0;
    // (可选) 克隆方法，用于修改指令时创建副本
    // virtual std::unique_ptr<Command> clone() const = 0;
};

// --- 运动指令基类 (处理指令包) ---
class MotionCommand : public Command {
protected:
    enum class MotionStep { IDLE, SET_MODE, SET_PARAMS, START_MOVE }; // 指令包内部步骤
    MotionStep currentStep = MotionStep::IDLE; // 当前执行到哪一步
    int crd = 0; // 坐标系/轴组 ID

    // 子类必须实现这些与硬件 API 交互的方法
    virtual bool apiSetMode() = 0;     // 调用API设置运动模式
    virtual bool apiSetParams() = 0;   // 调用API设置运动参数
    virtual bool apiStartMove() = 0;   // 调用API启动运动

public:
    MotionCommand() = default; // 用于文件加载
    MotionCommand(int coord_system) : crd(coord_system) {}

    // 实现分步执行逻辑
    ExecutionStatus execute(ExecutionContext& context) override;

    // 序列化/反序列化坐标系ID等通用参数
    void saveParameters(std::ostream& os) const override;
    bool loadParameters(std::istream& is) override;
};

// --- 具体指令实现 (示例) ---
class LinearMoveCommand : public MotionCommand {
    double targetPos[3] = {0.0}; double speed = 0.0, acc = 0.0;
public:
    LinearMoveCommand() = default;
    LinearMoveCommand(int crd, double x, double y, double z, double s, double a)
        : MotionCommand(crd), speed(s), acc(a) { targetPos[0]=x; targetPos[1]=y; targetPos[2]=z; }
    std::string getType() const override { return "LINEAR"; }
    std::string getDescription() const override;
    void saveParameters(std::ostream& os) const override;
    bool loadParameters(std::istream& is) override;
protected:
    bool apiSetMode() override;
    bool apiSetParams() override;
    bool apiStartMove() override;
};

// --- 轴点位运动指令 ---
class AxisTrapCommand : public MotionCommand {
    int axis = 0;                // 轴号
    double posTarget = 0.0;      // 目标位置
    double velMax = 0.0;         // 最大速度
    double acc = 0.0;            // 加速度
    short rat = 0;               // 加速度比例
public:
    AxisTrapCommand() = default;
    AxisTrapCommand(int crd, int axis, double pos, double vel, double a, short r)
        : MotionCommand(crd), axis(axis), posTarget(pos), velMax(vel), acc(a), rat(r) {}
    std::string getType() const override { return "AXIS_TRAP"; }
    std::string getDescription() const override;
    void saveParameters(std::ostream& os) const override;
    bool loadParameters(std::istream& is) override;
protected:
    bool apiSetMode() override;
    bool apiSetParams() override;
    bool apiStartMove() override;
};

class DelayCommand : public Command {
    int milliseconds = 0;
    std::chrono::steady_clock::time_point startTime;
    bool waiting = false;       // 是否已经开始等待
    bool checkingMotion = true; // 是否正在检查前序运动是否完成

    // 延时指令的执行状态序列
    enum class DelayStep {
        CHECK_MOTION,  // 检查前序运动是否完成
        START_DELAY,  // 开始延时
        WAIT_DELAY    // 等待延时完成
    };
    DelayStep currentStep = DelayStep::CHECK_MOTION;

public:
    DelayCommand() = default;
    DelayCommand(int ms) : milliseconds(ms) {}
    std::string getType() const override { return "DELAY"; }
    std::string getDescription() const override;
    ExecutionStatus execute(ExecutionContext& context) override;
    void saveParameters(std::ostream& os) const override;
    bool loadParameters(std::istream& is) override;
};

// --- 错误信息结构体 ---
struct ErrorInfo {
    size_t lineNumber = 0;                   // 行号 (0-based)
    std::string commandDescription = "N/A";  // 指令描述
    std::string errorMessage = "Unknown error"; // 错误消息
    bool hasErrorCode = false;                // 是否有错误码
    int errorCode = 0;                        // 错误码

    // 构造函数
    ErrorInfo(size_t line, std::string cmdDesc, std::string msg, int code = 0, bool hasCode = false)
        : lineNumber(line), commandDescription(cmdDesc), errorMessage(msg),
          hasErrorCode(hasCode), errorCode(code) {}
};

// --- 执行上下文 ---
class ExecutionContext {
public:
    size_t programCounter = 0;                      // 程序计数器
    std::vector<std::unique_ptr<Command>>& program; // 程序引用
    std::atomic<bool> isMotionActive{false};        // 运动状态标志
    std::atomic<bool> stopRequested{false};         // 停止请求标志
    std::chrono::steady_clock::time_point motionCompleteTime; // 运动完成时间点
    std::atomic<bool> isStabilizing{false};         // 稳定期标志

    // 稳定时间常量（毫秒）
    static constexpr int STABILIZATION_TIME_MS = 100;

    ExecutionContext(std::vector<std::unique_ptr<Command>>& prog) : program(prog) {}

    // 检查运动是否完成（调用实际API）
    bool checkMotionComplete();

    // 检查运动是否完成并稳定
    bool checkMotionCompleteAndStable();

    // 重置运动状态
    void resetMotionState();

    // 设置运动完成时间点
    void setMotionCompleteTime();
};

// --- 程序执行器 ---
class ProgramExecutor {
public:
    // 执行器状态
    enum class State { STOPPED, RUNNING_AUTO, HALTED_ERROR };
    // 执行模式
    enum class ExecutionMode { AUTO, MANUAL };
    // 回调定义
    using StatusUpdateCallback = std::function<void(State, size_t)>;
    using ErrorCallback = std::function<void(const ErrorInfo&)>;

    // 构造与析构
    ProgramExecutor(StatusUpdateCallback statusCb, ErrorCallback errorCb);
    ~ProgramExecutor();

    // 文件与程序管理
    bool loadProgramFromFile(const std::string& filePath); // 加载程序
    bool saveProgramToFile(const std::string& filePath); // 保存程序
    void setProgram(std::vector<std::unique_ptr<Command>> newProgram); // 设置程序 (UI编辑后调用)
    const std::vector<std::unique_ptr<Command>>& getProgram() const { return program; } // 获取程序引用

    // 执行控制
    void start(ExecutionMode mode); // 启动
    void requestStop();             // 请求停止
    void setExecutionMode(ExecutionMode mode); // 设置执行模式

    // 状态获取
    State getCurrentState() const;
    size_t getCurrentLine() const;
    ExecutionMode getCurrentMode() const;

private:
    std::atomic<State> currentState{State::STOPPED};           // 当前状态
    std::atomic<ExecutionMode> currentMode{ExecutionMode::AUTO}; // 当前模式
    std::vector<std::unique_ptr<Command>> program;             // 程序指令列表
    ExecutionContext context;                         // 执行上下文 (初始化引用)

    std::thread executionThread;                             // 后台执行线程
    std::mutex stateMutex;                                   // 互斥锁，保护复杂状态转换和条件变量
    std::condition_variable stepCv;                          // 条件变量，用于线程同步
    std::atomic<bool> stopRequested{false};                  // 停止请求标志

    StatusUpdateCallback statusUpdateCallback;               // 状态更新回调
    ErrorCallback errorCallback;                             // 错误报告回调

    void runLoop();                                          // 主执行循环函数
    bool createCommandByType(const std::string& type, std::unique_ptr<Command>& cmd); // 根据类型创建指令对象
    void reportError(const ErrorInfo& errorInfo);            // 安全调用错误回调
    void notifyUI(State state, size_t pc);                   // 安全调用状态回调
};

#endif // PROGRAMEXECUTOR_H