# 代码变更记录

本文档记录了对安达运动控制系统的所有代码修改内容。

## 2023-09-12 功能增强

### 1. 添加Jog子界面功能

#### 修改文件: axiscontrolwidget.h
```cpp
// 添加Jog相关头文件
#include <QDoubleSpinBox>
#include <QPushButton>

// 添加Jog相关槽函数
void startJogPositive();
void startJogNegative();
void stopJog();
void updateJogParams();

// 添加Jog相关成员变量
bool m_isJogging;
int m_jogDirection; // 1: 正向, -1: 负向, 0: 停止

// 添加Jog控件成员变量
QDoubleSpinBox *m_spinJogSpeed;
QDoubleSpinBox *m_spinJogAcc;
QPushButton *m_btnJogPositive;
QPushButton *m_btnJogNegative;
QPushButton *m_btnJogParams;

// 添加创建Jog界面的函数
void createJogInterface();
```

#### 修改文件: axiscontrolwidget.cpp
```cpp
// 1. 在构造函数中添加Jog相关初始化
AxisControlWidget::AxisControlWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::AxisControlWidget),
    m_statusTimer(new QTimer(this)),
    m_isMoving(false),
    m_isJogging(false),
    m_jogDirection(0)
{
    // ...现有代码...
    
    // 添加Jog控制界面
    createJogInterface();
    
    // ...现有代码...
}

// 2. 添加创建Jog界面的函数实现
void AxisControlWidget::createJogInterface()
{
    // 创建一个Jog控制组
    QGroupBox *groupJog = new QGroupBox("Jog控制", this);
    
    // 创建网格布局
    QGridLayout *gridLayoutJog = new QGridLayout(groupJog);
    
    // 添加速度控制
    QLabel *labelSpeed = new QLabel("速度:", groupJog);
    m_spinJogSpeed = new QDoubleSpinBox(groupJog);
    m_spinJogSpeed->setRange(1, 1000);
    m_spinJogSpeed->setValue(50);
    m_spinJogSpeed->setSuffix(" mm/s");
    m_spinJogSpeed->setObjectName("spinJogSpeed");
    
    // 添加加速度控制
    QLabel *labelAcc = new QLabel("加速度:", groupJog);
    m_spinJogAcc = new QDoubleSpinBox(groupJog);
    m_spinJogAcc->setRange(10, 10000);
    m_spinJogAcc->setValue(500);
    m_spinJogAcc->setSuffix(" mm/s²");
    m_spinJogAcc->setObjectName("spinJogAcc");
    
    // 添加操作按钮
    QLabel *labelOperation = new QLabel("操作:", groupJog);
    QHBoxLayout *hboxButtons = new QHBoxLayout();
    
    m_btnJogParams = new QPushButton("获取参数", groupJog);
    m_btnJogParams->setObjectName("btnJogParams");
    
    m_btnJogNegative = new QPushButton("←", groupJog);
    m_btnJogNegative->setObjectName("btnJogNegative");
    m_btnJogNegative->setMinimumWidth(40);
    
    m_btnJogPositive = new QPushButton("→", groupJog);
    m_btnJogPositive->setObjectName("btnJogPositive");
    m_btnJogPositive->setMinimumWidth(40);
    
    hboxButtons->addWidget(m_btnJogParams);
    hboxButtons->addSpacing(20);
    hboxButtons->addWidget(m_btnJogNegative);
    hboxButtons->addWidget(m_btnJogPositive);
    
    // 添加控件到网格布局
    gridLayoutJog->addWidget(labelSpeed, 0, 0);
    gridLayoutJog->addWidget(m_spinJogSpeed, 0, 1);
    gridLayoutJog->addWidget(labelAcc, 1, 0);
    gridLayoutJog->addWidget(m_spinJogAcc, 1, 1);
    gridLayoutJog->addWidget(labelOperation, 2, 0);
    gridLayoutJog->addLayout(hboxButtons, 2, 1);
    
    // 将Jog控制组添加到主布局中，在"运动控制"和"实时状态"之间
    QVBoxLayout *mainLayout = qobject_cast<QVBoxLayout*>(layout());
    if (mainLayout) {
        // 找到"实时状态"组框的索引
        int statusGroupIndex = -1;
        for (int i = 0; i < mainLayout->count(); ++i) {
            QWidget *widget = mainLayout->itemAt(i)->widget();
            if (widget && widget->objectName() == "groupBox_2") {
                statusGroupIndex = i;
                break;
            }
        }
        
        if (statusGroupIndex != -1) {
            mainLayout->insertWidget(statusGroupIndex, groupJog);
        } else {
            mainLayout->addWidget(groupJog);
        }
    }
    
    // 连接信号槽
    connect(m_btnJogPositive, &QPushButton::pressed, this, &AxisControlWidget::startJogPositive);
    connect(m_btnJogPositive, &QPushButton::released, this, &AxisControlWidget::stopJog);
    connect(m_btnJogNegative, &QPushButton::pressed, this, &AxisControlWidget::startJogNegative);
    connect(m_btnJogNegative, &QPushButton::released, this, &AxisControlWidget::stopJog);
    connect(m_btnJogParams, &QPushButton::clicked, this, &AxisControlWidget::updateJogParams);
}

// 3. 添加Jog相关功能函数
void AxisControlWidget::startJogPositive()
{
    int axis = ui->comboAxis->currentIndex();
    if (m_isJogging || m_isMoving) {
        return;
    }
    
    m_isJogging = true;
    m_jogDirection = 1;
    
    double speed = m_spinJogSpeed->value();
    double acc = m_spinJogAcc->value();
    
    // 在实际应用中，这里应该调用底层API执行Jog操作
    QString status = QString("轴%1正在正向Jog，速度: %2 mm/s, 加速度: %3 mm/s²")
                   .arg(axis)
                   .arg(speed)
                   .arg(acc);
    ui->labelStatus->setText(status);
    
    // 更新UI状态
    ui->btnMoveAbs->setEnabled(false);
    ui->btnMoveRel->setEnabled(false);
    ui->btnDisable->setEnabled(false);
}

void AxisControlWidget::startJogNegative()
{
    int axis = ui->comboAxis->currentIndex();
    if (m_isJogging || m_isMoving) {
        return;
    }
    
    m_isJogging = true;
    m_jogDirection = -1;
    
    double speed = m_spinJogSpeed->value();
    double acc = m_spinJogAcc->value();
    
    // 在实际应用中，这里应该调用底层API执行Jog操作
    QString status = QString("轴%1正在负向Jog，速度: %2 mm/s, 加速度: %3 mm/s²")
                   .arg(axis)
                   .arg(speed)
                   .arg(acc);
    ui->labelStatus->setText(status);
    
    // 更新UI状态
    ui->btnMoveAbs->setEnabled(false);
    ui->btnMoveRel->setEnabled(false);
    ui->btnDisable->setEnabled(false);
}

void AxisControlWidget::stopJog()
{
    int axis = ui->comboAxis->currentIndex();
    if (!m_isJogging) {
        return;
    }
    
    // 在实际应用中，这里应该调用底层API停止Jog操作
    QString status = QString("轴%1已停止Jog").arg(axis);
    ui->labelStatus->setText(status);
    
    // 更新状态
    m_isJogging = false;
    m_jogDirection = 0;
    
    // 更新UI状态
    ui->btnMoveAbs->setEnabled(true);
    ui->btnMoveRel->setEnabled(true);
    ui->btnDisable->setEnabled(true);
}

void AxisControlWidget::updateJogParams()
{
    int axis = ui->comboAxis->currentIndex();
    
    // 在实际应用中，这里应该从底层API获取Jog参数
    // 模拟一些随机参数
    double speed = QRandomGenerator::global()->bounded(5, 100);
    double acc = QRandomGenerator::global()->bounded(900.0) + 100.0;
    
    m_spinJogSpeed->setValue(speed);
    m_spinJogAcc->setValue(acc);
    
    QString status = QString("已获取轴%1的Jog参数").arg(axis);
    ui->labelStatus->setText(status);
}

// 4. 修改updateAxisStatus函数，添加Jog状态更新
void AxisControlWidget::updateAxisStatus()
{
    // ...现有代码...
    
    } else if (m_isJogging) {
        // 模拟Jog移动，在实际应用中应该从硬件获取真实位置和速度
        double currentPos = ui->lcdPosition->value() + 
                          (m_jogDirection * m_spinJogSpeed->value() * 0.1); // 0.1秒的位移
        ui->lcdPosition->display(currentPos);
        ui->lcdSpeed->display(m_spinJogSpeed->value());
    } else {
        // 非移动状态，速度为0
        ui->lcdSpeed->display(0.0);
    }
}
```

### 2. 创建连接对话框

#### 新增文件: connectiondialog.h
```cpp
#ifndef CONNECTIONDIALOG_H
#define CONNECTIONDIALOG_H

#include <QDialog>
#include <QLineEdit>
#include <QSpinBox>
#include <QPushButton>
#include <QLabel>
#include <QGridLayout>
#include <QTimer>

class ConnectionDialog : public QDialog
{
    Q_OBJECT

public:
    explicit ConnectionDialog(QWidget *parent = nullptr);
    ~ConnectionDialog();

    // 获取连接信息
    QString getIpAddress() const;
    int getPort() const;
    bool isConnected() const;

public slots:
    // 连接断开操作
    void connectDevice();
    void disconnectDevice();
    void resetDevice();

private slots:
    // 状态更新
    void updateConnectionStatus();

private:
    // UI初始化
    void setupUi();

    // 成员控件
    QLineEdit *m_ipEdit;
    QSpinBox *m_portSpin;
    QPushButton *m_connectBtn;
    QPushButton *m_disconnectBtn;
    QPushButton *m_resetBtn;
    QLabel *m_statusLabel;

    // 状态
    bool m_connected;
    QTimer *m_statusTimer;
};

#endif // CONNECTIONDIALOG_H
```

#### 新增文件: connectiondialog.cpp
```cpp
#include "connectiondialog.h"
#include <QMessageBox>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QGroupBox>
#include <QRegExpValidator>
#include <QRegExp>

ConnectionDialog::ConnectionDialog(QWidget *parent)
    : QDialog(parent),
      m_connected(false),
      m_statusTimer(new QTimer(this))
{
    setWindowTitle("设备连接");
    setMinimumWidth(350);
    
    // 初始化UI
    setupUi();
    
    // 连接信号槽
    connect(m_connectBtn, &QPushButton::clicked, this, &ConnectionDialog::connectDevice);
    connect(m_disconnectBtn, &QPushButton::clicked, this, &ConnectionDialog::disconnectDevice);
    connect(m_resetBtn, &QPushButton::clicked, this, &ConnectionDialog::resetDevice);
    connect(m_statusTimer, &QTimer::timeout, this, &ConnectionDialog::updateConnectionStatus);
    
    // 启动定时器更新状态
    m_statusTimer->start(500);
    
    // 初始化状态
    updateConnectionStatus();
}

// ...实现其余接口和功能函数...
```

### 3. 修改mainwindow.cpp实现右键菜单功能

```cpp
void MainWindow::showContextMenu(const QPoint &pos)
{
    // 获取当前选中项
    QTreeWidgetItem *currentItem = ui->deviceTreeWidget->itemAt(pos);
    if (!currentItem) {
        return;
    }
    
    // 判断是控制器节点还是轴节点
    QTreeWidgetItem *rootItem = ui->deviceTreeWidget->topLevelItem(0);
    
    if (currentItem == rootItem) {
        // 运动控制器的右键菜单
        QMenu contextMenu(this);
        
        QAction *connectAction = contextMenu.addAction("连接");
        QAction *disconnectAction = contextMenu.addAction("断开");
        QAction *refreshAction = contextMenu.addAction("刷新");
        
        // 设置动作状态
        connectAction->setEnabled(!m_connected);
        disconnectAction->setEnabled(m_connected);
        
        // 连接信号槽
        connect(connectAction, &QAction::triggered, this, [this]() {
            // 创建连接对话框
            ConnectionDialog dialog(this);
            if (dialog.exec() == QDialog::Accepted) {
                // 如果连接成功，更新主界面状态
                if (dialog.isConnected()) {
                    connectDevice();
                }
            }
        });
        
        connect(disconnectAction, &QAction::triggered, this, &MainWindow::disconnectDevice);
        connect(refreshAction, &QAction::triggered, [this]() {
            QMessageBox::information(this, "刷新", "设备列表已刷新");
        });
        
        // 显示菜单
        contextMenu.exec(ui->deviceTreeWidget->mapToGlobal(pos));
    } else {
        // 轴的右键菜单
        QMenu contextMenu(this);
        
        QAction *enableAction = contextMenu.addAction("使能开");
        QAction *disableAction = contextMenu.addAction("使能关");
        
        // 只有在控制器连接时才能使能/禁用轴
        enableAction->setEnabled(m_connected);
        disableAction->setEnabled(m_connected);
        
        // 获取轴索引
        int axisIndex = rootItem->indexOfChild(currentItem);
        
        // 连接信号槽
        connect(enableAction, &QAction::triggered, [this, axisIndex]() {
            // 在实际应用中调用底层API使能轴
            QMessageBox::information(this, "使能轴", 
                                   QString("轴%1已成功使能").arg(axisIndex));
            
            // 更新轴图标
            QTreeWidgetItem *rootItem = ui->deviceTreeWidget->topLevelItem(0);
            if (rootItem && axisIndex >= 0 && axisIndex < rootItem->childCount()) {
                rootItem->child(axisIndex)->setIcon(0, style()->standardIcon(QStyle::SP_MediaPlay));
            }
        });
        
        connect(disableAction, &QAction::triggered, [this, axisIndex]() {
            // 在实际应用中调用底层API禁用轴
            QMessageBox::information(this, "禁用轴", 
                                   QString("轴%1已成功禁用").arg(axisIndex));
            
            // 更新轴图标
            QTreeWidgetItem *rootItem = ui->deviceTreeWidget->topLevelItem(0);
            if (rootItem && axisIndex >= 0 && axisIndex < rootItem->childCount()) {
                rootItem->child(axisIndex)->setIcon(0, style()->standardIcon(QStyle::SP_MediaStop));
            }
        });
        
        // 显示菜单
        contextMenu.exec(ui->deviceTreeWidget->mapToGlobal(pos));
    }
}
```

### 4. 修改项目文件

#### 修改文件: MotionControlApp.pro
```
# 添加新类
SOURCES += \
    # ...现有源文件...
    connectiondialog.cpp

HEADERS += \
    # ...现有头文件...
    connectiondialog.h
``` 