    #ifndef LUAPROGRAMWIDGET_H
#define LUAPROGRAMWIDGET_H

#include <QWidget>
#include <QTextEdit>
#include <QTabWidget>
#include <QPushButton>
#include <QComboBox>
#include <QLabel>
#include <QGroupBox>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QSplitter>
#include <QFileDialog>
#include <QMessageBox>
#include <QMenu>
#include <QAction>
#include <QToolButton>
#include <QStatusBar>
#include <QTimer>
#include <QListWidget>
#include <QScrollArea>
#include <QPlainTextEdit>
#include <QSyntaxHighlighter>
#include <QRegularExpression>
#include <QTextCharFormat>

#include "luaexecutor.h"
#include "admc_api_wrapper.h"
#include "unitconverter.h"

// Lua语法高亮器
class LuaSyntaxHighlighter : public QSyntaxHighlighter
{
    Q_OBJECT

public:
    explicit LuaSyntaxHighlighter(QTextDocument *parent = nullptr);

protected:
    void highlightBlock(const QString &text) override;

private:
    struct HighlightingRule
    {
        QRegularExpression pattern;
        QTextCharFormat format;
    };
    QVector<HighlightingRule> highlightingRules;

    QTextCharFormat keywordFormat;
    QTextCharFormat functionFormat;
    QTextCharFormat stringFormat;
    QTextCharFormat commentFormat;
    QTextCharFormat numberFormat;
};

// Lua程序编辑器
class LuaProgramWidget : public QWidget
{
    Q_OBJECT

public:
    explicit LuaProgramWidget(QWidget *parent = nullptr);
    ~LuaProgramWidget();

signals:
    void apiStatusChanged(const QString& message, bool isSuccess);

private slots:
    // 指令模板插入
    void onInsertTemplateButtonClicked();
    void onTemplateTypeChanged(int index);

    // 文件操作
    void onLoadButtonClicked();
    void onSaveButtonClicked();

    // 执行控制
    void onStartButtonClicked();
    void onStopButtonClicked();
    void onStartAllButtonClicked();
    void onStopAllButtonClicked();

    // 坐标系切换
    void onCoordTabChanged(int index);

    // Lua执行器回调
    void onStateChanged(LuaExecutor::CoordSystem coord, LuaExecutor::State state);
    void onErrorOccurred(LuaExecutor::CoordSystem coord, const QString& errorMessage);
    void onOutputProduced(LuaExecutor::CoordSystem coord, const QString& output);

    // 单位转换
    void onUnitTypeChanged(UnitType type);

private:
    // UI组件
    QTabWidget* m_coordTabWidget;                // 坐标系选项卡
    QPlainTextEdit* m_scriptEditors[2];          // 脚本编辑器
    LuaSyntaxHighlighter* m_highlighters[2];     // 语法高亮器
    QTextEdit* m_outputTextEdit;                 // 输出窗口
    QComboBox* m_templateTypeComboBox;           // 模板类型下拉框
    QPushButton* m_insertTemplateButton;         // 插入模板按钮
    QPushButton* m_loadButton;                   // 加载按钮
    QPushButton* m_saveButton;                   // 保存按钮
    QPushButton* m_startButton;                  // 启动按钮
    QPushButton* m_stopButton;                   // 停止按钮
    QPushButton* m_startAllButton;               // 启动所有按钮
    QPushButton* m_stopAllButton;                // 停止所有按钮
    QLabel* m_statusLabel[2];                    // 状态标签
    QLabel* m_errorLabel[2];                     // 错误标签

    // Lua执行器
    LuaExecutor* m_luaExecutor;

    // 当前坐标系
    LuaExecutor::CoordSystem m_currentCoord;

    // 当前模板类型
    QString m_currentTemplateType;

    // 当前文件路径
    QString m_currentFilePaths[2];

    // 单位转换器
    UnitConverter* m_unitConverter;

    // 初始化UI
    void setupUI();

    // 创建模板类型下拉框
    void createTemplateTypeComboBox();

    // 获取模板代码
    QString getTemplateCode(const QString& templateType);

    // 更新UI状态
    void updateUIState(LuaExecutor::CoordSystem coord, LuaExecutor::State state);

    // 添加输出文本
    void addOutputText(const QString& text, const QColor& color = Qt::black);

    // 清空输出窗口
    void clearOutput();

    // 文件操作
    bool loadScriptFromFile(LuaExecutor::CoordSystem coord, const QString& filePath);
    bool saveScriptToFile(LuaExecutor::CoordSystem coord, const QString& filePath);
};

#endif // LUAPROGRAMWIDGET_H
