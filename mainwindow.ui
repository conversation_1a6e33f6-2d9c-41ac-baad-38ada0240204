<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="minimumSize">
   <size>
    <width>800</width>
    <height>600</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>安达运动控制系统</string>
  </property>
  <widget class="QWidget" name="centralWidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QTabWidget" name="tabWidget"/>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menuBar">
   <widget class="QMenu" name="menuFile">
    <property name="title">
     <string>文件</string>
    </property>
    <addaction name="actionExit"/>
   </widget>
   <widget class="QMenu" name="menuView">
    <property name="title">
     <string>视图</string>
    </property>
    <addaction name="actionDeviceView"/>
    <addaction name="actionHomeView"/>
    <addaction name="actionJogView"/>
    <addaction name="actionStatusView"/>
    <addaction name="actionAxisControlView"/>
    <addaction name="actionInterpolationView"/>
    <addaction name="actionIOView"/>
    <addaction name="actionProgramView"/>
    <addaction name="actionOscilloscopeView"/>
   </widget>
   <widget class="QMenu" name="menuDevice">
    <property name="title">
     <string>设备</string>
    </property>
    <addaction name="actionConnect"/>
    <addaction name="actionDisconnect"/>
   </widget>
   <widget class="QMenu" name="menuHelp">
    <property name="title">
     <string>帮助</string>
    </property>
    <addaction name="actionAbout"/>
   </widget>
   <addaction name="menuFile"/>
   <addaction name="menuView"/>
   <addaction name="menuDevice"/>
   <addaction name="menuHelp"/>
  </widget>
  <widget class="QToolBar" name="mainToolBar">
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>

  </widget>
  <widget class="QStatusBar" name="statusBar"/>
  <widget class="QDockWidget" name="dockWidget">
   <property name="windowTitle">
    <string>设备列表</string>
   </property>
   <attribute name="dockWidgetArea">
    <number>1</number>
   </attribute>
   <widget class="QWidget" name="dockWidgetContents">
    <layout class="QVBoxLayout" name="verticalLayout_2">
     <item>
      <widget class="QTreeWidget" name="deviceTreeWidget">
       <column>
        <property name="text">
         <string notr="true">1</string>
        </property>
       </column>
      </widget>
     </item>
    </layout>
   </widget>
  </widget>
  <action name="actionExit">
   <property name="text">
    <string>退出</string>
   </property>
  </action>
  <action name="actionAbout">
   <property name="text">
    <string>关于</string>
   </property>
  </action>
  <action name="actionDeviceView">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>设备列表</string>
   </property>
  </action>
  <action name="actionConnect">
   <property name="icon">
    <iconset theme="network-wired"/>
   </property>
   <property name="text">
    <string>连接</string>
   </property>
  </action>
  <action name="actionDisconnect">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset theme="network-offline"/>
   </property>
   <property name="text">
    <string>断开</string>
   </property>
  </action>
  <action name="actionHomeView">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="icon">
    <iconset resource="resources.qrc">
     <normaloff>:/Resources/HomePlugin.ico</normaloff>:/Resources/HomePlugin.ico</iconset>
   </property>
   <property name="text">
    <string>回零</string>
   </property>
  </action>
  <action name="actionJogView">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="icon">
    <iconset resource="resources.qrc">
     <normaloff>:/Resources/TrapPlugin.ico</normaloff>:/Resources/TrapPlugin.ico</iconset>
   </property>
   <property name="text">
    <string>点位运动</string>
   </property>
  </action>
  <action name="actionStatusView">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="icon">
    <iconset resource="resources.qrc">
     <normaloff>:/Resources/AxisStsPlugin.ico</normaloff>:/Resources/AxisStsPlugin.ico</iconset>
   </property>
   <property name="text">
    <string>轴状态</string>
   </property>
  </action>
  <action name="actionAxisControlView">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="icon">
    <iconset resource="resources.qrc">
     <normaloff>:/Resources/JogPlugin.ico</normaloff>:/Resources/JogPlugin.ico</iconset>
   </property>
   <property name="text">
    <string>JOG运动</string>
   </property>
  </action>
  <action name="actionInterpolationView">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="icon">
    <iconset resource="resources.qrc">
     <normaloff>:/Resources/CrdPlugin.ico</normaloff>:/Resources/CrdPlugin.ico</iconset>
   </property>
   <property name="text">
    <string>插补</string>
   </property>
  </action>
  <action name="actionIOView">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="icon">
    <iconset resource="resources.qrc">
     <normaloff>:/Resources/DIDO.ico</normaloff>:/Resources/DIDO.ico</iconset>
   </property>
   <property name="text">
    <string>IO输入/输出</string>
   </property>
  </action>
  <action name="actionProgramView">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="icon">
    <iconset resource="resources.qrc">
     <normaloff>:/Resources/CapturePlugin.ico</normaloff>:/Resources/CapturePlugin.ico</iconset>
   </property>
   <property name="text">
    <string>编程界面</string>
   </property>
  </action>
  <action name="actionOscilloscopeView">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="icon">
    <iconset resource="resources.qrc">
     <normaloff>:/Resources/OscilloscopePlugin.ico</normaloff>:/Resources/OscilloscopePlugin.ico</iconset>
   </property>
   <property name="text">
    <string>示波器</string>
   </property>
  </action>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources>
  <include location="resources.qrc"/>
 </resources>
 <connections/>
</ui>