#ifndef INTERPOLATIONWIDGET_H
#define INTERPOLATIONWIDGET_H

#include <QWidget>
#include <QTimer>
#include <QElapsedTimer>
#include "admc_pci.h" // 包含 ADMC PCI 头文件
//#include "includeMotion/admc_info.h" // 包含 TCrdPrm 定义
#include "unitconverter.h" // 包含单位转换器

namespace Ui {
class InterpolationWidget;
}

class InterpolationWidget : public QWidget
{
    Q_OBJECT

signals:
    void apiStatusChanged(const QString& message, bool isSuccess);

public:
    explicit InterpolationWidget(QWidget *parent = nullptr);
    ~InterpolationWidget();

private slots:
    void on_setCrdPrmButton_clicked(); // 坐标系参数设置按钮槽函数
    void on_setLnPrmButton_clicked(); // 直线插补参数设置按钮槽函数
    void on_setArcPrmButton_clicked(); // 圆弧插补参数设置按钮槽函数（保留兼容性）
    void on_setArc3PointButton_clicked(); // 三点圆弧插补参数设置按钮槽函数
    void on_setArcRadiusButton_clicked(); // 半径圆弧插补参数设置按钮槽函数
    void on_setArcCenterButton_clicked(); // 中心圆弧插补参数设置按钮槽函数
    void on_startCrdButton_clicked(); // 启动运动按钮槽函数

    // 单位转换相关槽函数
    void onUnitTypeChanged(UnitType type);
    void onUnitSwitchClicked();

public slots:
    void on_setButton_clicked();

private:
    Ui::InterpolationWidget *ui;
    UnitConverter* m_unitConverter; // 单位转换器
    QElapsedTimer m_lastClickTimer; // 记录上次点击时间
    int m_cooldownPeriod; // 冷却时间（毫秒）

    // 更新单位显示
    void updateUnitDisplay();
    // 设置默认参数值
    void setDefaultParameters();
};

#endif // INTERPOLATIONWIDGET_H
