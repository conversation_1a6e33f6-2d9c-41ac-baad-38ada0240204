# Lua编程指南

## 1. Lua编程简介

Lua是一种轻量级、高效的脚本语言，设计用于嵌入应用程序中，以便进行配置、脚本编写和快速原型设计。在本系统中，Lua被用作运动控制和自动化的脚本语言，允许用户编写程序来控制运动、处理IO信号和管理变量。

### Lua的主要特点：

- 轻量级：核心引擎小，易于嵌入
- 高效：执行速度快
- 简单易学：语法简洁明了
- 灵活：支持多种编程范式
- 可扩展：可以方便地与C/C++集成

## 2. 系统中的Lua环境

在本系统中，Lua环境已经预先配置，并扩展了一系列特殊函数，用于运动控制、IO操作和变量管理。系统支持两个坐标系的脚本，可以同时或单独运行。

### 脚本执行流程：

1. 编写Lua脚本
2. 加载脚本到坐标系
3. 启动执行
4. 系统按顺序执行脚本中的指令
5. 脚本执行完成或手动停止

## 3. Lua基本语法

### 变量和数据类型

Lua是动态类型语言，变量不需要声明类型。

```lua
-- 变量赋值
local a = 10       -- 数字
local b = "hello"  -- 字符串
local c = true     -- 布尔值
local d = nil      -- 空值
```

### 运算符

```lua
-- 算术运算符
local sum = 10 + 5      -- 加法
local diff = 10 - 5     -- 减法
local product = 10 * 5  -- 乘法
local quotient = 10 / 5 -- 除法
local power = 10 ^ 2    -- 幂运算

-- 比较运算符
local equal = (10 == 10)       -- 等于
local not_equal = (10 ~= 5)    -- 不等于
local greater = (10 > 5)       -- 大于
local less = (5 < 10)          -- 小于
local greater_equal = (10 >= 10) -- 大于等于
local less_equal = (5 <= 5)    -- 小于等于

-- 逻辑运算符
local and_result = true and false  -- 与
local or_result = true or false    -- 或
local not_result = not false       -- 非
```

### 条件语句

```lua
-- if语句
if a > 10 then
    print("a大于10")
elseif a == 10 then
    print("a等于10")
else
    print("a小于10")
end
```

### 循环

```lua
-- for循环
for i = 1, 10, 1 do  -- 从1到10，步长为1
    print(i)
end

-- while循环
local i = 1
while i <= 10 do
    print(i)
    i = i + 1
end

-- repeat-until循环
local i = 1
repeat
    print(i)
    i = i + 1
until i > 10
```

### 函数

```lua
-- 函数定义
function add(a, b)
    return a + b
end

-- 函数调用
local result = add(5, 3)  -- result = 8
```

## 4. 系统特殊函数/指令

### 4.1 运动控制指令

#### 直线插补运动

```lua
-- 直线插补运动
-- 参数: x坐标, y坐标, 速度, 加速度
coord.move_linear(100, 100, 50, 10)
```

#### 轴点位运动

```lua
-- 轴点位运动
-- 参数: 轴号, 目标位置, 最大速度, 加速度, 加速度比例
axis.move_trap(0, 100, 50, 10, 1)
```

#### 等待运动完成

```lua
-- 等待运动完成
wait_motion_complete()
```

### 4.2 IO操作指令

#### 设置IO输出

```lua
-- 设置IO输出
-- 参数: IO编号, 输出值("true"或"false")
set_io_output(1, "true")   -- 设置IO 1为高电平
set_io_output(2, "false")  -- 设置IO 2为低电平
```

#### 等待IO输入

```lua
-- 等待IO输入
-- 参数: IO编号, 期望值("true"或"false")
wait("input", 1, "true")   -- 等待IO 1变为高电平
wait("input", 2, "false")  -- 等待IO 2变为低电平
```

### 4.3 变量操作指令

#### 设置共享变量

```lua
-- 设置共享变量
-- 参数: 变量名, 变量值
set_shared_variable("counter", 10)
set_shared_variable("status", "ready")
set_shared_variable("enabled", true)
```

#### 获取共享变量

```lua
-- 获取共享变量
-- 参数: 变量名
local counter = get_shared_variable("counter")
local status = get_shared_variable("status")
local enabled = get_shared_variable("enabled")
```

#### 等待共享变量满足条件

```lua
-- 等待共享变量满足条件
-- 参数: 变量名, 操作符, 期望值
wait("var", "counter", "==", 10)  -- 等待counter等于10
wait("var", "counter", ">", 5)    -- 等待counter大于5
wait("var", "counter", "<", 20)   -- 等待counter小于20
wait("var", "counter", ">=", 10)  -- 等待counter大于等于10
wait("var", "counter", "<=", 10)  -- 等待counter小于等于10
wait("var", "counter", "!=", 0)   -- 等待counter不等于0
```

### 4.4 其他指令

#### 延时

```lua
-- 延时
-- 参数: 毫秒数
delay(1000)  -- 延时1秒
```

#### 打印输出

```lua
-- 打印输出
-- 参数: 消息
print("Hello, World!")
print("计数器值: " .. counter)  -- 字符串连接使用..
```

## 5. 编程示例

### 示例1: 简单的点位运动

```lua
-- 简单的点位运动示例
print("开始执行点位运动")

-- 设置IO输出
set_io_output(1, "true")

-- 执行点位运动
axis.move_trap(0, 100, 50, 10, 1)
wait_motion_complete()

-- 延时1秒
delay(1000)

-- 返回原点
axis.move_trap(0, 0, 50, 10, 1)
wait_motion_complete()

-- 设置IO输出
set_io_output(1, "false")

print("点位运动执行完成")
```

### 示例2: 等待IO信号并执行运动

```lua
-- 等待IO信号并执行运动示例
print("等待IO信号")

-- 等待IO 1变为高电平
wait("input", 1, "true")

print("收到IO信号，开始执行运动")

-- 执行直线插补运动
coord.move_linear(100, 100, 50, 10)
wait_motion_complete()

-- 设置共享变量
set_shared_variable("position_reached", true)

print("运动执行完成")
```

### 示例3: 使用循环和条件执行多段运动

```lua
-- 使用循环和条件执行多段运动示例
print("开始执行多段运动")

-- 初始化计数器
set_shared_variable("counter", 0)

-- 循环执行5次
for i = 1, 5 do
    print("执行第" .. i .. "次运动")
    
    -- 根据计数器值选择不同的运动
    local counter = get_shared_variable("counter")
    
    if counter < 3 then
        -- 执行直线插补运动
        coord.move_linear(i * 20, i * 20, 50, 10)
    else
        -- 执行点位运动
        axis.move_trap(0, i * 20, 50, 10, 1)
    end
    
    wait_motion_complete()
    
    -- 更新计数器
    set_shared_variable("counter", counter + 1)
    
    -- 延时0.5秒
    delay(500)
end

print("多段运动执行完成")
```

## 6. 注意事项

1. 脚本执行是顺序的，一条指令执行完成后才会执行下一条指令。
2. 使用`wait_motion_complete()`等待运动完成，否则下一条运动指令可能会报错。
3. 共享变量可以在不同坐标系的脚本之间共享数据。
4. IO操作和变量操作可以用于实现复杂的逻辑控制。
5. 使用`print()`函数可以在输出窗口显示调试信息。



--------

![alt text](image.png)

![alt text](image-1.png)

![alt text](image-2.png)
