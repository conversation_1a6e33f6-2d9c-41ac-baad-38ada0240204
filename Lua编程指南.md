# Lua运动控制编程指南

## 1. 简介

Lua是一种轻量级、高效的脚本语言，在我们的运动控制系统中被用作用户编程接口。通过Lua脚本，您可以方便地控制运动系统的各种功能，包括直线插补、圆弧插补和轴点位运动等。

本系统支持两个独立的坐标系（coord1和coord2），每个坐标系可以独立运行Lua脚本。坐标系之间可以通过共享变量进行通信。

## 2. Lua基础语法

### 2.1 变量定义

Lua是动态类型语言，变量不需要声明类型。变量名可以使用字母、数字和下划线，但不能以数字开头。

```lua
-- 全局变量
x = 10
y = 20.5
name = "运动控制"

-- 局部变量（推荐使用）
local speed = 100
local isEnabled = true
```

### 2.2 数据类型

Lua支持以下基本数据类型：

- `nil`：表示无效值或空值
- `boolean`：布尔值，`true`或`false`
- `number`：数值（双精度浮点数）
- `string`：字符串
- `table`：表（可用作数组、字典等）
- `function`：函数

```lua
-- 数据类型示例
local a = nil        -- 空值
local b = true       -- 布尔值
local c = 42         -- 数值
local d = "hello"    -- 字符串
local e = {1, 2, 3}  -- 表（数组）
local f = {x=10, y=20} -- 表（字典）
```

### 2.3 运算符

Lua支持常见的算术、比较和逻辑运算符：

- 算术运算符：`+`, `-`, `*`, `/`, `%`（取模）, `^`（幂）
- 比较运算符：`==`, `~=`（不等于）, `>`, `<`, `>=`, `<=`
- 逻辑运算符：`and`, `or`, `not`

```lua
-- 运算符示例
local a = 10 + 5     -- 15
local b = 10 * 5     -- 50
local c = 10 / 3     -- 3.3333...
local d = 10 % 3     -- 1
local e = 2 ^ 3      -- 8

local f = (a > b)    -- false
local g = (a ~= b)   -- true

local h = true and false  -- false
local i = true or false   -- true
local j = not true        -- false
```

### 2.4 注释

Lua支持单行注释和多行注释：

```lua
-- 这是单行注释

--[[
这是
多行
注释
--]]
```

## 3. 控制结构

### 3.1 条件语句

```lua
-- if-then-else语句
if x > 0 then
    print("x是正数")
elseif x < 0 then
    print("x是负数")
else
    print("x是零")
end
```

### 3.2 循环语句

```lua
-- while循环
local i = 1
while i <= 5 do
    print(i)
    i = i + 1
end

-- for循环（数值）
for i = 1, 5 do
    print(i)
end

-- for循环（带步长）
for i = 10, 1, -2 do  -- 从10到1，步长为-2
    print(i)
end

-- repeat-until循环
local i = 1
repeat
    print(i)
    i = i + 1
until i > 5
```

### 3.3 break和return

```lua
-- break示例
for i = 1, 10 do
    if i > 5 then
        break  -- 跳出循环
    end
    print(i)
end

-- return示例
function max(a, b)
    if a > b then
        return a
    else
        return b
    end
end
```

## 4. 函数

### 4.1 函数定义

```lua
-- 全局函数
function add(a, b)
    return a + b
end

-- 局部函数
local function multiply(a, b)
    return a * b
end

-- 匿名函数
local square = function(x)
    return x * x
end
```

### 4.2 函数调用

```lua
local result1 = add(5, 3)       -- 8
local result2 = multiply(5, 3)  -- 15
local result3 = square(5)       -- 25
```

## 5. 运动指令

### 5.1 直线插补

```lua
-- 直线插补
-- 参数：x, y, 速度, 加速度
coord.move_linear(100, 100, 50, 10)
```

### 5.2 圆弧插补

```lua
-- 圆弧插补
-- 参数：x, y, 半径, 方向(0:顺时针, 1:逆时针), 速度, 加速度, 终点速度
coord.move_arc(100, 100, 50, 1, 50, 10, 0)
```

### 5.3 轴点位运动

```lua
-- 轴点位运动
-- 参数：轴号, 位置, 速度, 加速度, 加速度比例
coord.move_axis(0, 100, 50, 10, 1.0)
```

### 5.4 延时函数

```lua
-- 延时（毫秒）
delay(1000)  -- 延时1秒
```

### 5.5 等待运动完成

```lua
-- 等待当前运动完成
wait_motion_complete()
```

## 6. 共享变量

### 6.1 设置共享变量

```lua
-- 设置共享变量
set_shared_variable("position", 100)
set_shared_variable("status", "running")
```

### 6.2 获取共享变量

```lua
-- 获取共享变量
local position = get_shared_variable("position")
local status = get_shared_variable("status")
```

## 7. 示例程序

### 7.1 简单的点位运动

```lua
-- 简单的点位运动程序
coord.move_linear(100, 0, 50, 10)    -- 移动到(100, 0)
coord.move_linear(100, 100, 50, 10)  -- 移动到(100, 100)
coord.move_linear(0, 100, 50, 10)    -- 移动到(0, 100)
coord.move_linear(0, 0, 50, 10)      -- 移动到(0, 0)
```

### 7.2 使用循环创建重复运动

```lua
-- 使用循环创建方形轨迹
local points = {
    {100, 0},
    {100, 100},
    {0, 100},
    {0, 0}
}

for i = 1, #points do
    local x = points[i][1]
    local y = points[i][2]
    coord.move_linear(x, y, 50, 10)
end
```

### 7.3 使用条件语句实现分支运动

```lua
-- 根据共享变量选择不同的运动路径
local mode = get_shared_variable("mode")

if mode == "square" then
    -- 方形轨迹
    coord.move_linear(100, 0, 50, 10)
    coord.move_linear(100, 100, 50, 10)
    coord.move_linear(0, 100, 50, 10)
    coord.move_linear(0, 0, 50, 10)
elseif mode == "circle" then
    -- 圆形轨迹
    local radius = 50
    local center_x = 50
    local center_y = 50
    
    -- 先移动到起点
    coord.move_linear(center_x + radius, center_y, 50, 10)
    
    -- 画圆
    coord.move_arc(center_x - radius, center_y, radius, 1, 50, 10, 0)
    coord.move_arc(center_x + radius, center_y, radius, 1, 50, 10, 0)
end
```

### 7.4 坐标系间协作

```lua
-- 坐标系1脚本
set_shared_variable("coord1_ready", true)
while not get_shared_variable("coord2_ready") do
    delay(10)  -- 等待坐标系2就绪
end

-- 开始协同运动
coord.move_linear(100, 0, 50, 10)
set_shared_variable("coord1_pos1_done", true)

-- 等待坐标系2完成第一段运动
while not get_shared_variable("coord2_pos1_done") do
    delay(10)
end

-- 继续下一段运动
coord.move_linear(100, 100, 50, 10)
```
