#ifndef AXISINITWIDGET_H
#define AXISINITWIDGET_H

#include <QWidget>
#include <QSpinBox>
#include <QComboBox>
#include <QPushButton>
#include <QLabel>
#include <QGridLayout>
#include <QGroupBox>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QMessageBox>
#include "admc_api_wrapper.h"
#include "unitconverter.h"

class AxisInitWidget : public QWidget
{
    Q_OBJECT

public:
    explicit AxisInitWidget(QWidget *parent = nullptr);
    ~AxisInitWidget();

signals:
    void apiStatusChanged(const QString& message, bool isSuccess);

private slots:
    void onSetButtonClicked();
    void onUnitTypeChanged(UnitType type);
    void updateUnitDisplay();
    void setDefaultParameters();

private:
    // 坐标系选择
    QLabel* m_labelCrd;
    QComboBox* m_comboCrd;

    // AxisMap (轴映射)
    QLabel* m_labelAxisMap;
    QSpinBox* m_spinAxisMapX;
    QSpinBox* m_spinAxisMapY;

    // AxisDir (轴方向)
    QLabel* m_labelAxisDir;
    QSpinBox* m_spinAxisDirX;
    QSpinBox* m_spinAxisDirY;

    // VelMax (最大速度)
    QLabel* m_labelVelMax;
    QSpinBox* m_spinVelMaxX;
    QSpinBox* m_spinVelMaxY;

    // AccMax (最大加速度)
    QLabel* m_labelAccMax;
    QSpinBox* m_spinAccMaxX;
    QSpinBox* m_spinAccMaxY;

    // Positive (正向限位)
    QLabel* m_labelPositive;
    QSpinBox* m_spinPositiveX;
    QSpinBox* m_spinPositiveY;

    // Negative (负向限位)
    QLabel* m_labelNegative;
    QSpinBox* m_spinNegativeX;
    QSpinBox* m_spinNegativeY;

    // 设置按钮
    QPushButton* m_btnSet;

    // 初始化UI
    void setupUI();

    // 单位转换器
    UnitConverter* m_unitConverter;
};

#endif // AXISINITWIDGET_H
