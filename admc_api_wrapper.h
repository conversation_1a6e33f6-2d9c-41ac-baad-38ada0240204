#ifndef ADMC_API_WRAPPER_H
#define ADMC_API_WRAPPER_H

#include <QString>
#include <QObject>
#include "admc_pci.h"

// API包装类，用于统一管理ADMC_PCI的API调用
class AdmcApiWrapper : public QObject
{
    Q_OBJECT

public:
    // 获取单例实例
    static AdmcApiWrapper* getInstance();

    // 删除拷贝构造和赋值操作符
    AdmcApiWrapper(const AdmcApiWrapper&) = delete;
    AdmcApiWrapper& operator=(const AdmcApiWrapper&) = delete;

    // 连接操作
    short openBoard(const QString& ip, int port);
    short closeBoard();
    short resetBoard();
    void deleteBoard(); // 新增：删除板卡句柄

    // 轴操作
    short axisOn(short axis);
    short axisOff(short axis);
    short getAxisPosition(short axis, double& position);
    short getAxisStatus(short axis, short& status);

    // 坐标系位置获取
    short getCrdPos(short crd, double* pPos);

    // Jog操作
    short setJogMode(short crd);
    short setJogParameters(short crd, int32_t Maxvel, int32_t acc, int32_t dec, int32_t rate);
    short jogUpdate(short axis, short dir);

    // 点位运动操作
    short setCrdTrapMode(short crd);
    short setCrdTrapParameters(short crd, double posTarget[2], double velMax, double acc, short rat);
    short crdTrapUpdate(short crd);

    // 直线插补
    short ln(
        short crd,
        int32_t x,
        int32_t y,
        double synVel,
        double synAcc,
        double velEnd
    );

    // 圆弧插补 - 半径圆弧
    short ArcXYR(
        short crd,
        int32_t x,
        int32_t y,
        double radius,
        short circleDir,
        double synVel,
        double synAcc,
        double velEnd
    );

    // 圆弧插补 - 三点圆弧
    short ArcXY_3point(
        short crd,
        int32_t* p1,
        int32_t* p2,
        int32_t* p3,
        double radius,
        short circleDir,
        double synVel,
        double synAcc
    );

    // 圆弧插补 - 中心圆弧
    short ArcXYC(
        short crd,
        int32_t x,
        int32_t y,
        double xCenter,
        double yCenter,
        short circleDir,
        double synVel,
        double synAcc
    );

    // 启动坐标系插补运动
    short crdStart(short crd);


    // 轴点位运动
    short setAxisTrapMode(short axis);
    short setAxisTrapParameters(short axis, double IncrPos, double velMax, double acc, short rat);
    short axisTrapUpdate(short axis);

    // 回零操作
    short axisGoHome(short axis);

    // 报警处理
    short axisClearAlarm(short axis);
    short getAxisErrorCode(short axis, short& errorCode);

    // 错误处理

    // 坐标系参数设置
    short setCrdPrm(short crd, double synVelMax, double synAccMax);

    // 轴参数初始化
    short setAxisPrm(short crd, short* axisMap, short* axisDir, int32_t* velMax, int32_t* accMax, int32_t* positive, int32_t* negative);

    // 坐标系轴映射
    short setAxisMapping(short x1, short y1, short x2, short y2);

    // IO控制
    short setDeviceOutput(int* deviceOutput);
    short getDeviceInput(int32_t* deviceInput);

    // 获取底层句柄，仅用于直接调用底层API
    TADMotionConn* getHandle() const { return m_handle; }

    QString getErrorString(short errorCode);
    bool isConnected() const { return m_isConnected; }

signals:
    void connectionStatusChanged(bool connected);
    void axisStatusChanged(short axis, short status);
    void axisPositionChanged(short axis, double position);
    void errorOccurred(short errorCode, const QString& errorMessage);

private:
    // 单例实现
    explicit AdmcApiWrapper(QObject* parent = nullptr);
    ~AdmcApiWrapper();
    TADMotionConn* m_handle;
    static AdmcApiWrapper* m_instance;
    bool m_isConnected;
};

#endif // ADMC_API_WRAPPER_H
