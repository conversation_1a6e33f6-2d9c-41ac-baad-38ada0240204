<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>TrapWidget</class>
 <widget class="QWidget" name="TrapWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>点位运动</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="tabAxisTrap">
      <attribute name="title">
       <string>轴点位模式(单轴寸动)</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="QGroupBox" name="groupAxisSelect">
         <property name="title">
          <string>轴选择</string>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout">
          <item>
           <widget class="QLabel" name="labelAxis">
            <property name="text">
             <string>轴：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="comboAxis"/>
          </item>
          <item>
           <spacer name="horizontalSpacer">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="groupAxisTrapParams">
         <property name="title">
          <string>轴点位参数</string>
         </property>
         <layout class="QGridLayout" name="gridLayout">
          <item row="0" column="0">
           <widget class="QLabel" name="labelPosTarget">
            <property name="text">
             <string>目标位置(posTarget)：</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QDoubleSpinBox" name="spinPosTarget">
            <property name="minimum">
             <double>-100000.000000000000000</double>
            </property>
            <property name="maximum">
             <double>100000.000000000000000</double>
            </property>
            <property name="value">
             <double>100.000000000000000</double>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="labelVelMax">
            <property name="text">
             <string>最大速度(velMax)：</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QDoubleSpinBox" name="spinVelMax">
            <property name="suffix">
             <string> mm/s</string>
            </property>
            <property name="minimum">
             <double>0.100000000000000</double>
            </property>
            <property name="maximum">
             <double>10000.000000000000000</double>
            </property>
            <property name="value">
             <double>100.000000000000000</double>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QLabel" name="labelRat">
            <property name="text">
             <string>倍率(rat)：</string>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QSpinBox" name="spinRat">
            <property name="minimum">
             <number>1</number>
            </property>
            <property name="maximum">
             <number>100</number>
            </property>
            <property name="value">
             <number>1</number>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QLabel" name="labelAcc">
            <property name="text">
             <string>加速度(acc)：</string>
            </property>
           </widget>
          </item>
          <item row="3" column="1">
           <widget class="QDoubleSpinBox" name="spinAcc">
            <property name="suffix">
             <string> mm/s²</string>
            </property>
            <property name="minimum">
             <double>0.100000000000000</double>
            </property>
            <property name="maximum">
             <double>10000.000000000000000</double>
            </property>
            <property name="value">
             <double>500.000000000000000</double>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="groupAxisTrapControl">
         <property name="title">
          <string>轴点位控制</string>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_2">
          <item>
           <spacer name="horizontalSpacer_2">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="btnSetAxisTrapParams">
            <property name="text">
             <string>设置参数</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="btnStartAxisTrap">
            <property name="text">
             <string>启动运动</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tabCrdTrap">
      <attribute name="title">
       <string>坐标系点位模式</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_3">
       <item>
        <widget class="QGroupBox" name="groupCrdSelect">
         <property name="title">
          <string>坐标系选择</string>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_3">
          <item>
           <widget class="QLabel" name="labelCrdSelect">
            <property name="text">
             <string>坐标系：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="comboCrdSelect"/>
          </item>
          <item>
           <spacer name="horizontalSpacer_3">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="groupCrdTrapParams">
         <property name="title">
          <string>坐标系点位参数</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_3">
          <item row="0" column="0">
           <widget class="QLabel" name="labelPosTargetX">
            <property name="text">
             <string>X目标位置(posTarget[0])：</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QDoubleSpinBox" name="spinPosTargetX">
            <property name="minimum">
             <double>-100000.000000000000000</double>
            </property>
            <property name="maximum">
             <double>100000.000000000000000</double>
            </property>
            <property name="value">
             <double>100.000000000000000</double>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="labelPosTargetY">
            <property name="text">
             <string>Y目标位置(posTarget[1])：</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QDoubleSpinBox" name="spinPosTargetY">
            <property name="minimum">
             <double>-100000.000000000000000</double>
            </property>
            <property name="maximum">
             <double>100000.000000000000000</double>
            </property>
            <property name="value">
             <double>100.000000000000000</double>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QLabel" name="labelCrdVelMax">
            <property name="text">
             <string>最大速度(velMax)：</string>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QDoubleSpinBox" name="spinCrdVelMax">
            <property name="suffix">
             <string> mm/s</string>
            </property>
            <property name="minimum">
             <double>0.100000000000000</double>
            </property>
            <property name="maximum">
             <double>10000.000000000000000</double>
            </property>
            <property name="value">
             <double>100.000000000000000</double>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QLabel" name="labelCrdRat">
            <property name="text">
             <string>倍率(rat)：</string>
            </property>
           </widget>
          </item>
          <item row="3" column="1">
           <widget class="QSpinBox" name="spinCrdRat">
            <property name="minimum">
             <number>1</number>
            </property>
            <property name="maximum">
             <number>100</number>
            </property>
            <property name="value">
             <number>1</number>
            </property>
           </widget>
          </item>
          <item row="4" column="0">
           <widget class="QLabel" name="labelCrdAcc">
            <property name="text">
             <string>加速度(acc)：</string>
            </property>
           </widget>
          </item>
          <item row="4" column="1">
           <widget class="QDoubleSpinBox" name="spinCrdAcc">
            <property name="suffix">
             <string> mm/s²</string>
            </property>
            <property name="minimum">
             <double>0.100000000000000</double>
            </property>
            <property name="maximum">
             <double>10000.000000000000000</double>
            </property>
            <property name="value">
             <double>500.000000000000000</double>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="groupCrdTrapControl">
         <property name="title">
          <string>坐标系点位控制</string>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_4">
          <item>
           <spacer name="horizontalSpacer_4">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="btnSetCrdTrapParams">
            <property name="text">
             <string>设置参数</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="btnStartCrdTrap">
            <property name="text">
             <string>启动运动</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="groupCrdStatus">
         <property name="title">
          <string>坐标系状态</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_4">
          <item row="0" column="0">
           <widget class="QLabel" name="labelCrdPositionX">
            <property name="text">
             <string>X位置：</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QLCDNumber" name="lcdCrdPositionX">
            <property name="digitCount">
             <number>8</number>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QLabel" name="labelCrdUnitX">
            <property name="text">
             <string>mm</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="labelCrdPositionY">
            <property name="text">
             <string>Y位置：</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QLCDNumber" name="lcdCrdPositionY">
            <property name="digitCount">
             <number>8</number>
            </property>
           </widget>
          </item>
          <item row="1" column="2">
           <widget class="QLabel" name="labelCrdUnitY">
            <property name="text">
             <string>mm</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer_2">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupStatus">
     <property name="title">
      <string>状态</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_4">
      <item>
       <widget class="QLabel" name="labelStatus">
        <property name="frameShape">
         <enum>QFrame::Panel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Sunken</enum>
        </property>
        <property name="text">
         <string>就绪</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
