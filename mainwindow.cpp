#include "mainwindow.h"
#include "ui_mainwindow.h"
#include "homewidget.h"

#include "statuswidget.h"
#include "axiscontrolwidget.h"
#include "connectiondialog.h"
#include "interpolationwidget.h" // 包含插补窗口头文件
#include "trapwidget.h" // 包含点位运动窗口头文件
#include "iowidget.h" // 包含IO窗口头文件
#include "programwidget.h" // 包含程序编辑窗口头文件


#include <QMessageBox>
#include <QDateTime>
#include <QLabel>
#include <QDebug>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QGraphicsColorizeEffect>
#include <QGraphicsOpacityEffect>
#include <QSvgRenderer>
#include <QApplication>
#include <QDesktopWidget>
#include <QScreen>
MainWindow::MainWindow(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::MainWindow),
    m_settings(new QSettings("MotionControl.ini", QSettings::IniFormat)),
    m_statusTimer(new QTimer(this)),
    m_connected(false),
    m_statusWidget(nullptr),
    m_unitConverter(UnitConverter::getInstance())
{
    ui->setupUi(this);

    // 初始化界面
    setupUI();
    setupMenus();
    setupDeviceTree();
    setupStatusBar();
    setupTabWidgets();

    // 设置定时器更新状态栏
    connect(m_statusTimer, &QTimer::timeout, this, &MainWindow::updateStatusBar);
    m_statusTimer->start(1000); // 每秒更新一次轴位置

    // 连接单位转换器的信号
    connect(m_unitConverter, &UnitConverter::unitTypeChanged, this, &MainWindow::onUnitTypeChanged);

    // 设置窗口标题
    setWindowTitle("安达运动控制系统 v1.0");

    // 显示工具栏
    ui->mainToolBar->show();
}

MainWindow::~MainWindow()
{
    // 保存窗口设置
    m_settings->setValue("MainWindow/Size", size());
    m_settings->setValue("MainWindow/Position", pos());

    // 如果还在连接状态，断开连接
    if (m_connected) {
        AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();
        if (wrapper && wrapper->isConnected()) {
            wrapper->closeBoard();
        }
    }

    // 删除板卡句柄
    AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();
    if (wrapper) {
        wrapper->deleteBoard();
    }

    // 清理资源
    delete m_settings;
    delete ui;
}

void MainWindow::setupUI()
{
    // 获取屏幕信息
    QScreen *screen = QApplication::primaryScreen();
    QRect screenGeometry = screen->availableGeometry();

    // 计算合适的窗口尺寸（屏幕的80%，但不小于最小尺寸）
    int screenWidth = screenGeometry.width();
    int screenHeight = screenGeometry.height();

    int windowWidth = qMax(800, static_cast<int>(screenWidth * 0.8));
    int windowHeight = qMax(600, static_cast<int>(screenHeight * 0.8));

    // 确保窗口不会超过屏幕大小
    windowWidth = qMin(windowWidth, screenWidth - 100);
    windowHeight = qMin(windowHeight, screenHeight - 100);

    // 从设置恢复窗口大小和位置，如果没有设置则使用自适应大小
    if (m_settings->contains("MainWindow/Size")) {
        QSize savedSize = m_settings->value("MainWindow/Size").toSize();
        // 确保保存的尺寸不会超过当前屏幕大小
        savedSize.setWidth(qMin(savedSize.width(), screenWidth - 100));
        savedSize.setHeight(qMin(savedSize.height(), screenHeight - 100));
        resize(savedSize);
    } else {
        resize(windowWidth, windowHeight);
    }

    // 设置窗口位置：优先使用保存的位置，否则居中显示
    if (m_settings->contains("MainWindow/Position")) {
        QPoint savedPos = m_settings->value("MainWindow/Position").toPoint();
        // 确保窗口在屏幕范围内
        savedPos.setX(qMax(0, qMin(savedPos.x(), screenWidth - width())));
        savedPos.setY(qMax(0, qMin(savedPos.y(), screenHeight - height())));
        move(savedPos);
    } else {
        // 计算居中位置
        int x = (screenWidth - width()) / 2;
        int y = (screenHeight - height()) / 2;
        move(x, y);
    }

    // 设置最小尺寸，确保窗口可以缩放但不会太小
    setMinimumSize(800, 600);

    // 确保状态栏始终可见
    statusBar()->setVisible(true);
}



void MainWindow::setupMenus()
{
    // 连接菜单动作
    connect(ui->actionConnect, &QAction::triggered, this, &MainWindow::connectDevice);
    connect(ui->actionDisconnect, &QAction::triggered, this, &MainWindow::disconnectDevice);

    // 设置视图菜单项的初始状态
    ui->actionDeviceView->setChecked(true);
    ui->actionHomeView->setChecked(true);
    ui->actionJogView->setChecked(true);
    ui->actionStatusView->setChecked(true);
    ui->actionAxisControlView->setChecked(true);
    ui->actionInterpolationView->setChecked(true);
    ui->actionIOView->setChecked(true);
    ui->actionProgramView->setChecked(true);
    ui->actionOscilloscopeView->setChecked(true);

    // 连接视图菜单项的信号槽
    connect(ui->actionHomeView, &QAction::triggered, this, &MainWindow::on_actionHomeView_triggered);
    connect(ui->actionJogView, &QAction::triggered, this, &MainWindow::on_actionJogView_triggered);
    connect(ui->actionStatusView, &QAction::triggered, this, &MainWindow::on_actionStatusView_triggered);
    connect(ui->actionAxisControlView, &QAction::triggered, this, &MainWindow::on_actionAxisControlView_triggered);
    connect(ui->actionInterpolationView, &QAction::triggered, this, &MainWindow::on_actionInterpolationView_triggered);
    connect(ui->actionIOView, &QAction::triggered, this, &MainWindow::on_actionIOView_triggered);
    connect(ui->actionProgramView, &QAction::triggered, this, &MainWindow::on_actionProgramView_triggered);
    connect(ui->actionOscilloscopeView, &QAction::triggered, this, &MainWindow::on_actionOscilloscopeView_triggered);

    // 设置工具栏
    setupToolBar();
}

void MainWindow::setupDeviceTree()
{
    ui->deviceTreeWidget->setHeaderLabel("设备列表");

    // 添加树状设备列表
    QTreeWidgetItem *rootItem = new QTreeWidgetItem(ui->deviceTreeWidget);
    rootItem->setText(0, "运动控制器");
    rootItem->setIcon(0, style()->standardIcon(QStyle::SP_ComputerIcon));

    // 添加轴节点
    for (int i = 0; i < 4; ++i) {
        QTreeWidgetItem *axisItem = new QTreeWidgetItem(rootItem);
        axisItem->setText(0, QString("轴 %1").arg(i));
        axisItem->setIcon(0, style()->standardIcon(QStyle::SP_DriveNetIcon));
    }

    // 展开根节点
    rootItem->setExpanded(true);

    // 允许右键菜单
    ui->deviceTreeWidget->setContextMenuPolicy(Qt::CustomContextMenu);
    connect(ui->deviceTreeWidget, &QTreeWidget::customContextMenuRequested,
            this, &MainWindow::showContextMenu);
}

void MainWindow::setupStatusBar()
{
    // 添加状态信息
    QLabel *statusLabel = new QLabel("就绪");
    ui->statusBar->addWidget(statusLabel);

    // 添加API调用状态信息
    QLabel *apiStatusLabel = new QLabel("");
    apiStatusLabel->setObjectName("apiStatusLabel");
    apiStatusLabel->setMinimumWidth(300); // 设置最小宽度
    apiStatusLabel->setAlignment(Qt::AlignCenter); // 居中显示
    apiStatusLabel->setFrameStyle(QFrame::Panel | QFrame::Sunken); // 添加边框样式
    ui->statusBar->addWidget(apiStatusLabel);

    // 添加弹簧占位符，使连接状态和时间标签靠右显示
    QWidget* spacer = new QWidget();
    spacer->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Preferred);
    ui->statusBar->addWidget(spacer);

    // 添加轴位置显示标签（0-3轴）
    for (int i = 0; i < 4; ++i) {
        m_axisPositionLabels[i] = new QLabel(QString("轴%1: 0 pulse").arg(i));
        m_axisPositionLabels[i]->setObjectName(QString("axisPositionLabel%1").arg(i));
        m_axisPositionLabels[i]->setMinimumWidth(120); // 设置最小宽度
        m_axisPositionLabels[i]->setAlignment(Qt::AlignCenter); // 居中显示
        m_axisPositionLabels[i]->setFrameStyle(QFrame::Panel | QFrame::Sunken); // 添加边框样式
        ui->statusBar->addPermanentWidget(m_axisPositionLabels[i]);
    }

    // 添加连接状态
    QLabel *connLabel = new QLabel("未连接");
    connLabel->setObjectName("connStatusLabel");
    ui->statusBar->addPermanentWidget(connLabel);

    // 添加时间标签
    QLabel *timeLabel = new QLabel(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
    timeLabel->setObjectName("timeLabel");
    ui->statusBar->addPermanentWidget(timeLabel);
}

void MainWindow::setupTabWidgets()
{
    // 创建各功能模块，但不立即添加到标签页

    // 创建回零模块
    HomeWidget* homeWidget = new HomeWidget();
    connect(homeWidget, &HomeWidget::apiStatusChanged, this, &MainWindow::onHomeWidgetApiStatusChanged);

    // 创建点位运动模块
    TrapWidget* trapWidget = new TrapWidget();
    connect(trapWidget, &TrapWidget::apiStatusChanged, this, &MainWindow::showApiStatus);

    // 创建轴状态模块并保存引用
    m_statusWidget = new StatusWidget();

    // 连接轴状态模块的信号
    connect(m_statusWidget, &StatusWidget::axisAlarmStatusChanged, this, &MainWindow::onAxisAlarmStatusChanged);
    connect(m_statusWidget, &StatusWidget::axisPositionChanged, this, [this](short axis, double position) {
        // 当轴位置变化时更新底部状态栏的轴位置显示
        if (axis >= 0 && axis < 4) {
            // 根据当前单位类型显示位置
            QString unitStr = m_unitConverter->getPositionUnitString();
            QString posStr = m_unitConverter->formatPosition(position);
            m_axisPositionLabels[axis]->setText(QString("轴%1: %2 %3").arg(axis).arg(posStr).arg(unitStr));
        }
    });
    connect(m_statusWidget, &StatusWidget::axisStatusChanged, this, [this](short axis, short status) {
        // 当轴状态变化时更新设备树中的轴图标
        QTreeWidgetItem *rootItem = ui->deviceTreeWidget->topLevelItem(0);
        if (rootItem && axis >= 0 && axis < rootItem->childCount()) {
            // 检查使能状态(Bit 0)
            bool isEnabled = (status & (1 << 0)) != 0;

            // 根据使能状态更新图标颜色
            if (isEnabled) {
                // 设置绿色图标表示已使能
                rootItem->child(axis)->setIcon(0, style()->standardIcon(QStyle::SP_MediaPlay));
                rootItem->child(axis)->setForeground(0, QBrush(Qt::green));
            } else {
                // 设置正常图标表示未使能
                rootItem->child(axis)->setIcon(0, style()->standardIcon(QStyle::SP_MediaStop));
                rootItem->child(axis)->setForeground(0, QBrush(Qt::black));
            }
        }
    });

    // 创建JOG运动模块
    AxisControlWidget* axisControlWidget = new AxisControlWidget();

    // 创建插补模块
    InterpolationWidget* interpolationWidget = new InterpolationWidget();
    connect(interpolationWidget, &InterpolationWidget::apiStatusChanged, this, &MainWindow::showApiStatus);

    // 创建IO模块
    IOWidget* ioWidget = new IOWidget();
    connect(ioWidget, &IOWidget::apiStatusChanged, this, &MainWindow::onIOWidgetApiStatusChanged);

    // 创建程序编辑模块
    ProgramWidget* programWidget = new ProgramWidget();
    connect(programWidget, &ProgramWidget::apiStatusChanged, this, &MainWindow::showApiStatus);

    // 创建示波器模块
    OscilloscopeWidget* oscilloscopeWidget = new OscilloscopeWidget();
    connect(oscilloscopeWidget, &OscilloscopeWidget::apiStatusChanged, this, &MainWindow::showApiStatus);

    // 保存模块指针，以便后续使用
    m_moduleWidgets["回零"] = homeWidget;
    m_moduleWidgets["点位运动"] = trapWidget;
    m_moduleWidgets["轴状态"] = m_statusWidget;
    m_moduleWidgets["JOG运动"] = axisControlWidget;
    m_moduleWidgets["插补"] = interpolationWidget;
    m_moduleWidgets["IO输入/输出"] = ioWidget;
    m_moduleWidgets["编程界面"] = programWidget;
    m_moduleWidgets["示波器"] = oscilloscopeWidget;

    // 根据视图菜单的选中状态添加模块
    if (ui->actionHomeView->isChecked()) {
        m_homeTabIndex = ui->tabWidget->addTab(homeWidget, "回零");
    }

    if (ui->actionJogView->isChecked()) {
        m_trapTabIndex = ui->tabWidget->addTab(trapWidget, "点位运动");
    }

    if (ui->actionStatusView->isChecked()) {
        m_statusTabIndex = ui->tabWidget->addTab(m_statusWidget, "轴状态");
    }

    if (ui->actionAxisControlView->isChecked()) {
        m_axisControlTabIndex = ui->tabWidget->addTab(axisControlWidget, "JOG运动");
    }

    if (ui->actionInterpolationView->isChecked()) {
        m_interpolationTabIndex = ui->tabWidget->addTab(interpolationWidget, "插补");
    }

    if (ui->actionIOView->isChecked()) {
        m_ioTabIndex = ui->tabWidget->addTab(ioWidget, "IO输入/输出");
    }

    if (ui->actionProgramView->isChecked()) {
        m_programTabIndex = ui->tabWidget->addTab(programWidget, "编程界面");
    }

    if (ui->actionOscilloscopeView->isChecked()) {
        m_oscilloscopeTabIndex = ui->tabWidget->addTab(oscilloscopeWidget, "示波器");
    }

    // 更新工具栏高亮状态
    updateToolBarHighlight(ui->tabWidget->currentIndex());
}

void MainWindow::on_actionExit_triggered()
{
    close();
}

void MainWindow::on_actionAbout_triggered()
{
    QMessageBox::about(this, "关于", "安达运动控制系统 v1.0.4f81ad67\n\n"
                      "© 2025 安达智能装备科技有限公司");
}

void MainWindow::on_actionDeviceView_triggered(bool checked)
{
    ui->dockWidget->setVisible(checked);
}

void MainWindow::on_actionConnect_triggered()
{
    // 创建连接对话框
    ConnectionDialog dialog(this);
    if (dialog.exec() == QDialog::Accepted) {
        // 如果连接成功，更新主界面状态
        if (dialog.isConnected()) {
            connectDevice();
        }
    }
}

void MainWindow::on_actionDisconnect_triggered()
{
    disconnectDevice();
}

void MainWindow::showContextMenu(const QPoint &pos)
{
    // 获取当前选中项
    QTreeWidgetItem *currentItem = ui->deviceTreeWidget->itemAt(pos);
    if (!currentItem) {
        return;
    }

    // 判断是控制器节点还是轴节点
    QTreeWidgetItem *rootItem = ui->deviceTreeWidget->topLevelItem(0);

    if (currentItem == rootItem) {
        // 运动控制器的右键菜单
        QMenu contextMenu(this);

        QAction *connectAction = contextMenu.addAction("连接");
        QAction *resetAction = contextMenu.addAction("复位");
        QAction *disconnectAction = contextMenu.addAction("断开");
        QAction *refreshAction = contextMenu.addAction("刷新");

        // 设置动作状态
        connectAction->setEnabled(!m_connected);
        resetAction->setEnabled(m_connected);
        disconnectAction->setEnabled(m_connected);

        // 连接信号槽
        connect(connectAction, &QAction::triggered, this, [this]() {
            // 创建连接对话框
            ConnectionDialog dialog(this);
            if (dialog.exec() == QDialog::Accepted) {
                // 如果连接成功，更新主界面状态
                if (dialog.isConnected()) {
                    connectDevice();
                }
            }
        });

        connect(disconnectAction, &QAction::triggered, this, &MainWindow::disconnectDevice);

        // 连接复位按钮的信号槽
        connect(resetAction, &QAction::triggered, [this]() {
            // 获取API包装器实例
            AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();
            if (wrapper && wrapper->isConnected()) {
                // 显示复位确认对话框
                QMessageBox::StandardButton reply = QMessageBox::question(this, "复位确认",
                                                                      "确定要复位运动控制器吗？请在运动停止的状态下复位。",
                                                                      QMessageBox::Yes | QMessageBox::No);
                if (reply == QMessageBox::Yes) {
                    // 调用底层API复位板卡
                    short result = wrapper->resetBoard();
                    if (result == 0) {
                        // 复位成功
                        QMessageBox::information(this, "复位成功", "运动控制器已成功复位");
                        showApiStatus("运动控制器已成功复位", true);
                    } else {
                        // 复位失败
                        QMessageBox::warning(this, "错误",
                                           QString("复位运动控制器失败，错误码：%1").arg(result));
                        showApiStatus(QString("复位运动控制器失败，错误码：%1").arg(result), false);
                    }
                }
            } else {
                QMessageBox::warning(this, "错误", "设备未连接，无法复位");
            }
        });

        connect(refreshAction, &QAction::triggered, [this]() {
            QMessageBox::information(this, "刷新", "设备列表已刷新");
        });

        // 显示菜单
        contextMenu.exec(ui->deviceTreeWidget->mapToGlobal(pos));
    } else {
        // 轴的右键菜单
        QMenu contextMenu(this);

        QAction *enableAction = contextMenu.addAction("使能开");
        QAction *disableAction = contextMenu.addAction("使能关");
        QAction *clearAlarmAction = contextMenu.addAction("清除报警");

        // 只有在控制器连接时才能使能/禁用轴/清除报警
        enableAction->setEnabled(m_connected);
        disableAction->setEnabled(m_connected);
        clearAlarmAction->setEnabled(m_connected);

        // 获取轴索引
        int axisIndex = rootItem->indexOfChild(currentItem);

        // 连接信号槽
        connect(enableAction, &QAction::triggered, [this, axisIndex]() {
            // 获取API包装器实例
            AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();
            if (wrapper && wrapper->isConnected()) {
                // 调用底层API使能轴
                short result = wrapper->axisOn(axisIndex);
                if (result == 0) {
                    // 使能成功，更新轴图标
                    QTreeWidgetItem *rootItem = ui->deviceTreeWidget->topLevelItem(0);
                    if (rootItem && axisIndex >= 0 && axisIndex < rootItem->childCount()) {
                        rootItem->child(axisIndex)->setIcon(0, style()->standardIcon(QStyle::SP_MediaPlay));
                    }
                    QMessageBox::information(this, "使能轴",
                                           QString("轴%1已成功使能").arg(axisIndex));
                } else {
                    // 使能失败
                    QMessageBox::warning(this, "错误",
                                       QString("轴%1使能失败，错误码：%2").arg(axisIndex).arg(result));
                }
            }
        });

        connect(disableAction, &QAction::triggered, [this, axisIndex]() {
            // 获取API包装器实例
            AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();
            if (wrapper && wrapper->isConnected()) {
                // 调用底层API禁用轴
                short result = wrapper->axisOff(axisIndex);
                if (result == 0) {
                    // 禁用成功，更新轴图标
                    QTreeWidgetItem *rootItem = ui->deviceTreeWidget->topLevelItem(0);
                    if (rootItem && axisIndex >= 0 && axisIndex < rootItem->childCount()) {
                        rootItem->child(axisIndex)->setIcon(0, style()->standardIcon(QStyle::SP_MediaStop));
                    }
                    QMessageBox::information(this, "禁用轴",
                                           QString("轴%1已成功禁用").arg(axisIndex));
                } else {
                    // 禁用失败
                    QMessageBox::warning(this, "错误",
                                       QString("轴%1禁用失败，错误码：%2").arg(axisIndex).arg(result));
                }
            }
        });

        // 添加清除报警操作
        connect(clearAlarmAction, &QAction::triggered, [this, axisIndex]() {
            // 获取API包装器实例
            AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();
            if (wrapper && wrapper->isConnected()) {
                // 调用底层API清除报警
                short result = wrapper->axisClearAlarm(axisIndex);
                if (result == 0) {
                    // 清除报警成功
                    QMessageBox::information(this, "清除报警",
                                           QString("轴%1的报警已成功清除").arg(axisIndex));

                    // 重置轴位置标签的背景色
                    if (axisIndex >= 0 && axisIndex < 4) {
                        m_axisPositionLabels[axisIndex]->setStyleSheet("");
                    }
                } else {
                    // 清除报警失败
                    QMessageBox::warning(this, "错误",
                                       QString("轴%1清除报警失败，错误码：%2").arg(axisIndex).arg(result));
                }
            }
        });

        // 显示菜单
        contextMenu.exec(ui->deviceTreeWidget->mapToGlobal(pos));
    }
}

void MainWindow::onAxisAlarmStatusChanged(short axis, bool hasAlarm)
{
    // 当轴报警状态变化时更新轴位置标签的背景色
    if (axis >= 0 && axis < 4) {
        if (hasAlarm) {
            // 设置红色背景
            m_axisPositionLabels[axis]->setStyleSheet("background-color: red; color: white;");
        } else {
            // 恢复正常背景
            m_axisPositionLabels[axis]->setStyleSheet("");
        }
    }
}

void MainWindow::updateStatusBar()
{
    // 更新时间
    QLabel *timeLabel = findChild<QLabel*>("timeLabel");
    if (timeLabel) {
        timeLabel->setText(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
    }
}

void MainWindow::updateAxisPositions()
{
    // 如果轴状态模块存在，则手动触发其刷新
    if (m_statusWidget) {
        m_statusWidget->refreshStatus();
    } else {
        // 未连接状态下显示零位置
        for (int i = 0; i < 4; ++i) {
            QString unitStr = m_unitConverter->getPositionUnitString();
            m_axisPositionLabels[i]->setText(QString("轴%1: 0 %2").arg(i).arg(unitStr));
        }
    }
}

void MainWindow::showApiStatus(const QString& message, bool isSuccess)
{
    QLabel *apiStatusLabel = findChild<QLabel*>("apiStatusLabel");
    if (apiStatusLabel) {
        apiStatusLabel->setText(message);

        // 根据成功或失败设置不同的样式
        if (isSuccess) {
            apiStatusLabel->setStyleSheet("background-color: #d4edda; color: #155724;"); // 绿色背景，深绿色文字
        } else {
            apiStatusLabel->setStyleSheet("background-color: #f8d7da; color: #721c24;"); // 红色背景，深红色文字
        }

        // 消息保持显示，直到被新消息覆盖或用户手动清除
    }
}

void MainWindow::setupToolBar()
{
    // 清除工具栏
    ui->mainToolBar->clear();

    // 设置工具栏样式，使其看起来像标签栏
    ui->mainToolBar->setStyleSheet(
        "QToolBar {"
        "   background-color: #f0f0f0;"
        "   border-bottom: 1px solid #c0c0c0;"
        "   spacing: 0px;"
        "}"
        "QWidget#moduleWidget {"
        "   padding: 5px;"
        "   margin: 0px;"
        "   border: none;"
        "}"
        "QWidget#moduleWidget[active=\"true\"] {"
        "   background-color: #e0e0e0;"
        "   border-bottom: 2px solid #2196F3;"
        "}"
        "QLabel[enabled=\"false\"] {"
        "   opacity: 0.5;"
        "}"
    );

    // 创建自定义工具栏，使用标签显示图标和文本
    QWidget* toolBarWidget = new QWidget();
    QHBoxLayout* layout = new QHBoxLayout(toolBarWidget);
    layout->setSpacing(0);
    layout->setContentsMargins(0, 0, 0, 0);

    // 创建图标和文本标签
    QStringList moduleNames = {"回零", "点位运动", "轴状态", "JOG运动", "插补", "IO输入/输出", "编程界面", "示波器"};
//    QStringList iconPaths = {
//        ":/Resources/apple-home-button-svg.ico",
//        ":/Resources/apple-point-motion-svg.ico",
//        ":/Resources/apple-axis-status-detailed-svg.ico",
//        ":/Resources/apple-jog-motion-svg.ico",
//        ":/Resources/apple-interpolation-svg.ico",
//        ":/Resources/apple-io-button-svg.ico",
//        ":/Resources/CapturePlugin.ico"
//    };
    QStringList iconPaths = {
        ":/Resources/apple-home-button-svg.svg",
        ":/Resources/apple-point-motion-svg.svg",
        ":/Resources/apple-axis-status-detailed-svg.svg",
        ":/Resources/apple-jog-motion-svg.svg",
        ":/Resources/apple-interpolation-svg",
        ":/Resources/apple-io-button-svg.svg",
        ":/Resources/apple-programming-interface-svg.svg",
        ":/Resources/shiboqi-icon.ico"
    };
    QList<QAction*> actions = {
        ui->actionHomeView,
        ui->actionJogView,
        ui->actionStatusView,
        ui->actionAxisControlView,
        ui->actionInterpolationView,
        ui->actionIOView,
        ui->actionProgramView,
        ui->actionOscilloscopeView
    };

    // 保存模块部件指针，以便后续更新高亮状态
    m_moduleWidgetMap.clear();
    m_moduleIconLabels.clear();

    for (int i = 0; i < moduleNames.size(); ++i) {
        QWidget* moduleWidget = new QWidget();
        moduleWidget->setObjectName("moduleWidget");
        moduleWidget->setProperty("active", false); // 初始状态为非活动

        QVBoxLayout* moduleLayout = new QVBoxLayout(moduleWidget);
        moduleLayout->setSpacing(2);
        moduleLayout->setContentsMargins(10, 5, 10, 5);

        // 创建图标标签
        QLabel* iconLabel = new QLabel();
        iconLabel->setObjectName("iconLabel_" + moduleNames[i]);
        iconLabel->setPixmap(QPixmap(iconPaths[i]).scaled(32, 32, Qt::KeepAspectRatio, Qt::SmoothTransformation));
        iconLabel->setAlignment(Qt::AlignCenter);

        // 根据模块是否显示设置图标状态
        bool isEnabled = actions[i]->isChecked();
        iconLabel->setProperty("enabled", isEnabled);

        // 创建文本标签
        QLabel* textLabel = new QLabel(moduleNames[i]);
        textLabel->setAlignment(Qt::AlignCenter);

        // 添加到布局
        moduleLayout->addWidget(iconLabel);
        moduleLayout->addWidget(textLabel);

        // 添加到工具栏布局
        layout->addWidget(moduleWidget);

        // 保存模块部件指针和图标标签指针
        m_moduleWidgetMap[moduleNames[i]] = moduleWidget;
        m_moduleIconLabels[moduleNames[i]] = iconLabel;

        // 连接视图菜单项的信号，更新图标状态
        connect(actions[i], &QAction::toggled, this, [this, moduleNames, i](bool checked) {
            updateModuleIconState(moduleNames[i], checked);
        });
    }

    // 添加弹性空间，使图标居左
    layout->addStretch();

    // 添加自定义工具栏到主工具栏
    ui->mainToolBar->addWidget(toolBarWidget);

    // 连接tabWidget的currentChanged信号，更新工具栏高亮状态
    connect(ui->tabWidget, &QTabWidget::currentChanged, this, &MainWindow::updateToolBarHighlight);
}

void MainWindow::on_actionHomeView_triggered(bool checked)
{
    if (checked) {
        // 添加回零模块
        if (m_moduleWidgets.contains("回零")) {
            m_homeTabIndex = ui->tabWidget->addTab(m_moduleWidgets["回零"], "回零");
        }
    } else {
        // 移除回零模块
        int index = ui->tabWidget->indexOf(m_moduleWidgets["回零"]);
        if (index >= 0) {
            ui->tabWidget->removeTab(index);
        }
    }
    reorderTabs();
}

void MainWindow::on_actionJogView_triggered(bool checked)
{
    if (checked) {
        // 添加点位运动模块
        if (m_moduleWidgets.contains("点位运动")) {
            m_trapTabIndex = ui->tabWidget->addTab(m_moduleWidgets["点位运动"], "点位运动");
        }
    } else {
        // 移除点位运动模块
        int index = ui->tabWidget->indexOf(m_moduleWidgets["点位运动"]);
        if (index >= 0) {
            ui->tabWidget->removeTab(index);
        }
    }
    reorderTabs();
}

void MainWindow::on_actionStatusView_triggered(bool checked)
{
    if (checked) {
        // 添加轴状态模块
        if (m_moduleWidgets.contains("轴状态")) {
            m_statusTabIndex = ui->tabWidget->addTab(m_moduleWidgets["轴状态"], "轴状态");
        }
    } else {
        // 移除轴状态模块
        int index = ui->tabWidget->indexOf(m_moduleWidgets["轴状态"]);
        if (index >= 0) {
            ui->tabWidget->removeTab(index);
        }
    }
    reorderTabs();
}

void MainWindow::on_actionAxisControlView_triggered(bool checked)
{
    if (checked) {
        // 添加JOG运动模块
        if (m_moduleWidgets.contains("JOG运动")) {
            m_axisControlTabIndex = ui->tabWidget->addTab(m_moduleWidgets["JOG运动"], "JOG运动");
        }
    } else {
        // 移除JOG运动模块
        int index = ui->tabWidget->indexOf(m_moduleWidgets["JOG运动"]);
        if (index >= 0) {
            ui->tabWidget->removeTab(index);
        }
    }
    reorderTabs();
}

void MainWindow::on_actionInterpolationView_triggered(bool checked)
{
    if (checked) {
        // 添加插补模块
        if (m_moduleWidgets.contains("插补")) {
            m_interpolationTabIndex = ui->tabWidget->addTab(m_moduleWidgets["插补"], "插补");
        }
    } else {
        // 移除插补模块
        int index = ui->tabWidget->indexOf(m_moduleWidgets["插补"]);
        if (index >= 0) {
            ui->tabWidget->removeTab(index);
        }
    }
    reorderTabs();
}

void MainWindow::on_actionIOView_triggered(bool checked)
{
    if (checked) {
        // 添加IO输入/输出模块
        if (m_moduleWidgets.contains("IO输入/输出")) {
            m_ioTabIndex = ui->tabWidget->addTab(m_moduleWidgets["IO输入/输出"], "IO输入/输出");
        }
    } else {
        // 移除IO输入/输出模块
        int index = ui->tabWidget->indexOf(m_moduleWidgets["IO输入/输出"]);
        if (index >= 0) {
            ui->tabWidget->removeTab(index);
        }
    }
    reorderTabs();
}

void MainWindow::on_actionProgramView_triggered(bool checked)
{
    if (checked) {
        // 添加编程界面模块
        if (m_moduleWidgets.contains("编程界面")) {
            m_programTabIndex = ui->tabWidget->addTab(m_moduleWidgets["编程界面"], "编程界面");
        }
    } else {
        // 移除编程界面模块
        int index = ui->tabWidget->indexOf(m_moduleWidgets["编程界面"]);
        if (index >= 0) {
            ui->tabWidget->removeTab(index);
        }
    }
    reorderTabs();
}

void MainWindow::on_actionOscilloscopeView_triggered(bool checked)
{
    if (checked) {
        // 添加示波器模块
        if (m_moduleWidgets.contains("示波器")) {
            m_oscilloscopeTabIndex = ui->tabWidget->addTab(m_moduleWidgets["示波器"], "示波器");
        }
    } else {
        // 移除示波器模块
        int index = ui->tabWidget->indexOf(m_moduleWidgets["示波器"]);
        if (index >= 0) {
            ui->tabWidget->removeTab(index);
        }
    }
    reorderTabs();
}

void MainWindow::reorderTabs()
{
    // 创建一个映射，将模块标题映射到实际的标签页索引
    QMap<QString, int> tabIndices;

    // 收集当前所有标签页的索引
    for (int i = 0; i < ui->tabWidget->count(); ++i) {
        QString title = ui->tabWidget->tabText(i);
        tabIndices[title] = i;
    }

    // 如果没有标签页，直接返回
    if (tabIndices.isEmpty()) {
        return;
    }

    // 按照固定顺序重新排列标签页
    // 顺序：回零、点位运动、轴状态、JOG运动、插补、IO输入/输出、编程界面、示波器
    QStringList moduleOrder;
    moduleOrder << "回零" << "点位运动" << "轴状态" << "JOG运动" << "插补" << "IO输入/输出" << "编程界面" << "示波器";

    // 创建一个新的标签页顺序
    QList<QPair<QString, QWidget*>> newOrder;

    // 按照固定顺序添加标签页
    for (const QString& title : moduleOrder) {
        if (tabIndices.contains(title)) {
            int index = tabIndices[title];
            QWidget* widget = ui->tabWidget->widget(index);
            newOrder.append(qMakePair(title, widget));
        }
    }

    // 清除所有标签页，但不删除它们
    while (ui->tabWidget->count() > 0) {
        ui->tabWidget->removeTab(0);
    }

    // 按照新顺序重新添加标签页
    for (const auto& pair : newOrder) {
        ui->tabWidget->addTab(pair.second, pair.first);
    }

    // 更新模块索引
    for (int i = 0; i < ui->tabWidget->count(); ++i) {
        QString title = ui->tabWidget->tabText(i);
        if (title == "回零") {
            m_homeTabIndex = i;
        } else if (title == "点位运动") {
            m_trapTabIndex = i;
        } else if (title == "轴状态") {
            m_statusTabIndex = i;
        } else if (title == "JOG运动") {
            m_axisControlTabIndex = i;
        } else if (title == "插补") {
            m_interpolationTabIndex = i;
        } else if (title == "IO输入/输出") {
            m_ioTabIndex = i;
        } else if (title == "编程界面") {
            m_programTabIndex = i;
        } else if (title == "示波器") {
            m_oscilloscopeTabIndex = i;
        }
    }

    // 更新工具栏高亮状态
    updateToolBarHighlight(ui->tabWidget->currentIndex());
}

void MainWindow::updateToolBarHighlight(int index)
{
    // 如果没有标签页，直接返回
    if (ui->tabWidget->count() == 0 || index < 0) {
        return;
    }

    // 获取当前标签页的标题
    QString currentTitle = ui->tabWidget->tabText(index);

    // 更新工具栏高亮状态
    for (auto it = m_moduleWidgetMap.begin(); it != m_moduleWidgetMap.end(); ++it) {
        QString title = it.key();
        QWidget* widget = it.value();

        // 设置活动状态
        bool isActive = (title == currentTitle);
        widget->setProperty("active", isActive);

        // 强制更新样式
        widget->style()->unpolish(widget);
        widget->style()->polish(widget);
        widget->update();
    }
}

void MainWindow::updateModuleIconState(const QString& moduleName, bool enabled)
{
    // 如果模块图标标签不存在，直接返回
    if (!m_moduleIconLabels.contains(moduleName)) {
        return;
    }

    // 获取模块图标标签
    QLabel* iconLabel = m_moduleIconLabels[moduleName];

    if (enabled) {
        // 启用状态：移除所有效果
        iconLabel->setGraphicsEffect(nullptr);
    } else {
        // 禁用状态：添加灰度效果
        QGraphicsColorizeEffect* effect = new QGraphicsColorizeEffect(iconLabel);
        effect->setColor(Qt::gray);
        effect->setStrength(1.0); // 完全灰度
        iconLabel->setGraphicsEffect(effect);
    }

    // 设置启用状态属性（用于CSS样式）
    iconLabel->setProperty("enabled", enabled);

    // 强制更新样式
    iconLabel->style()->unpolish(iconLabel);
    iconLabel->style()->polish(iconLabel);
    iconLabel->update();
}

void MainWindow::onHomeWidgetApiStatusChanged(const QString& message, bool isSuccess)
{
    // 显示API调用状态
    showApiStatus(message, isSuccess);
}

void MainWindow::onIOWidgetApiStatusChanged(const QString& message, bool isSuccess)
{
    // 显示API调用状态
    showApiStatus(message, isSuccess);
}

void MainWindow::connectDevice()
{
    if (!m_connected) {
        // 连接过程已经在ConnectionDialog中完成
        // 这里只需要更新UI状态

        // 更新连接状态
        m_connected = true;
        QLabel *connLabel = findChild<QLabel*>("connStatusLabel");
        if (connLabel) {
            connLabel->setText("已连接");
        }

        // 更新UI状态
        ui->actionConnect->setEnabled(false);
        ui->actionDisconnect->setEnabled(true);

        // 更新设备树图标
        QTreeWidgetItem *rootItem = ui->deviceTreeWidget->topLevelItem(0);
        if (rootItem) {
            rootItem->setIcon(0, style()->standardIcon(QStyle::SP_DriveNetIcon));
            for (int i = 0; i < rootItem->childCount(); ++i) {
                rootItem->child(i)->setIcon(0, style()->standardIcon(QStyle::SP_MediaPlay));
            }
        }

        // 立即更新轴位置和状态
        updateAxisPositions();
    }
}

void MainWindow::disconnectDevice()
{
    if (m_connected) {
        // 显示断开连接提示
        QMessageBox::information(this, "断开连接", "正在断开设备连接...");

        // 调用API包装器关闭板卡
        AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();
        if (wrapper && wrapper->isConnected()) {
            short result = wrapper->closeBoard();
            if (result != 0) {
                QMessageBox::warning(this, "错误",
                                   QString("断开设备连接失败，错误码：%1").arg(result));
                return; // 如果断开失败，不继续执行
            }
        }

        // 更新连接状态
        m_connected = false;
        QLabel *connLabel = findChild<QLabel*>("connStatusLabel");
        if (connLabel) {
            connLabel->setText("未连接");
        }

        // 更新UI状态
        ui->actionConnect->setEnabled(true);
        ui->actionDisconnect->setEnabled(false);

        // 更新设备树图标
        QTreeWidgetItem *rootItem = ui->deviceTreeWidget->topLevelItem(0);
        if (rootItem) {
            rootItem->setIcon(0, style()->standardIcon(QStyle::SP_ComputerIcon));
            for (int i = 0; i < rootItem->childCount(); ++i) {
                rootItem->child(i)->setIcon(0, style()->standardIcon(QStyle::SP_DriveNetIcon));
            }
        }

        // 重置轴位置显示
        for (int i = 0; i < 4; ++i) {
            QString unitStr = m_unitConverter->getPositionUnitString();
            m_axisPositionLabels[i]->setText(QString("轴%1: 0 %2").arg(i).arg(unitStr));
        }
    }
}

// 这里不需要重复定义槽函数

// 单位类型变化响应
void MainWindow::onUnitTypeChanged(UnitType type)
{
    // 更新底部状态栏的轴位置显示
    updateAxisPositions();

    // 显示单位切换提示
    QString unitName = (type == UNIT_PULSE) ? "pulse" : "mm";
    showApiStatus(QString("已切换到%1单位显示").arg(unitName), true);
}

