#ifndef AXISCONTROLWIDGET_H
#define AXISCONTROLWIDGET_H

#include <QWidget>
#include <QTimer>
#include <QDoubleSpinBox>
#include <QPushButton>
#include "admc_api_wrapper.h"
#include "unitconverter.h"

namespace Ui {
class AxisControlWidget;
}

class AxisControlWidget : public QWidget
{
    Q_OBJECT

signals:
    void apiStatusChanged(const QString& message, bool isSuccess);

public:
    explicit AxisControlWidget(QWidget *parent = nullptr);
    ~AxisControlWidget();

private slots:
    void enableAxis();
    void disableAxis();
    void resetAxis();

    // Jog相关槽函数
    void startJogPositive();
    void startJogNegative();
    void stopJog();

    // 单位转换相关槽函数
    void onUnitTypeChanged(UnitType type);
    void onUnitSwitchClicked();

private:
    // 创建Jog控制界面
    void createJogInterface();

    // 更新单位显示
    void updateUnitDisplay();
    // 设置默认参数值
    void setDefaultParameters();

    Ui::AxisControlWidget *ui;
    AdmcApiWrapper* m_apiWrapper;
    UnitConverter* m_unitConverter; // 单位转换器

    // Jog相关成员变量
    bool m_isJogging;
    int m_jogDirection; // 1: 正向, -1: 负向, 0: 停止
    int m_currentAxis;
    int m_currentCrd;

    // Jog控件成员变量
    QDoubleSpinBox *m_spinJogSpeed;
    QDoubleSpinBox *m_spinJogAcc;
    QPushButton *m_btnJogPositive;
    QPushButton *m_btnJogNegative;
};

#endif // AXISCONTROLWIDGET_H