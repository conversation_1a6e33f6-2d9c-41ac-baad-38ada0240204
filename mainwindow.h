#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QTreeWidget>
#include <QDockWidget>
#include <QTabWidget>
#include <QStatusBar>
#include <QMenu>
#include <QSettings>
#include <QTimer>
#include <QLabel>
#include "interpolationwidget.h"
#include "trapwidget.h"
#include "admc_api_wrapper.h"
#include "statuswidget.h"
#include "unitconverter.h"
#include "programwidget.h"
#include "oscilloscopewidget.h"

namespace Ui {
class MainWindow;
}

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    // 菜单动作响应
    void on_actionExit_triggered();
    void on_actionAbout_triggered();
    void on_actionDeviceView_triggered(bool checked);
    void on_actionConnect_triggered();
    void on_actionDisconnect_triggered();

    // 视图菜单动作响应
    void on_actionHomeView_triggered(bool checked);
    void on_actionJogView_triggered(bool checked);
    void on_actionStatusView_triggered(bool checked);
    void on_actionAxisControlView_triggered(bool checked);
    void on_actionInterpolationView_triggered(bool checked);
    void on_actionIOView_triggered(bool checked);
    void on_actionProgramView_triggered(bool checked);
    void on_actionOscilloscopeView_triggered(bool checked);

    // 自定义槽函数
    void showContextMenu(const QPoint &pos);
    void updateStatusBar();
    void updateAxisPositions(); // 新增：更新轴位置
    void showApiStatus(const QString& message, bool isSuccess);
    void connectDevice();
    void disconnectDevice();
    void onHomeWidgetApiStatusChanged(const QString& message, bool isSuccess);
    void onIOWidgetApiStatusChanged(const QString& message, bool isSuccess);
    void onAxisAlarmStatusChanged(short axis, bool hasAlarm);
    void onUnitTypeChanged(UnitType type); // 单位类型变化响应

private:
    // UI初始化
    void setupUI();
    void setupMenus();
    void setupToolBar();
    void setupDeviceTree();
    void setupStatusBar();
    void setupTabWidgets();

    // 成员变量
    Ui::MainWindow *ui;
    QSettings *m_settings;
    QTimer *m_statusTimer;
    bool m_connected;

    // 轴状态模块引用
    StatusWidget* m_statusWidget;

    // 轴位置显示标签
    QLabel *m_axisPositionLabels[4]; // 新增：轴位置显示标签数组

    // 单位转换器
    UnitConverter* m_unitConverter;

    // 模块部件映射
    QMap<QString, QWidget*> m_moduleWidgets;

    // 工具栏模块部件映射
    QMap<QString, QWidget*> m_moduleWidgetMap;

    // 工具栏模块图标标签映射
    QMap<QString, QLabel*> m_moduleIconLabels;

    // 模块标签页索引
    int m_homeTabIndex;
    int m_trapTabIndex;
    int m_statusTabIndex;
    int m_axisControlTabIndex;
    int m_interpolationTabIndex;
    int m_ioTabIndex;
    int m_programTabIndex;
    int m_oscilloscopeTabIndex;

    // 重新排序标签页
    void reorderTabs();

    // 更新工具栏高亮状态
    void updateToolBarHighlight(int index);

    // 更新模块图标状态
    void updateModuleIconState(const QString& moduleName, bool enabled);


// 这里不需要重复声明槽函数
};

#endif // MAINWINDOW_H