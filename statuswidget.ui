<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>StatusWidget</class>
 <widget class="QWidget" name="StatusWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>640</width>
    <height>480</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>轴状态监控</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string>显示设置</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <widget class="QLabel" name="label">
        <property name="text">
         <string>刷新模式:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QRadioButton" name="radioRealtime">
        <property name="text">
         <string>实时刷新</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QRadioButton" name="radioManual">
        <property name="text">
         <string>手动刷新</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="btnRefresh">
        <property name="text">
         <string>刷新</string>
        </property>
        <property name="icon">
         <iconset theme="view-refresh"/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="btnClear">
        <property name="text">
         <string>清除</string>
        </property>
        <property name="icon">
         <iconset theme="edit-clear"/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="btnExport">
        <property name="text">
         <string>导出</string>
        </property>
        <property name="icon">
         <iconset theme="document-save-as"/>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QTableWidget" name="tableStatus">
     <property name="alternatingRowColors">
      <bool>true</bool>
     </property>
     <property name="selectionMode">
      <enum>QAbstractItemView::SingleSelection</enum>
     </property>
     <property name="selectionBehavior">
      <enum>QAbstractItemView::SelectRows</enum>
     </property>
     <attribute name="horizontalHeaderStretchLastSection">
      <bool>true</bool>
     </attribute>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="labelLastUpdate">
     <property name="frameShape">
      <enum>QFrame::Panel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Sunken</enum>
     </property>
     <property name="text">
      <string>未刷新</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui> 