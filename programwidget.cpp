#include "programwidget.h"
#include "ui_programwidget.h"
#include "luaexecutor.h"
#include "luaprogramwidget.h" // For LuaSyntaxHighlighter
#include "unitconverter.h" // Keep if still needed

#include <QDebug>
#include <QFileDialog>
#include <QMessageBox>
#include <QInputDialog>
#include <QApplication>
#include <QFile>
#include <QTextStream>
#include <QTimer>
#include <QTableWidgetItem>
#include <QDialogButtonBox>  // 添加QDialogButtonBox头文件
#include <QStackedLayout>    // 添加QStackedLayout头文件

ProgramWidget::ProgramWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::ProgramWidget),
    m_luaExecutor(LuaExecutor::getInstance()), // Get LuaExecutor singleton
    m_highlighterCoord1(nullptr),
    m_highlighterCoord2(nullptr),
    m_sharedVariablesTimer(new QTimer(this)),
    m_unitConverter(UnitConverter::getInstance()) // Keep if still needed
{
    ui->setupUi(this);

    // 设置UI控件
    setupUI();

    // Populate command combo box
    populateCommandComboBox();

    // Connect UI signals to slots
    connect(ui->loadButtonCoord1, &QPushButton::clicked, this, &ProgramWidget::onLoadScriptCoord1Clicked);
    connect(ui->saveButtonCoord1, &QPushButton::clicked, this, &ProgramWidget::onSaveScriptCoord1Clicked);
    connect(ui->loadButtonCoord2, &QPushButton::clicked, this, &ProgramWidget::onLoadScriptCoord2Clicked);
    connect(ui->saveButtonCoord2, &QPushButton::clicked, this, &ProgramWidget::onSaveScriptCoord2Clicked);

    connect(ui->startButtonCoord1, &QPushButton::clicked, this, &ProgramWidget::onStartScriptCoord1Clicked);
    connect(ui->stopButtonCoord1, &QPushButton::clicked, this, &ProgramWidget::onStopScriptCoord1Clicked);
    connect(ui->startButtonCoord2, &QPushButton::clicked, this, &ProgramWidget::onStartScriptCoord2Clicked);
    connect(ui->stopButtonCoord2, &QPushButton::clicked, this, &ProgramWidget::onStopScriptCoord2Clicked);
    connect(ui->startAllButton, &QPushButton::clicked, this, &ProgramWidget::onStartAllScriptsClicked);
    connect(ui->stopAllButton, &QPushButton::clicked, this, &ProgramWidget::onStopAllScriptsClicked);

    connect(m_commandComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &ProgramWidget::onCommandSelected);
    connect(ui->insertCommandButton, &QPushButton::clicked, this, &ProgramWidget::onInsertCommandClicked);

    // 连接清除输出按钮
    connect(m_clearOutputButton, &QPushButton::clicked, this, &ProgramWidget::onClearOutputClicked);

    // Connect LuaExecutor signals to slots
    connect(m_luaExecutor, &LuaExecutor::outputProduced, this, &ProgramWidget::onLuaOutputProduced);
    connect(m_luaExecutor, &LuaExecutor::errorOccurred, this, &ProgramWidget::onLuaErrorOccurred);
    connect(m_luaExecutor, &LuaExecutor::stateChanged, this, &ProgramWidget::onLuaStateChanged);

    // Set up shared variables timer
    m_sharedVariablesTimer->setInterval(100); // Update every 100ms
    connect(m_sharedVariablesTimer, &QTimer::timeout, this, &ProgramWidget::onUpdateSharedVariables);
    m_sharedVariablesTimer->start();

    // Initialize UI state
    updateUIState(LuaExecutor::CoordSystem::COORD1, m_luaExecutor->getState(LuaExecutor::CoordSystem::COORD1)); // Initialize with state of COORD1

    // Connect unit type changed signal if UnitConverter is used
    if (m_unitConverter) {
        connect(m_unitConverter, &UnitConverter::unitTypeChanged, this, &ProgramWidget::onUnitTypeChanged);
    }

    // 测试"等待条件"选项是否可以被选中
    QTimer::singleShot(1000, this, [this]() {
        int index = m_commandComboBox->findText("等待条件");
        if (index != -1) {
            qDebug() << "找到'等待条件'选项，索引为:" << index;
            m_commandComboBox->setCurrentIndex(index);
            qDebug() << "当前选中的选项:" << m_commandComboBox->currentText();
        } else {
            qDebug() << "未找到'等待条件'选项";
        }
    });
}

ProgramWidget::~ProgramWidget()
{
    // Stop the timer
    m_sharedVariablesTimer->stop();

    // Note: LuaExecutor is a singleton, do not delete it here.
    // Syntax highlighters are parented by document, which is parented by editor,
    // which is parented by this widget, so they should be deleted automatically.
    delete ui;
}

// --- Slot Implementations ---

void ProgramWidget::onLoadScriptCoord1Clicked()
{
    QString filePath = QFileDialog::getOpenFileName(this, "加载坐标系 1 脚本", "", "Lua 脚本文件 (*.lua);;所有文件 (*)");
    if (!filePath.isEmpty()) {
        QFile file(filePath);
        if (file.open(QIODevice::ReadOnly | QIODevice::Text)) {
            QTextStream in(&file);
            m_scriptEditorCoord1->setPlainText(in.readAll());
            file.close();
            QMessageBox::information(this, "加载成功", "坐标系 1 脚本加载成功。");
        } else {
            QMessageBox::warning(this, "加载失败", "无法打开文件进行读取。");
        }
    }
}

void ProgramWidget::onSaveScriptCoord1Clicked()
{
    QString filePath = QFileDialog::getSaveFileName(this, "保存坐标系 1 脚本", "", "Lua 脚本文件 (*.lua);;所有文件 (*)");
    if (!filePath.isEmpty()) {
        QFile file(filePath);
        if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            QTextStream out(&file);
            out << m_scriptEditorCoord1->toPlainText();
            file.close();
            QMessageBox::information(this, "保存成功", "坐标系 1 脚本保存成功。");
        } else {
            QMessageBox::warning(this, "保存失败", "无法打开文件进行写入。");
        }
    }
}

void ProgramWidget::onLoadScriptCoord2Clicked()
{
    QString filePath = QFileDialog::getOpenFileName(this, "加载坐标系 2 脚本", "", "Lua 脚本文件 (*.lua);;所有文件 (*)");
    if (!filePath.isEmpty()) {
        QFile file(filePath);
        if (file.open(QIODevice::ReadOnly | QIODevice::Text)) {
            QTextStream in(&file);
            m_scriptEditorCoord2->setPlainText(in.readAll());
            file.close();
            QMessageBox::information(this, "加载成功", "坐标系 2 脚本加载成功。");
        } else {
            QMessageBox::warning(this, "加载失败", "无法打开文件进行读取。");
        }
    }
}

void ProgramWidget::onSaveScriptCoord2Clicked()
{
    QString filePath = QFileDialog::getSaveFileName(this, "保存坐标系 2 脚本", "", "Lua 脚本文件 (*.lua);;所有文件 (*)");
    if (!filePath.isEmpty()) {
        QFile file(filePath);
        if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            QTextStream out(&file);
            out << m_scriptEditorCoord2->toPlainText();
            file.close();
            QMessageBox::information(this, "保存成功", "坐标系 2 脚本保存成功。");
        } else {
            QMessageBox::warning(this, "保存失败", "无法打开文件进行写入。");
        }
    }
}

void ProgramWidget::onStartScriptCoord1Clicked()
{
    if (!m_luaExecutor) return;
    QString script = m_scriptEditorCoord1->toPlainText();
    if (script.isEmpty()) {
        QMessageBox::warning(this, "启动失败", "坐标系 1 脚本为空，无法启动。");
        return;
    }
    m_luaExecutor->loadScript(LuaExecutor::CoordSystem::COORD1, script);
    m_luaExecutor->startExecution(LuaExecutor::CoordSystem::COORD1);
}

void ProgramWidget::onStopScriptCoord1Clicked()
{
    if (!m_luaExecutor) return;
    m_luaExecutor->stopExecution(LuaExecutor::CoordSystem::COORD1);
}

void ProgramWidget::onStartScriptCoord2Clicked()
{
    if (!m_luaExecutor) return;
    QString script = m_scriptEditorCoord2->toPlainText();
    if (script.isEmpty()) {
        QMessageBox::warning(this, "启动失败", "坐标系 2 脚本为空，无法启动。");
        return;
    }
    m_luaExecutor->loadScript(LuaExecutor::CoordSystem::COORD2, script);
    m_luaExecutor->startExecution(LuaExecutor::CoordSystem::COORD2);
}

void ProgramWidget::onStopScriptCoord2Clicked()
{
    if (!m_luaExecutor) return;
    m_luaExecutor->stopExecution(LuaExecutor::CoordSystem::COORD2);
}

void ProgramWidget::onStartAllScriptsClicked()
{
    if (!m_luaExecutor) return;
    QString script1 = m_scriptEditorCoord1->toPlainText();
    QString script2 = m_scriptEditorCoord2->toPlainText();

    if (script1.isEmpty() && script2.isEmpty()) {
         QMessageBox::warning(this, "启动失败", "两个坐标系脚本都为空，无法启动。");
         return;
    }

    if (!script1.isEmpty()) {
        m_luaExecutor->loadScript(LuaExecutor::CoordSystem::COORD1, script1);
    }
     if (!script2.isEmpty()) {
        m_luaExecutor->loadScript(LuaExecutor::CoordSystem::COORD2, script2);
    }

    m_luaExecutor->startAllExecution();
}

void ProgramWidget::onStopAllScriptsClicked()
{
    if (!m_luaExecutor) return;
    m_luaExecutor->stopAllExecution();
}

void ProgramWidget::onLuaOutputProduced(LuaExecutor::CoordSystem coord, const QString& output)
{
    qDebug() << "ProgramWidget::onLuaOutputProduced被调用，坐标系:" << static_cast<int>(coord) << "输出:" << output;
    QString prefix = (coord == LuaExecutor::CoordSystem::COORD1) ? "[COORD1] " : "[COORD2] ";
    m_outputWindow->append(prefix + output);
    qDebug() << "输出已添加到m_outputWindow";
}

void ProgramWidget::onLuaErrorOccurred(LuaExecutor::CoordSystem coord, const QString& errorMessage)
{
    QString prefix = (coord == LuaExecutor::CoordSystem::COORD1) ? "[COORD1 ERROR] " : "[COORD2 ERROR] ";

    // 检查是否是底层API错误
    if (errorMessage.contains("错误代码:") || errorMessage.contains("error code:")) {
        // 这是底层API错误，使用红色文本特别显示
        QString formattedError = QString("<span style='color:red;'>%1%2</span>").arg(prefix).arg(errorMessage);
        m_outputWindow->append(formattedError);
        return;
    }

    // 尝试从错误信息中提取行号
    // 错误信息格式通常是 "[string "..."]:行号: 错误信息"
    int lineNumber = -1;
    int colonPos1 = errorMessage.indexOf("]:");
    if (colonPos1 != -1) {
        int colonPos2 = errorMessage.indexOf(":", colonPos1 + 2);
        if (colonPos2 != -1) {
            QString lineStr = errorMessage.mid(colonPos1 + 2, colonPos2 - colonPos1 - 2);
            bool ok;
            int line = lineStr.toInt(&ok);
            if (ok) {
                lineNumber = line;
            }
        }
    }

    if (lineNumber > 0) {
        QString formattedError = prefix + QString("第 %1 行: %2").arg(lineNumber).arg(errorMessage);
        m_outputWindow->append(formattedError);

        // 高亮显示错误行
        QPlainTextEdit* editor = (coord == LuaExecutor::CoordSystem::COORD1) ? m_scriptEditorCoord1 : m_scriptEditorCoord2;
        if (editor) {
            QTextCursor cursor(editor->document()->findBlockByLineNumber(lineNumber - 1));
            editor->setTextCursor(cursor);
            editor->ensureCursorVisible();
        }
    } else {
        m_outputWindow->append(prefix + errorMessage);
    }
}

void ProgramWidget::onLuaStateChanged(LuaExecutor::CoordSystem coord, LuaExecutor::State state)
{
    updateUIState(coord, state);
}

void ProgramWidget::onCommandSelected(int index)
{
    // 只记录选择的命令，不执行插入操作
    // 插入操作将在点击插入按钮时执行
    Q_UNUSED(index); // 防止未使用参数警告
}

void ProgramWidget::onInsertCommandClicked()
{
    QString selectedCommand = m_commandComboBox->currentText();
    QPlainTextEdit* currentEditor = currentScriptEditor();
    if (!currentEditor) return;

    // 根据选择的命令类型，创建参数对话框
    QDialog paramDialog(this);
    paramDialog.setWindowTitle("编辑参数");
    QVBoxLayout* layout = new QVBoxLayout(&paramDialog);
    QFormLayout* formLayout = new QFormLayout();
    QList<QLineEdit*> paramEdits;
    QString codeTemplate;

    if (selectedCommand == "直线插补") {
        codeTemplate = "coord.move_linear(%1, %2, %3, %4)";
        QLineEdit* xEdit = new QLineEdit("0");
        QLineEdit* yEdit = new QLineEdit("0");
        QLineEdit* speedEdit = new QLineEdit("100");
        QLineEdit* accEdit = new QLineEdit("10");
        formLayout->addRow("目标X:", xEdit);
        formLayout->addRow("目标Y:", yEdit);
        formLayout->addRow("速度:", speedEdit);
        formLayout->addRow("加速度:", accEdit);
        paramEdits << xEdit << yEdit << speedEdit << accEdit;
    } else if (selectedCommand == "延时") {
        codeTemplate = "delay(%1)";
        QLineEdit* msEdit = new QLineEdit("1000");
        formLayout->addRow("毫秒:", msEdit);
        paramEdits << msEdit;
    } else if (selectedCommand == "轴点位运动") {
        codeTemplate = "axis.move_trap(%1, %2, %3, %4, %5)";
        QLineEdit* axisEdit = new QLineEdit("0");
        QLineEdit* posEdit = new QLineEdit("0");
        QLineEdit* speedEdit = new QLineEdit("100");
        QLineEdit* accEdit = new QLineEdit("10");
        QLineEdit* ratioEdit = new QLineEdit("1");
        formLayout->addRow("轴号:", axisEdit);
        formLayout->addRow("目标位置:", posEdit);
        formLayout->addRow("最大速度:", speedEdit);
        formLayout->addRow("加速度:", accEdit);
        formLayout->addRow("加速度比例:", ratioEdit);
        paramEdits << axisEdit << posEdit << speedEdit << accEdit << ratioEdit;
    } else if (selectedCommand == "设置共享变量") {
        codeTemplate = "set_shared_variable(\"%1\", %2)";
        QLineEdit* nameEdit = new QLineEdit("变量名");
        QLineEdit* valueEdit = new QLineEdit("0");
        formLayout->addRow("变量名:", nameEdit);
        formLayout->addRow("值:", valueEdit);
        paramEdits << nameEdit << valueEdit;
    } else if (selectedCommand == "获取共享变量") {
        codeTemplate = "local var = get_shared_variable(\"%1\")\n-- 使用 var";
        QLineEdit* nameEdit = new QLineEdit("变量名");
        formLayout->addRow("变量名:", nameEdit);
        paramEdits << nameEdit;
    } else if (selectedCommand == "打印输出") {
        codeTemplate = "print(\"%1\")";
        QLineEdit* contentEdit = new QLineEdit("内容");
        formLayout->addRow("内容:", contentEdit);
        paramEdits << contentEdit;
    } else if (selectedCommand == "条件判断 (if)") {
        codeTemplate = "if %1 then\n  -- 代码块\nend";
        QLineEdit* conditionEdit = new QLineEdit("条件");
        formLayout->addRow("条件:", conditionEdit);
        paramEdits << conditionEdit;
    } else if (selectedCommand == "条件判断 (if-else)") {
        codeTemplate = "if %1 then\n  -- if 代码块\nelse\n  -- else 代码块\nend";
        QLineEdit* conditionEdit = new QLineEdit("条件");
        formLayout->addRow("条件:", conditionEdit);
        paramEdits << conditionEdit;
    } else if (selectedCommand == "数值循环 (for)") {
        codeTemplate = "for i = %1, %2, %3 do\n  -- 循环体\nend";
        QLineEdit* startEdit = new QLineEdit("1");
        QLineEdit* endEdit = new QLineEdit("10");
        QLineEdit* stepEdit = new QLineEdit("1");
        formLayout->addRow("起始值:", startEdit);
        formLayout->addRow("结束值:", endEdit);
        formLayout->addRow("步长:", stepEdit);
        paramEdits << startEdit << endEdit << stepEdit;
    } else if (selectedCommand == "while 循环") {
        codeTemplate = "while %1 do\n  -- 循环体\nend";
        QLineEdit* conditionEdit = new QLineEdit("条件");
        formLayout->addRow("条件:", conditionEdit);
        paramEdits << conditionEdit;
    } else if (selectedCommand == "repeat-until 循环") {
        codeTemplate = "repeat\n  -- 循环体\nuntil %1";
        QLineEdit* conditionEdit = new QLineEdit("条件");
        formLayout->addRow("条件:", conditionEdit);
        paramEdits << conditionEdit;
    } else if (selectedCommand == "跳转标签") {
        codeTemplate = "::%1::";
        QLineEdit* labelEdit = new QLineEdit("标签名");
        formLayout->addRow("标签名:", labelEdit);
        paramEdits << labelEdit;
    } else if (selectedCommand == "跳转 (goto)") {
        codeTemplate = "goto %1";
        QLineEdit* labelEdit = new QLineEdit("标签名");
        formLayout->addRow("标签名:", labelEdit);
        paramEdits << labelEdit;
    } else if (selectedCommand == "等待条件") {
        // 创建一个垂直布局，包含所有参数控件
        QVBoxLayout* waitLayout = new QVBoxLayout();

        // 创建一个组合框，用于选择等待类型
        QComboBox* typeComboBox = new QComboBox();
        typeComboBox->addItem("IO输入");
        typeComboBox->addItem("共享变量");
        QHBoxLayout* typeLayout = new QHBoxLayout();
        typeLayout->addWidget(new QLabel("等待类型:"));
        typeLayout->addWidget(typeComboBox);
        waitLayout->addLayout(typeLayout);

        // 创建一个堆栈布局，根据选择的类型显示不同的参数
        QStackedLayout* stackedLayout = new QStackedLayout();

        // IO输入参数
        QWidget* ioWidget = new QWidget();
        QFormLayout* ioFormLayout = new QFormLayout(ioWidget);
        QLineEdit* ioNumberEdit = new QLineEdit("1");
        QComboBox* ioValueComboBox = new QComboBox();
        ioValueComboBox->addItem("true");
        ioValueComboBox->addItem("false");
        ioFormLayout->addRow("IO输入:", ioNumberEdit);
        ioFormLayout->addRow("期望值:", ioValueComboBox);
        stackedLayout->addWidget(ioWidget);

        // 共享变量参数
        QWidget* varWidget = new QWidget();
        QFormLayout* varFormLayout = new QFormLayout(varWidget);
        QLineEdit* varNameEdit = new QLineEdit("变量名");
        QComboBox* opComboBox = new QComboBox();
        opComboBox->addItem("==");
        opComboBox->addItem("!=");
        opComboBox->addItem(">");
        opComboBox->addItem("<");
        opComboBox->addItem(">=");
        opComboBox->addItem("<=");
        QLineEdit* varValueEdit = new QLineEdit("0");
        varFormLayout->addRow("变量名:", varNameEdit);
        varFormLayout->addRow("操作符:", opComboBox);
        varFormLayout->addRow("期望值:", varValueEdit);
        stackedLayout->addWidget(varWidget);

        // 将堆栈布局添加到主布局
        waitLayout->addLayout(stackedLayout);

        // 将整个等待条件布局添加到主布局
        layout->addLayout(waitLayout);

        // 连接类型选择框的信号，切换参数页面
        connect(typeComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
                stackedLayout, &QStackedLayout::setCurrentIndex);

        // 添加确定和取消按钮
        QDialogButtonBox* buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel);
        connect(buttonBox, &QDialogButtonBox::rejected, &paramDialog, &QDialog::reject);
        layout->addWidget(buttonBox);

        // 设置确定按钮的点击处理
        connect(buttonBox, &QDialogButtonBox::accepted, [&paramDialog, typeComboBox,
                ioNumberEdit, ioValueComboBox, varNameEdit, opComboBox, varValueEdit, &codeTemplate]() {
            // 根据选择的类型，设置代码模板
            if (typeComboBox->currentIndex() == 0) {
                // IO输入
                QString ioValue = ioValueComboBox->currentText();
                codeTemplate = QString("wait(\"input\", %1, \"%2\")").arg(ioNumberEdit->text()).arg(ioValue);
            } else {
                // 共享变量
                codeTemplate = QString("wait(\"var\", \"%1\", \"%2\", %3)")
                    .arg(varNameEdit->text())
                    .arg(opComboBox->currentText())
                    .arg(varValueEdit->text());
            }
            paramDialog.accept();
        });

        // 显示对话框
        if (paramDialog.exec() == QDialog::Accepted) {
            // 插入代码
            currentEditor->insertPlainText(codeTemplate + "\n");
            return;
        }
        return;
    } else if (selectedCommand == "设置IO输出") {
        // 创建一个新的对话框
        QDialog ioDialog(this);
        ioDialog.setWindowTitle("设置IO输出参数");
        QVBoxLayout* ioLayout = new QVBoxLayout(&ioDialog);

        // 创建表单布局
        QFormLayout* ioFormLayout = new QFormLayout();
        QLineEdit* ioNumberEdit = new QLineEdit("1");
        QComboBox* ioValueComboBox = new QComboBox();
        ioValueComboBox->addItem("true");
        ioValueComboBox->addItem("false");
        ioFormLayout->addRow("IO输出:", ioNumberEdit);
        ioFormLayout->addRow("输出值:", ioValueComboBox);

        // 将表单布局添加到主布局
        ioLayout->addLayout(ioFormLayout);

        // 添加确定和取消按钮
        QDialogButtonBox* buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel);
        connect(buttonBox, &QDialogButtonBox::accepted, &ioDialog, &QDialog::accept);
        connect(buttonBox, &QDialogButtonBox::rejected, &ioDialog, &QDialog::reject);
        ioLayout->addWidget(buttonBox);

        // 显示对话框
        if (ioDialog.exec() == QDialog::Accepted) {
            // 插入代码
            QString ioValue = ioValueComboBox->currentText();
            codeTemplate = "set_io_output(%1, \"%2\")";
            QString code = QString(codeTemplate).arg(ioNumberEdit->text()).arg(ioValue);
            currentEditor->insertPlainText(code + "\n");
        }
        return;
    } else if (selectedCommand == "等待运动完成") {
        // 等待运动完成指令不需要参数
        codeTemplate = "wait_motion_complete()";

        // 直接插入代码，不需要弹出参数对话框
        currentEditor->insertPlainText(codeTemplate + "\n");
        return;
    } else {
        // 未知命令类型
        QMessageBox::warning(this, "错误", "未知的命令类型: " + selectedCommand);
        return;
    }

    layout->addLayout(formLayout);

    // 添加确定和取消按钮
    QDialogButtonBox* buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel);
    connect(buttonBox, &QDialogButtonBox::accepted, &paramDialog, &QDialog::accept);
    connect(buttonBox, &QDialogButtonBox::rejected, &paramDialog, &QDialog::reject);
    layout->addWidget(buttonBox);

    // 显示对话框
    if (paramDialog.exec() == QDialog::Accepted) {
        // 收集参数值
        QStringList params;
        for (QLineEdit* edit : paramEdits) {
            params << edit->text();
        }

        // 格式化代码
        QString codeSnippet = codeTemplate;
        for (int i = 0; i < params.size(); ++i) {
            codeSnippet = codeSnippet.arg(params[i]);
        }

        // 插入代码
        currentEditor->insertPlainText(codeSnippet + "\n");
    }
}

void ProgramWidget::onUpdateSharedVariables()
{
    if (!m_luaExecutor) return;

    // 获取所有共享变量
    QMap<QString, QVariant> sharedVars = m_luaExecutor->getAllSharedVariables();

    // 更新表格
    m_sharedVariablesTable->setRowCount(sharedVars.size());
    int row = 0;
    for (auto it = sharedVars.begin(); it != sharedVars.end(); ++it) {
        QTableWidgetItem* nameItem = new QTableWidgetItem(it.key());
        QTableWidgetItem* valueItem = new QTableWidgetItem(it.value().toString()); // 将 QVariant 转换为字符串显示
        m_sharedVariablesTable->setItem(row, 0, nameItem);
        m_sharedVariablesTable->setItem(row, 1, valueItem);
        row++;
    }

    // 自动调整列宽
    m_sharedVariablesTable->resizeColumnsToContents();
}

void ProgramWidget::onUnitTypeChanged(UnitType type)
{
    // Handle unit type changes if necessary, e.g., update labels or input hints
    // For now, this is a placeholder.
    qDebug() << "Unit type changed to:" << (type == UNIT_MM ? "mm" : "pulse");
}

void ProgramWidget::onClearOutputClicked()
{
    if (m_outputWindow) {
        m_outputWindow->clear();
        m_outputWindow->append("输出已清除");
    }
}

// --- Private Helper Implementations ---

QPlainTextEdit* ProgramWidget::currentScriptEditor()
{
    if (!m_scriptTabWidget) return nullptr;
    int currentIndex = m_scriptTabWidget->currentIndex();
    if (currentIndex == 0) {
        return m_scriptEditorCoord1;
    } else if (currentIndex == 1) {
        return m_scriptEditorCoord2;
    }
    return nullptr;
}

void ProgramWidget::setupUI()
{
    // 获取UI控件
    m_scriptTabWidget = ui->scriptTabWidget;
    m_outputWindow = ui->outputWindow;
    m_commandComboBox = ui->commandComboBox;
    m_sharedVariablesTable = ui->sharedVariablesTable;

    // 检查必要的控件是否存在
    if (!m_scriptTabWidget || !m_outputWindow) {
        qWarning() << "必要的UI控件不存在，无法初始化UI";
        return;
    }

    // 使用原有的QPlainTextEdit
    m_scriptEditorCoord1 = ui->scriptEditorCoord1;
    m_scriptEditorCoord2 = ui->scriptEditorCoord2;

    // 设置语法高亮
    if (m_scriptEditorCoord1) {
        m_highlighterCoord1 = new LuaSyntaxHighlighter(m_scriptEditorCoord1->document());
    }

    if (m_scriptEditorCoord2) {
        m_highlighterCoord2 = new LuaSyntaxHighlighter(m_scriptEditorCoord2->document());
    }

    // 创建清除输出按钮
    m_clearOutputButton = new QPushButton("清除输出", this);
    m_clearOutputButton->setObjectName("clearOutputButton");

    // 获取输出窗口的父布局
    QWidget* outputParent = m_outputWindow->parentWidget();
    if (outputParent && outputParent->layout()) {
        QLayout* outputLayout = outputParent->layout();

        // 添加清除按钮到布局
        if (outputLayout->inherits("QVBoxLayout")) {
            QHBoxLayout* buttonLayout = new QHBoxLayout();
            buttonLayout->addStretch(1);
            buttonLayout->addWidget(m_clearOutputButton);
            qobject_cast<QVBoxLayout*>(outputLayout)->addLayout(buttonLayout);
        }
    }

    // 设置输出窗口接受HTML格式
    m_outputWindow->setAcceptRichText(true);
}

void ProgramWidget::populateCommandComboBox()
{
    if (!m_commandComboBox) return;

    m_commandComboBox->clear();
    m_commandComboBox->addItem("直线插补");
    m_commandComboBox->addItem("延时");
    m_commandComboBox->addItem("轴点位运动");
    m_commandComboBox->addItem("设置共享变量");
    m_commandComboBox->addItem("获取共享变量");
    m_commandComboBox->addItem("打印输出");
    m_commandComboBox->addItem("条件判断 (if)");
    m_commandComboBox->addItem("条件判断 (if-else)");
    m_commandComboBox->addItem("数值循环 (for)");
    m_commandComboBox->addItem("while 循环");
    m_commandComboBox->addItem("repeat-until 循环");
    m_commandComboBox->addItem("跳转标签");
    m_commandComboBox->addItem("跳转 (goto)");
    m_commandComboBox->addItem("等待条件");
    m_commandComboBox->addItem("设置IO输出");
    m_commandComboBox->addItem("等待运动完成");
    // Add other command names here

    // 调试输出所有项目
    qDebug() << "命令下拉框中的所有项目:";
    for (int i = 0; i < m_commandComboBox->count(); ++i) {
        qDebug() << i << ":" << m_commandComboBox->itemText(i);
    }

    // 确保下拉框可以显示所有项目
    m_commandComboBox->setMaxVisibleItems(20);

    // 确保下拉框的宽度足够大
    m_commandComboBox->setSizeAdjustPolicy(QComboBox::AdjustToContents);
    m_commandComboBox->setMinimumWidth(150);
}

void ProgramWidget::updateUIState(LuaExecutor::CoordSystem coord, LuaExecutor::State state)
{
    // This function updates the state of buttons and other UI elements
    // based on the execution state of the LuaExecutor for a specific coordinate system.

    bool isRunningCoord1 = m_luaExecutor->getState(LuaExecutor::CoordSystem::COORD1) == LuaExecutor::State::RUNNING;
    bool isRunningCoord2 = m_luaExecutor->getState(LuaExecutor::CoordSystem::COORD2) == LuaExecutor::State::RUNNING;
    bool isAnyScriptRunning = isRunningCoord1 || isRunningCoord2;

    // 输出状态变化信息
    QString statusText;
    switch (state) {
        case LuaExecutor::State::STOPPED: statusText = "停止"; break;
        case LuaExecutor::State::RUNNING: statusText = "运行中"; break;
        case LuaExecutor::State::PAUSED: statusText = "暂停"; break;
        case LuaExecutor::State::ERROR: statusText = "错误"; break;
    }
    onLuaOutputProduced(coord, QString("状态变化: %1").arg(statusText));

    // 调试输出当前状态
    qDebug() << "更新UI状态: 坐标系" << static_cast<int>(coord) + 1
             << "状态=" << statusText
             << "isRunningCoord1=" << isRunningCoord1
             << "isRunningCoord2=" << isRunningCoord2;

    // 检查是否有轴在运动
    bool isAnyAxisMoving = false;
    for (short axis = 0; axis < 4; ++axis) {
        short status = 0;
        short result = m_luaExecutor->getApiWrapper()->getAxisStatus(axis, status);
        if (result == 0) {
            // 检查运动中状态位（MOV，第5位）
            if ((status & (1 << 5)) != 0) {
                isAnyAxisMoving = true;
                break;
            }
        }
    }

    // 更新按钮状态
    ui->startButtonCoord1->setEnabled(!isRunningCoord1);
    ui->stopButtonCoord1->setEnabled(isRunningCoord1 || isAnyAxisMoving);

    ui->startButtonCoord2->setEnabled(!isRunningCoord2);
    ui->stopButtonCoord2->setEnabled(isRunningCoord2 || isAnyAxisMoving);

    ui->startAllButton->setEnabled(!isAnyScriptRunning && !isAnyAxisMoving);
    ui->stopAllButton->setEnabled(isAnyScriptRunning || isAnyAxisMoving);

    // 启用/禁用脚本编辑器和命令插入
    m_scriptEditorCoord1->setEnabled(!isRunningCoord1);
    m_scriptEditorCoord2->setEnabled(!isRunningCoord2);
    m_commandComboBox->setEnabled(!isAnyScriptRunning);
    ui->insertCommandButton->setEnabled(!isAnyScriptRunning);

    // 启用/禁用文件操作
    ui->loadButtonCoord1->setEnabled(!isRunningCoord1);
    ui->saveButtonCoord1->setEnabled(!isRunningCoord1);
    ui->loadButtonCoord2->setEnabled(!isRunningCoord2);
    ui->saveButtonCoord2->setEnabled(!isRunningCoord2);

    // 如果状态是STOPPED或ERROR，强制更新按钮状态
    if (state == LuaExecutor::State::STOPPED || state == LuaExecutor::State::ERROR) {
        if (coord == LuaExecutor::CoordSystem::COORD1) {
            ui->startButtonCoord1->setEnabled(true);
            ui->stopButtonCoord1->setEnabled(false);
            m_scriptEditorCoord1->setEnabled(true);
            ui->loadButtonCoord1->setEnabled(true);
            ui->saveButtonCoord1->setEnabled(true);
        } else {
            ui->startButtonCoord2->setEnabled(true);
            ui->stopButtonCoord2->setEnabled(false);
            m_scriptEditorCoord2->setEnabled(true);
            ui->loadButtonCoord2->setEnabled(true);
            ui->saveButtonCoord2->setEnabled(true);
        }

        // 如果两个坐标系都停止了，启用全局按钮
        if (m_luaExecutor->getState(LuaExecutor::CoordSystem::COORD1) != LuaExecutor::State::RUNNING &&
            m_luaExecutor->getState(LuaExecutor::CoordSystem::COORD2) != LuaExecutor::State::RUNNING) {
            ui->startAllButton->setEnabled(true);
            ui->stopAllButton->setEnabled(false);
            m_commandComboBox->setEnabled(true);
            ui->insertCommandButton->setEnabled(true);
        }
    }

    // 强制更新UI
    QApplication::processEvents();
}

// Note: The old createParamWidgets, updateParamWidgets, getParamsFromUI,
// createCommand, showCommandParams, clearParamWidgets, cloneCommand,
// createCommandByType, loadProgramFromFile, saveProgramToFile,
// updateProgramList, onCommandTypeChanged, onInsertButtonClicked,
// onDeleteButtonClicked, onModifyButtonClicked, onProgramListItemClicked,
// onProgramListItemDoubleClicked, onStartButtonClicked, onStopButtonClicked,
// onStatusUpdate, onErrorReport, setListItemBackground functions are removed
// or replaced by the new Lua-based logic.
