#include "iowidget.h"
#include <QDebug>

IOWidget::IOWidget(QWidget *parent) : QWidget(parent), m_showingInput(true)
{
    // 初始化数据
    for (int i = 0; i < 16; i++) {
        m_deviceInput[i] = 0;
        m_deviceOutput[i] = 0;
    }

    // 设置UI
    setupUI();

    // 创建定时器，定期刷新IO状态
    m_refreshTimer = new QTimer(this);
    connect(m_refreshTimer, &QTimer::timeout, this, &IOWidget::updateIOStatus);
    m_refreshTimer->start(100); // 100ms刷新一次

    // 初始显示输入
    updateIOStatus();
}

IOWidget::~IOWidget()
{
    if (m_refreshTimer) {
        m_refreshTimer->stop();
    }
}

void IOWidget::setupUI()
{
    // 创建主布局
    QVBoxLayout* mainLayout = new QVBoxLayout(this);

    // 创建选择分组框
    QGroupBox* selectionGroup = new QGroupBox("IO类型选择");
    QHBoxLayout* selectionLayout = new QHBoxLayout(selectionGroup);

    // 创建单选按钮
    m_radioInput = new QRadioButton("输入");
    m_radioOutput = new QRadioButton("输出");
    m_buttonGroup = new QButtonGroup(this);
    m_buttonGroup->addButton(m_radioInput, 0);
    m_buttonGroup->addButton(m_radioOutput, 1);

    // 默认选中输入
    m_radioInput->setChecked(true);

    // 添加到布局
    selectionLayout->addWidget(m_radioInput);
    selectionLayout->addWidget(m_radioOutput);
    selectionLayout->addStretch();

    // 创建表格
    m_tableWidget = new QTableWidget(16, 2, this);
    m_tableWidget->setHorizontalHeaderLabels(QStringList() << "IO序号" << "状态");
    m_tableWidget->verticalHeader()->setVisible(false);
    m_tableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_tableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    m_tableWidget->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);

    // 初始化表格内容
    for (int i = 0; i < 16; i++) {
        // 设置IO序号
        QTableWidgetItem* indexItem = new QTableWidgetItem(QString::number(i));
        indexItem->setTextAlignment(Qt::AlignCenter);
        m_tableWidget->setItem(i, 0, indexItem);

        // 设置状态（初始为空）
        QTableWidgetItem* statusItem = new QTableWidgetItem();
        statusItem->setTextAlignment(Qt::AlignCenter);
        m_tableWidget->setItem(i, 1, statusItem);
    }

    // 添加到主布局
    mainLayout->addWidget(selectionGroup);
    mainLayout->addWidget(m_tableWidget);

    // 连接信号槽
    connect(m_radioInput, &QRadioButton::toggled, this, &IOWidget::onInputRadioToggled);
    connect(m_radioOutput, &QRadioButton::toggled, this, &IOWidget::onOutputRadioToggled);
    connect(m_tableWidget, &QTableWidget::cellClicked, this, &IOWidget::onCellClicked);
}

void IOWidget::onInputRadioToggled(bool checked)
{
    if (checked) {
        m_showingInput = true;
        updateTable();
    }
}

void IOWidget::onOutputRadioToggled(bool checked)
{
    if (checked) {
        m_showingInput = false;
        updateTable();
    }
}

void IOWidget::updateIOStatus()
{
    // 如果显示的是输入，则获取最新的输入状态
    if (m_showingInput) {
        getDeviceInput();
    }

    // 更新表格显示
    updateTable();
}

void IOWidget::updateTable()
{
    for (int i = 0; i < 16; i++) {
        QTableWidgetItem* statusItem = m_tableWidget->item(i, 1);

        if (m_showingInput) {
            // 显示输入状态
            bool isOn = (m_deviceInput[i] != 0);
            statusItem->setBackground(isOn ? QColor(0, 255, 0, 128) : QColor(255, 255, 255));
            statusItem->setText(isOn ? "●" : "");
        } else {
            // 显示输出状态
            bool isOn = (m_deviceOutput[i] != 0);
            statusItem->setBackground(isOn ? QColor(0, 255, 0, 128) : QColor(255, 255, 255));
            statusItem->setText(isOn ? "●" : "");
        }
    }
}

void IOWidget::onCellClicked(int row, int column)
{
    // 只处理状态列的点击，且只在输出模式下
    if (column == 1 && !m_showingInput) {
        // 切换状态
        bool currentState = (m_deviceOutput[row] != 0);
        setDeviceOutput(row, !currentState);
    }
}

void IOWidget::setDeviceOutput(int index, bool value)
{
    if (index < 0 || index >= 16) {
        return;
    }

    // 更新本地状态
    m_deviceOutput[index] = value ? 1 : 0;

    // 获取API包装器实例
    AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();

    // 调用API设置输出
    if (wrapper && wrapper->isConnected()) {
        // 调用API设置设备输出
        short result = wrapper->setDeviceOutput(m_deviceOutput);

        // 处理结果
        if (result == 0) {
            // 成功
            emit apiStatusChanged(QString("设置设备输出成功，IO%1=%2").arg(index).arg(value ? "ON" : "OFF"), true);
        } else {
            // 失败
            emit apiStatusChanged(QString("设置设备输出失败，返回值为%1").arg(result), false);
        }
    } else {
        // 未连接
        qDebug() << "未连接到控制器，无法设置设备输出";
        emit apiStatusChanged("未连接到控制器，无法设置设备输出", false);
    }

    // 更新表格显示
    updateTable();
}

void IOWidget::getDeviceInput()
{
    // 获取API包装器实例
    AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();

    // 调用API获取输入
    if (wrapper && wrapper->isConnected()) {
        // 调用API获取设备输入
        short result = wrapper->getDeviceInput(m_deviceInput);

        if (result != 0) {
            // 获取失败
            qDebug() << "获取设备输入失败，返回值为" << result;
        }
    } else {
        // 未连接
        qDebug() << "未连接到控制器，无法获取设备输入";
    }
}
