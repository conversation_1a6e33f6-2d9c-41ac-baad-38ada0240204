#include "mainwindow.h"
#include <QApplication>
#include <QTranslator>
#include <QLibraryInfo>
#include <QTimer>
#include <QThread>
#include <QPixmap>
#include <QPainter>
#include <QLinearGradient>
#include <QSplashScreen>
#include <QProgressBar>
#include <QFile>
#include <QDebug>

// 模拟加载过程的函数
void performStartupTasks(QSplashScreen *splash, MainWindow *mainWindow)
{
    // 模拟初始化过程的各个阶段
    QStringList loadingTexts = {
        "正在初始化系统...",
        "正在加载设备驱动...",
        "正在初始化运动控制模块...",
        "正在加载界面组件...",
        "正在检查系统配置...",
        "准备就绪，即将启动..."
    };

    // 总共的加载步骤数
    const int totalSteps = loadingTexts.size();

    // 为每个步骤分配进度值
    for (int i = 0; i < totalSteps; ++i) {
        // 更新加载消息
        splash->showMessage(loadingTexts[i], Qt::AlignBottom | Qt::AlignHCenter, Qt::white);

        // 处理事件，确保UI更新
        QApplication::processEvents();

        // 模拟耗时操作
        QThread::msleep(200);
    }

    // 完成所有加载任务后，显示主窗口
    mainWindow->show();

    // 短暂延迟后关闭启动画面
    QThread::msleep(200);
}

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    // 设置应用程序信息
    app.setApplicationName("安达运动控制系统");
    app.setApplicationVersion("1.0.0.ec86e1d5");
    app.setOrganizationName("安达自动化");

    // 加载Qt自带的简体中文翻译
    QTranslator qtTranslator;
    qtTranslator.load("qt_zh_CN", QLibraryInfo::location(QLibraryInfo::TranslationsPath));
    app.installTranslator(&qtTranslator);

    // 创建启动画面
    // 尝试从Resources文件夹加载启动画面图片
    QPixmap pixmap;
    bool loaded = false;

    // 尝试加载图片的路径列表
    QStringList imagePaths = {
        ":/Resources/101.jpg",
    };

    // 尝试加载图片
    for (const QString& path : imagePaths) {
        if (QFile::exists(path)) {
            pixmap.load(path);
            if (!pixmap.isNull()) {
                qDebug() << "成功加载启动画面图片:" << path;
                loaded = true;
                break;
            }
        }
    }

    // 如果无法加载图片，创建一个默认的启动画面
    if (!loaded) {
        qDebug() << "无法加载启动画面图片，使用默认启动画面";
        pixmap = QPixmap(600, 400);

        // 创建渐变背景
        QLinearGradient gradient(0, 0, 0, 400);
        gradient.setColorAt(0, QColor(0, 120, 215));
        gradient.setColorAt(1, QColor(0, 80, 155));

        // 创建画家
        QPainter painter(&pixmap);
        painter.setRenderHint(QPainter::Antialiasing);

        // 填充背景
        painter.fillRect(0, 0, 600, 400, gradient);

        // 设置字体
        QFont titleFont("Microsoft YaHei", 24, QFont::Bold);
        QFont versionFont("Microsoft YaHei", 12);

        // 绘制标题
        painter.setPen(Qt::white);
        painter.setFont(titleFont);
        painter.drawText(QRect(0, 150, 600, 50), Qt::AlignCenter, "安达运动控制系统");

        // 绘制版本信息
        painter.setFont(versionFont);
        painter.drawText(QRect(0, 200, 600, 30), Qt::AlignCenter, "版本 1.0.0");

        // 绘制公司信息
        painter.drawText(QRect(0, 350, 600, 30), Qt::AlignCenter, "© 2025 安达智能装备科技有限公司");

        // 结束绘制
        painter.end();
    }

    QSplashScreen splash(pixmap);
    splash.show();

    // 确保启动画面显示
    app.processEvents();

    // 创建主窗口（但不显示）
    MainWindow *w = new MainWindow();

    // 执行启动任务
    performStartupTasks(&splash, w);

    // 关闭启动画面
    splash.finish(w);

    return app.exec();
}
