#include "oscilloscopewidget.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QLabel>
#include <QPushButton>
#include <QDebug>
#include <QMessageBox>
#include <QFileDialog>
#include <QFile>
#include <QTextStream>
#include <QDateTime>

OscilloscopeWidget::OscilloscopeWidget(QWidget *parent)
    : QWidget(parent),
      m_customPlot(nullptr),
      m_deltaTimeLabel(nullptr),
      m_deltaValueLabel(nullptr),
      m_apiWrapper(AdmcApiWrapper::getInstance()),
      m_unitConverter(UnitConverter::getInstance()),
      m_isPaused(false),
      m_startTime(QDateTime::currentDateTime().toMSecsSinceEpoch()),
      m_isDraggingMarker(false),
      m_draggedMarkerIndex(-1),
      m_isDraggingHorizontal(false)
{
    // 初始化垂直标记线的相对位置
    m_verticalMarkerRelativePositions[0] = 0.25; // 25%位置
    m_verticalMarkerRelativePositions[1] = 0.75; // 75%位置

    // 初始化水平标记线的相对位置
    m_horizontalMarkerRelativePositions[0] = 0.75; // 75%位置（上方）
    m_horizontalMarkerRelativePositions[1] = 0.25; // 25%位置（下方）

    // 初始化上一次位置和时间戳
    for (int i = 0; i < POSITION_CHANNEL_COUNT; ++i) {
        m_lastPositions[i] = 0.0;
        m_lastTimestamps[i] = 0.0;
    }

    setupUI();
    setupPlot();
    setupMarkers();

    // 初始化定时器
    m_updateTimer = new QTimer(this);
    connect(m_updateTimer, &QTimer::timeout, this, &OscilloscopeWidget::updatePlot);
    m_updateTimer->start(10); // 每10ms更新一次，降低更新频率以提高性能

    // 连接单位转换器的信号
    connect(m_unitConverter, &UnitConverter::unitTypeChanged, this, &OscilloscopeWidget::onUnitTypeChanged);

    // 清空数据
    clearData();
}

OscilloscopeWidget::~OscilloscopeWidget()
{
    if (m_updateTimer->isActive()) {
        m_updateTimer->stop();
    }
}

void OscilloscopeWidget::setupUI()
{
    // 创建主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(this);

    // 创建图表控件
    m_customPlot = new QCustomPlot(this);
    mainLayout->addWidget(m_customPlot, 1); // 图表占据大部分空间

    // 连接鼠标事件
    connect(m_customPlot, &QCustomPlot::mouseMove, this, &OscilloscopeWidget::onPlotMouseMove);
    connect(m_customPlot, &QCustomPlot::mousePress, this, &OscilloscopeWidget::onPlotMousePress);
    connect(m_customPlot, &QCustomPlot::mouseWheel, this, &OscilloscopeWidget::onPlotMouseWheel);

    // 创建控制面板
    QGroupBox *controlGroupBox = new QGroupBox("控制面板");
    QHBoxLayout *controlLayout = new QHBoxLayout(controlGroupBox);

    // 创建通道选择组
    QGroupBox *channelGroupBox = new QGroupBox("通道选择");
    QGridLayout *channelLayout = new QGridLayout(channelGroupBox);

    // 添加位置通道复选框
    QColor posColors[] = {Qt::blue, Qt::red, Qt::green, Qt::magenta};
    for (int i = 0; i < POSITION_CHANNEL_COUNT; ++i) {
        m_channelCheckBoxes[i] = new QCheckBox(QString("fbpos0%1").arg(i+1));
        m_channelCheckBoxes[i]->setChecked(false); // 默认不显示通道

        // 设置复选框文本颜色与图表线条颜色一致
        QPalette palette = m_channelCheckBoxes[i]->palette();
        palette.setColor(QPalette::WindowText, posColors[i]);
        m_channelCheckBoxes[i]->setPalette(palette);

        channelLayout->addWidget(m_channelCheckBoxes[i], i, 0);

        // 连接信号槽
        connect(m_channelCheckBoxes[i], &QCheckBox::stateChanged, this, &OscilloscopeWidget::onChannelVisibilityChanged);
    }

    // 添加速度通道复选框
    QColor velColors[] = {Qt::darkBlue, Qt::darkRed, Qt::darkGreen, Qt::darkMagenta};
    for (int i = 0; i < VELOCITY_CHANNEL_COUNT; ++i) {
        int index = POSITION_CHANNEL_COUNT + i;
        m_channelCheckBoxes[index] = new QCheckBox(QString("fbvel0%1").arg(i+1));
        m_channelCheckBoxes[index]->setChecked(false); // 默认不显示速度通道

        // 设置复选框文本颜色与图表线条颜色一致
        QPalette palette = m_channelCheckBoxes[index]->palette();
        palette.setColor(QPalette::WindowText, velColors[i]);
        m_channelCheckBoxes[index]->setPalette(palette);

        channelLayout->addWidget(m_channelCheckBoxes[index], i, 1);

        // 连接信号槽
        connect(m_channelCheckBoxes[index], &QCheckBox::stateChanged, this, &OscilloscopeWidget::onChannelVisibilityChanged);
    }

    controlLayout->addWidget(channelGroupBox);

    // 创建标记线控制组
    QGroupBox *markerGroupBox = new QGroupBox("标记线");
    QVBoxLayout *markerLayout = new QVBoxLayout(markerGroupBox);

    m_horizontalMarkerCheckBox = new QCheckBox("水平标记线");
    m_verticalMarkerCheckBox = new QCheckBox("垂直标记线");

    // 设置初始状态为未选中
    m_horizontalMarkerCheckBox->setChecked(false);
    m_verticalMarkerCheckBox->setChecked(false);

    markerLayout->addWidget(m_horizontalMarkerCheckBox);
    markerLayout->addWidget(m_verticalMarkerCheckBox);

    // 连接标记线复选框信号槽
    connect(m_horizontalMarkerCheckBox, &QCheckBox::stateChanged, this, &OscilloscopeWidget::onHorizontalMarkerCheckBoxChanged);
    connect(m_verticalMarkerCheckBox, &QCheckBox::stateChanged, this, &OscilloscopeWidget::onVerticalMarkerCheckBoxChanged);

    controlLayout->addWidget(markerGroupBox);

    // 创建按钮组
    QGroupBox *buttonGroupBox = new QGroupBox("操作");
    QGridLayout *buttonLayout = new QGridLayout(buttonGroupBox);

    // 创建更短的按钮
    m_resetButton = new QPushButton("重置");
    m_pauseButton = new QPushButton("暂停");
    m_centerButton = new QPushButton("居中");
    m_clearButton = new QPushButton("清屏");
    m_saveButton = new QPushButton("保存CSV");
    m_importButton = new QPushButton("导入CSV");

    // 设置按钮的最小宽度
    int minButtonWidth = 60;
    m_resetButton->setMinimumWidth(minButtonWidth);
    m_pauseButton->setMinimumWidth(minButtonWidth);
    m_centerButton->setMinimumWidth(minButtonWidth);
    m_clearButton->setMinimumWidth(minButtonWidth);
    m_saveButton->setMinimumWidth(minButtonWidth);
    m_importButton->setMinimumWidth(minButtonWidth);

    // 使用网格布局，每行两个按钮
    buttonLayout->addWidget(m_resetButton, 0, 0);
    buttonLayout->addWidget(m_pauseButton, 0, 1);
    buttonLayout->addWidget(m_centerButton, 1, 0);
    buttonLayout->addWidget(m_clearButton, 1, 1);
    buttonLayout->addWidget(m_saveButton, 2, 0);
    buttonLayout->addWidget(m_importButton, 2, 1);

    // 连接按钮信号槽
    connect(m_resetButton, &QPushButton::clicked, this, &OscilloscopeWidget::onResetButtonClicked);
    connect(m_pauseButton, &QPushButton::clicked, this, &OscilloscopeWidget::onPauseButtonClicked);
    connect(m_centerButton, &QPushButton::clicked, this, &OscilloscopeWidget::onCenterButtonClicked);
    connect(m_clearButton, &QPushButton::clicked, this, &OscilloscopeWidget::onClearButtonClicked);
    connect(m_saveButton, &QPushButton::clicked, this, &OscilloscopeWidget::onSaveButtonClicked);
    connect(m_importButton, &QPushButton::clicked, this, &OscilloscopeWidget::onImportButtonClicked);

    controlLayout->addWidget(buttonGroupBox);

    // 添加控制面板到主布局
    mainLayout->addWidget(controlGroupBox);
}

void OscilloscopeWidget::setupPlot()
{
    // 配置图表
    m_customPlot->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom | QCP::iSelectPlottables);
    m_customPlot->axisRect()->setupFullAxesBox();

    // 设置X轴为时间轴（毫秒）
    m_customPlot->xAxis->setLabel("时间 (ms)");

    // 设置X轴刻度间隔为1ms
    QSharedPointer<QCPAxisTickerFixed> xTicker(new QCPAxisTickerFixed);
    xTicker->setTickStep(1.0); // 1ms一个小刻度
    xTicker->setScaleStrategy(QCPAxisTickerFixed::ssMultiples);
    m_customPlot->xAxis->setTicker(xTicker);

    // 设置Y轴为位置轴
    m_customPlot->yAxis->setLabel(QString("位置 (%1)").arg(m_unitConverter->getPositionUnitString()));

    // 启用图例
    m_customPlot->legend->setVisible(true);
    m_customPlot->legend->setBrush(QBrush(QColor(255, 255, 255, 200)));

    // 启用抗锯齿
    m_customPlot->setAntialiasedElements(QCP::aeAll);

    // 创建位置图表
    QColor posColors[] = {Qt::blue, Qt::red, Qt::green, Qt::magenta};
    for (int i = 0; i < POSITION_CHANNEL_COUNT; ++i) {
        m_graphs[i] = m_customPlot->addGraph();
        // 设置线条样式 - 使用细线条
        m_graphs[i]->setPen(QPen(posColors[i], 1));
        // 设置散点样式 - 使用圆点
        m_graphs[i]->setScatterStyle(QCPScatterStyle(QCPScatterStyle::ssCircle, posColors[i], posColors[i], 5));
        // 设置线型为线条连接，点加线的方式
        m_graphs[i]->setLineStyle(QCPGraph::lsLine);
        m_graphs[i]->setName(QString("fbpos0%1").arg(i+1));
        m_graphs[i]->setVisible(false); // 默认不显示位置通道
    }

    // 创建速度图表
    QColor velColors[] = {Qt::darkBlue, Qt::darkRed, Qt::darkGreen, Qt::darkMagenta};
    for (int i = 0; i < VELOCITY_CHANNEL_COUNT; ++i) {
        int index = POSITION_CHANNEL_COUNT + i;
        m_graphs[index] = m_customPlot->addGraph();
        // 设置线条样式 - 使用虚线区分速度曲线
        m_graphs[index]->setPen(QPen(velColors[i], 1, Qt::DashLine));
        // 设置散点样式 - 使用方形点
        m_graphs[index]->setScatterStyle(QCPScatterStyle(QCPScatterStyle::ssSquare, velColors[i], velColors[i], 5));
        // 设置线型为线条连接，点加线的方式
        m_graphs[index]->setLineStyle(QCPGraph::lsLine);
        m_graphs[index]->setName(QString("fbvel0%1").arg(i+1));
        m_graphs[index]->setVisible(false); // 默认不显示速度通道
    }

    // 创建鼠标跟踪器
    m_tracer = new QCPItemTracer(m_customPlot);
    m_tracer->setStyle(QCPItemTracer::tsCircle);
    m_tracer->setSize(7);
    m_tracer->setPen(QPen(Qt::black));
    m_tracer->setBrush(Qt::white);
    m_tracer->setVisible(false);

    // 创建跟踪器标签
    m_tracerLabel = new QCPItemText(m_customPlot);
    m_tracerLabel->setPositionAlignment(Qt::AlignLeft | Qt::AlignTop);
    m_tracerLabel->setTextAlignment(Qt::AlignLeft);
    m_tracerLabel->setPadding(QMargins(5, 5, 5, 5));
    m_tracerLabel->setBrush(QBrush(QColor(255, 255, 255, 200)));
    m_tracerLabel->setPen(QPen(Qt::black));
    m_tracerLabel->setVisible(false);

    // 设置初始范围
    m_customPlot->xAxis->setRange(0, TIME_WINDOW_MS);
    m_customPlot->yAxis->setRange(-1000, 1000);

    // 立即重绘
    m_customPlot->replot(QCustomPlot::rpQueuedReplot);
}

void OscilloscopeWidget::setupMarkers()
{
    // 创建水平标记线
    for (int i = 0; i < 2; ++i) {
        m_horizontalMarkers[i] = new QCPItemStraightLine(m_customPlot);
        m_horizontalMarkers[i]->setPen(QPen(Qt::darkGray, 1, Qt::DashLine));
        m_horizontalMarkers[i]->setVisible(false);

        // 设置初始位置
        double yPos = (i == 0) ? 500 : -500;
        m_horizontalMarkers[i]->point1->setCoords(0, yPos);
        m_horizontalMarkers[i]->point2->setCoords(1, yPos);

        // 创建水平标记线标签
        m_horizontalMarkerLabels[i] = new QCPItemText(m_customPlot);
        m_horizontalMarkerLabels[i]->setPositionAlignment(Qt::AlignLeft | Qt::AlignVCenter);
        m_horizontalMarkerLabels[i]->setTextAlignment(Qt::AlignLeft);
        m_horizontalMarkerLabels[i]->setPadding(QMargins(2, 2, 2, 2));
        m_horizontalMarkerLabels[i]->setBrush(QBrush(QColor(255, 255, 255, 200)));
        m_horizontalMarkerLabels[i]->setPen(QPen(Qt::darkGray));
        m_horizontalMarkerLabels[i]->setText(QString::number(yPos));
        m_horizontalMarkerLabels[i]->position->setCoords(0, yPos);
        m_horizontalMarkerLabels[i]->setVisible(false);
    }

    // 创建垂直标记线
    for (int i = 0; i < 2; ++i) {
        m_verticalMarkers[i] = new QCPItemStraightLine(m_customPlot);
        m_verticalMarkers[i]->setPen(QPen(Qt::darkGray, 1, Qt::DashLine));
        m_verticalMarkers[i]->setVisible(false);

        // 设置初始位置
        double xPos = (i == 0) ? TIME_WINDOW_MS * 0.25 : TIME_WINDOW_MS * 0.75;
        m_verticalMarkers[i]->point1->setCoords(xPos, 0);
        m_verticalMarkers[i]->point2->setCoords(xPos, 1);

        // 创建垂直标记线标签
        m_verticalMarkerLabels[i] = new QCPItemText(m_customPlot);
        m_verticalMarkerLabels[i]->setPositionAlignment(Qt::AlignHCenter | Qt::AlignBottom);
        m_verticalMarkerLabels[i]->setTextAlignment(Qt::AlignCenter);
        m_verticalMarkerLabels[i]->setPadding(QMargins(2, 2, 2, 2));
        m_verticalMarkerLabels[i]->setBrush(QBrush(QColor(255, 255, 255, 200)));
        m_verticalMarkerLabels[i]->setPen(QPen(Qt::darkGray));
        m_verticalMarkerLabels[i]->setText(QString::number(xPos, 'f', 1));
        m_verticalMarkerLabels[i]->position->setCoords(xPos, 0);
        m_verticalMarkerLabels[i]->setVisible(false);
    }

    // 创建差值标签 - 确保两个标签大小一致
    // 创建ΔT标签 - 垂直标记线差值 - 左下角
    m_deltaTimeLabel = new QCPItemText(m_customPlot);
    m_deltaTimeLabel->setPositionAlignment(Qt::AlignLeft | Qt::AlignBottom);
    m_deltaTimeLabel->setTextAlignment(Qt::AlignLeft);
    m_deltaTimeLabel->setPadding(QMargins(8, 5, 8, 5));
    m_deltaTimeLabel->setBrush(QBrush(QColor(255, 255, 255, 200)));
    m_deltaTimeLabel->setPen(QPen(Qt::black));
    m_deltaTimeLabel->setText("ΔT: 0.0 ms");

    // 确保标签在最上层
    // 尝试获取overlay层，如果不存在则创建
    QCPLayer *overlayLayer = nullptr;
    bool hasOverlayLayer = false;

    for (int i = 0; i < m_customPlot->layerCount(); ++i) {
        if (m_customPlot->layer(i)->name() == "overlay") {
            overlayLayer = m_customPlot->layer(i);
            hasOverlayLayer = true;
            break;
        }
    }

    if (!hasOverlayLayer) {
        // 创建新的overlay层
        m_customPlot->addLayer("overlay", m_customPlot->layer("main"), QCustomPlot::limAbove);
        overlayLayer = m_customPlot->layer("overlay");
    }

    if (overlayLayer) {
        m_deltaTimeLabel->setLayer(overlayLayer);
    }

    // 初始位置设置在左下角
    double xMin = m_customPlot->xAxis->range().lower;
    double yMin = m_customPlot->yAxis->range().lower;
    double xRange = m_customPlot->xAxis->range().size();
    double yRange = m_customPlot->yAxis->range().size();
    m_deltaTimeLabel->position->setCoords(xMin + 0.05 * xRange, yMin + 0.05 * yRange);
    m_deltaTimeLabel->setVisible(false);

    // 创建ΔY标签 - 水平标记线差值 - 右下角
    m_deltaValueLabel = new QCPItemText(m_customPlot);
    m_deltaValueLabel->setPositionAlignment(Qt::AlignRight | Qt::AlignBottom);
    m_deltaValueLabel->setTextAlignment(Qt::AlignRight);
    m_deltaValueLabel->setPadding(QMargins(8, 5, 8, 5));
    m_deltaValueLabel->setBrush(QBrush(QColor(255, 255, 255, 200)));
    m_deltaValueLabel->setPen(QPen(Qt::black));
    m_deltaValueLabel->setText("ΔY: 0.0");

    // 确保标签在最上层
    // 使用已经创建的overlay层
    if (m_customPlot->layer("overlay")) {
        m_deltaValueLabel->setLayer(m_customPlot->layer("overlay"));
    }

    // 初始位置设置在右下角
    double xMax = m_customPlot->xAxis->range().upper;
    m_deltaValueLabel->position->setCoords(xMax - 0.05 * xRange, yMin + 0.05 * yRange);
    m_deltaValueLabel->setVisible(false);
}

void OscilloscopeWidget::updatePlot()
{
    if (m_isPaused) {
        return; // 如果暂停，不更新图表
    }

    // 获取当前时间（毫秒）
    double currentTime = QDateTime::currentDateTime().toMSecsSinceEpoch() - m_startTime;

    // 获取轴位置
    fetchAxisPositions();

    // 计算速度
    calculateVelocities();

    // 更新位置图表数据 - 使用降采样优化性能
    for (int i = 0; i < POSITION_CHANNEL_COUNT; ++i) {
        if (m_graphs[i]->visible()) {
            // 根据数据量和视图范围进行降采样
            double xMin = m_customPlot->xAxis->range().lower;
            double xMax = m_customPlot->xAxis->range().upper;

            // 计算视图范围内的数据点数量
            int visiblePoints = 0;
            int startIdx = -1;
            int endIdx = -1;

            // 查找视图范围内的数据点索引
            for (int j = 0; j < m_timestamps[i].size(); ++j) {
                if (m_timestamps[i][j] >= xMin && m_timestamps[i][j] <= xMax) {
                    if (startIdx == -1) startIdx = j;
                    endIdx = j;
                    visiblePoints++;
                } else if (m_timestamps[i][j] > xMax) {
                    break;
                }
            }

            // 如果有可见数据点
            if (visiblePoints > 0 && startIdx >= 0 && endIdx >= startIdx) {
                // 准备视图范围内的数据
                QVector<double> visibleKeys;
                QVector<double> visibleValues;

                for (int j = startIdx; j <= endIdx; ++j) {
                    visibleKeys.append(m_timestamps[i][j]);
                    visibleValues.append(m_positions[i][j]);
                }

                // 根据视图宽度动态调整最大可见点数
                int viewWidth = m_customPlot->viewport().width();
                // 每像素最多显示2个点，确保在任何缩放级别下都有足够的点
                int maxVisiblePoints = qMax(1000, viewWidth * 2);

                if (visiblePoints > maxVisiblePoints) {
                    // 准备降采样后的数据
                    QVector<QCPGraphData> sampledData = prepareGraphData(visibleKeys, visibleValues, maxVisiblePoints);
                    m_graphs[i]->data()->set(sampledData, true);
                } else {
                    // 数据点不多，直接设置
                    m_graphs[i]->setData(visibleKeys, visibleValues, true);
                }
            } else {
                // 没有可见数据点，清空图表
                m_graphs[i]->data()->clear();
            }
        }
    }

    // 更新速度图表数据 - 使用降采样优化性能
    for (int i = 0; i < VELOCITY_CHANNEL_COUNT; ++i) {
        int index = POSITION_CHANNEL_COUNT + i;
        if (m_graphs[index]->visible()) {
            // 根据数据量和视图范围进行降采样
            double xMin = m_customPlot->xAxis->range().lower;
            double xMax = m_customPlot->xAxis->range().upper;

            // 计算视图范围内的数据点数量
            int visiblePoints = 0;
            int startIdx = -1;
            int endIdx = -1;

            // 查找视图范围内的数据点索引
            for (int j = 0; j < m_velocityTimestamps[i].size(); ++j) {
                if (m_velocityTimestamps[i][j] >= xMin && m_velocityTimestamps[i][j] <= xMax) {
                    if (startIdx == -1) startIdx = j;
                    endIdx = j;
                    visiblePoints++;
                } else if (m_velocityTimestamps[i][j] > xMax) {
                    break;
                }
            }

            // 如果有可见数据点
            if (visiblePoints > 0 && startIdx >= 0 && endIdx >= startIdx) {
                // 准备视图范围内的数据
                QVector<double> visibleKeys;
                QVector<double> visibleValues;

                for (int j = startIdx; j <= endIdx; ++j) {
                    visibleKeys.append(m_velocityTimestamps[i][j]);
                    visibleValues.append(m_velocities[i][j]);
                }

                // 根据视图宽度动态调整最大可见点数
                int viewWidth = m_customPlot->viewport().width();
                // 每像素最多显示2个点，确保在任何缩放级别下都有足够的点
                int maxVisiblePoints = qMax(1000, viewWidth * 2);

                if (visiblePoints > maxVisiblePoints) {
                    // 准备降采样后的数据
                    QVector<QCPGraphData> sampledData = prepareGraphData(visibleKeys, visibleValues, maxVisiblePoints);
                    m_graphs[index]->data()->set(sampledData, true);
                } else {
                    // 数据点不多，直接设置
                    m_graphs[index]->setData(visibleKeys, visibleValues, true);
                }
            } else {
                // 没有可见数据点，清空图表
                m_graphs[index]->data()->clear();
            }
        }
    }

    // 调整X轴范围以实现滚动效果
    double lowerBound = qMax(0.0, currentTime - TIME_WINDOW_MS);
    double upperBound = qMax(TIME_WINDOW_MS, currentTime);
    m_customPlot->xAxis->setRange(lowerBound, upperBound);

    // 更新垂直标记线位置，使其在视图中保持相对固定的位置
    if (m_verticalMarkerCheckBox->isChecked()) {
        double xRange = upperBound - lowerBound;
        for (int i = 0; i < 2; ++i) {
            double xPos = lowerBound + xRange * m_verticalMarkerRelativePositions[i];
            m_verticalMarkers[i]->point1->setCoords(xPos, 0);
            m_verticalMarkers[i]->point2->setCoords(xPos, 1);
        }
    }

    // 更新水平标记线位置，使其在视图中保持相对固定的位置
    if (m_horizontalMarkerCheckBox->isChecked()) {
        double yMin = m_customPlot->yAxis->range().lower;
        double yMax = m_customPlot->yAxis->range().upper;
        double yRange = yMax - yMin;
        for (int i = 0; i < 2; ++i) {
            double yPos = yMin + yRange * m_horizontalMarkerRelativePositions[i];
            m_horizontalMarkers[i]->point1->setCoords(0, yPos);
            m_horizontalMarkers[i]->point2->setCoords(1, yPos);
        }
    }

    // 查找所有可见曲线的最大值和最小值
    double minY = std::numeric_limits<double>::max();
    double maxY = std::numeric_limits<double>::lowest();
    bool hasData = false;

    // 检查位置通道
    for (int i = 0; i < POSITION_CHANNEL_COUNT; ++i) {
        if (m_graphs[i]->visible() && !m_positions[i].isEmpty()) {
            hasData = true;
            for (const double& value : m_positions[i]) {
                minY = qMin(minY, value);
                maxY = qMax(maxY, value);
            }
        }
    }

    // 检查速度通道
    for (int i = 0; i < VELOCITY_CHANNEL_COUNT; ++i) {
        int index = POSITION_CHANNEL_COUNT + i;
        if (m_graphs[index]->visible() && !m_velocities[i].isEmpty()) {
            hasData = true;
            for (const double& value : m_velocities[i]) {
                minY = qMin(minY, value);
                maxY = qMax(maxY, value);
            }
        }
    }

    // 设置Y轴范围
    if (hasData) {
        // 添加一些边距，使曲线不会紧贴边缘
        double margin = (maxY - minY) * 0.1;
        if (margin < 0.1) margin = 0.1; // 确保至少有一些边距

        m_customPlot->yAxis->setRange(minY - margin, maxY + margin);
    } else {
        // 如果没有数据，设置默认范围
        if (m_unitConverter->getCurrentUnitType() == UNIT_PULSE) {
            m_customPlot->yAxis->setRange(-10000, 10000); // pulse单位下的默认范围
        } else {
            m_customPlot->yAxis->setRange(-10, 10); // mm单位下的默认范围
        }
    }

    // 更新标记线标签
    updateMarkerLabels();

    // 更新所有固定标签的位置
    for (int i = 0; i < m_fixedTracers.size(); ++i) {
        QCPItemTracer *tracer = m_fixedTracers[i];
        QCPItemText *label = m_fixedLabels[i];

        // 更新跟踪器位置
        tracer->updatePosition();

        // 更新标签位置
        label->position->setCoords(tracer->position->key(), tracer->position->value());
    }

    // 重绘图表
    m_customPlot->replot(QCustomPlot::rpQueuedReplot);
}

void OscilloscopeWidget::calculateVelocities()
{
    // 计算每个轴的速度
    for (int i = 0; i < POSITION_CHANNEL_COUNT; ++i) {
        if (m_timestamps[i].size() > 0) {
            double currentTime = m_timestamps[i].last();
            double currentPos = m_positions[i].last();

            // 如果有上一次的位置和时间戳，计算速度
            if (m_lastTimestamps[i] > 0) {
                double deltaTime = currentTime - m_lastTimestamps[i]; // 时间差（毫秒）

                // 确保时间差不为零，避免除零错误
                if (deltaTime > 0) {
                    double deltaPos = currentPos - m_lastPositions[i]; // 位置差
                    double velocity = deltaPos / 4.0; // 速度（位置差除以10）

                    // 添加速度数据点
                    m_velocityTimestamps[i].append(currentTime);
                    m_velocities[i].append(velocity);
                }
            }

            // 更新上一次的位置和时间戳
            m_lastPositions[i] = currentPos;
            m_lastTimestamps[i] = currentTime;
        }
    }
}

void OscilloscopeWidget::updateMarkerLabels()
{
    // 获取图表范围
    double xMin = m_customPlot->xAxis->range().lower;
    double xMax = m_customPlot->xAxis->range().upper;
    double yMin = m_customPlot->yAxis->range().lower;
    double yMax = m_customPlot->yAxis->range().upper;
    double xRange = xMax - xMin;
    double yRange = yMax - yMin;

    // 更新水平标记线标签
    for (int i = 0; i < 2; ++i) {
        // 使用相对位置计算实际Y坐标
        double yPos = yMin + yRange * m_horizontalMarkerRelativePositions[i];
        m_horizontalMarkerLabels[i]->setText(QString::number(yPos, 'f', 2));

        // 使用像素坐标固定标签位置在左侧
        double pixelY = m_customPlot->yAxis->coordToPixel(yPos);
        QPoint pixelPos = QPoint(10, pixelY);
        m_horizontalMarkerLabels[i]->position->setPixelPosition(pixelPos);

        // 只有在复选框被选中时才显示标签
        m_horizontalMarkerLabels[i]->setVisible(m_horizontalMarkerCheckBox->isChecked());
    }

    // 更新垂直标记线标签
    for (int i = 0; i < 2; ++i) {
        // 使用相对位置计算实际X坐标
        double xPos = xMin + xRange * m_verticalMarkerRelativePositions[i];
        m_verticalMarkerLabels[i]->setText(QString::number(xPos, 'f', 1));

        // 使用像素坐标固定标签位置在底部
        double pixelX = m_customPlot->xAxis->coordToPixel(xPos);
        QPoint pixelPos = QPoint(pixelX, m_customPlot->viewport().height() - 10);
        m_verticalMarkerLabels[i]->position->setPixelPosition(pixelPos);

        // 只有在复选框被选中时才显示标签
        m_verticalMarkerLabels[i]->setVisible(m_verticalMarkerCheckBox->isChecked());
    }

    // 处理ΔT标签 - 垂直标记线差值 - 左下角
    bool showDeltaTime = m_verticalMarkerCheckBox->isChecked();
    if (showDeltaTime) {
        // 使用相对位置计算实际X坐标
        double x1 = xMin + xRange * m_verticalMarkerRelativePositions[0];
        double x2 = xMin + xRange * m_verticalMarkerRelativePositions[1];
        double deltaTime = qAbs(x2 - x1);
        m_deltaTimeLabel->setText(QString("ΔT: %1 ms").arg(deltaTime, 0, 'f', 1));

        // 使用坐标系坐标固定位置 - 左下角
        double xPos = xMin + 0.05 * xRange; // 左侧5%位置
        double yPos = yMin + 0.05 * yRange; // 底部5%位置
        m_deltaTimeLabel->position->setCoords(xPos, yPos);

        // 确保标签在最上层
        // 使用已经创建的overlay层
        if (m_customPlot->layer("overlay")) {
            m_deltaTimeLabel->setLayer(m_customPlot->layer("overlay"));
        }

        m_deltaTimeLabel->setVisible(true);

        qDebug() << "ΔT标签应该显示在" << xPos << "," << yPos;
    } else {
        m_deltaTimeLabel->setVisible(false);
    }

    // 处理ΔY标签 - 水平标记线差值 - 右下角
    bool showDeltaValue = m_horizontalMarkerCheckBox->isChecked();
    if (showDeltaValue) {
        // 使用相对位置计算实际Y坐标
        double y1 = yMin + yRange * m_horizontalMarkerRelativePositions[0];
        double y2 = yMin + yRange * m_horizontalMarkerRelativePositions[1];
        double deltaValue = qAbs(y2 - y1);
        m_deltaValueLabel->setText(QString("ΔY: %1").arg(deltaValue, 0, 'f', 2));

        // 使用坐标系坐标固定位置 - 右下角
        double xPos = xMax - 0.05 * xRange; // 右侧5%位置
        double yPos = yMin + 0.05 * yRange; // 底部5%位置
        m_deltaValueLabel->position->setCoords(xPos, yPos);

        // 确保标签在最上层
        // 使用已经创建的overlay层
        if (m_customPlot->layer("overlay")) {
            m_deltaValueLabel->setLayer(m_customPlot->layer("overlay"));
        }

        m_deltaValueLabel->setVisible(true);

        qDebug() << "ΔY标签应该显示在" << xPos << "," << yPos;
    } else {
        m_deltaValueLabel->setVisible(false);
    }
}

void OscilloscopeWidget::fetchAxisPositions()
{
    if (!m_apiWrapper->isConnected()) {
        return; // 如果未连接，不获取位置
    }

    // 获取当前时间（毫秒）
    double currentTime = QDateTime::currentDateTime().toMSecsSinceEpoch() - m_startTime;

    // 获取各轴位置
    for (int i = 0; i < POSITION_CHANNEL_COUNT; ++i) {
        double position = 0.0;
        short result = m_apiWrapper->getAxisPosition(i, position);

        if (result == 0) {
            // 添加数据点
            m_timestamps[i].append(currentTime);
            m_positions[i].append(position);
        }
    }
}

void OscilloscopeWidget::onChannelVisibilityChanged(int state)
{
    Q_UNUSED(state); // 防止未使用参数警告
    QCheckBox *checkBox = qobject_cast<QCheckBox*>(sender());
    if (!checkBox) return;

    // 确定是哪个通道的复选框
    for (int i = 0; i < TOTAL_CHANNEL_COUNT; ++i) {
        if (m_channelCheckBoxes[i] == checkBox) {
            m_graphs[i]->setVisible(checkBox->isChecked());
            break;
        }
    }

    // 重绘图表
    m_customPlot->replot(QCustomPlot::rpQueuedReplot);
}

void OscilloscopeWidget::onUnitTypeChanged(UnitType type)
{
    Q_UNUSED(type); // 防止未使用参数警告
    // 更新Y轴标签
    m_customPlot->yAxis->setLabel(QString("位置 (%1)").arg(m_unitConverter->getPositionUnitString()));

    // 重绘图表
    m_customPlot->replot(QCustomPlot::rpQueuedReplot);
}

void OscilloscopeWidget::onResetButtonClicked()
{
    // 清空数据并重置时间
    clearData();
    m_startTime = QDateTime::currentDateTime().toMSecsSinceEpoch();

    // 重置X轴范围
    m_customPlot->xAxis->setRange(0, TIME_WINDOW_MS);

    // 重绘图表
    m_customPlot->replot(QCustomPlot::rpQueuedReplot);

    emit apiStatusChanged("示波器数据已重置", true);
}

void OscilloscopeWidget::onPauseButtonClicked()
{
    m_isPaused = !m_isPaused;

    if (m_isPaused) {
        m_pauseButton->setText("继续");
        emit apiStatusChanged("示波器已暂停", true);
    } else {
        m_pauseButton->setText("暂停");
        emit apiStatusChanged("示波器已继续", true);
    }
}

void OscilloscopeWidget::clearData()
{
    // 清空所有位置通道的数据
    for (int i = 0; i < POSITION_CHANNEL_COUNT; ++i) {
        m_timestamps[i].clear();
        m_positions[i].clear();
        m_lastPositions[i] = 0.0;
        m_lastTimestamps[i] = 0.0;
    }

    // 清空所有速度通道的数据
    for (int i = 0; i < VELOCITY_CHANNEL_COUNT; ++i) {
        m_velocityTimestamps[i].clear();
        m_velocities[i].clear();
    }

    // 清除所有固定标签
    clearAllFixedLabels();
}

void OscilloscopeWidget::onCenterButtonClicked()
{
    // 查找所有可见曲线的最大值和最小值
    double minY = std::numeric_limits<double>::max();
    double maxY = std::numeric_limits<double>::lowest();
    double minX = std::numeric_limits<double>::max();
    double maxX = std::numeric_limits<double>::lowest();
    bool hasData = false;

    // 检查位置通道
    for (int i = 0; i < POSITION_CHANNEL_COUNT; ++i) {
        if (m_graphs[i]->visible() && !m_positions[i].isEmpty()) {
            hasData = true;

            // 查找Y轴范围
            for (const double& value : m_positions[i]) {
                minY = qMin(minY, value);
                maxY = qMax(maxY, value);
            }

            // 查找X轴范围
            if (!m_timestamps[i].isEmpty()) {
                minX = qMin(minX, m_timestamps[i].first());
                maxX = qMax(maxX, m_timestamps[i].last());
            }
        }
    }

    // 检查速度通道
    for (int i = 0; i < VELOCITY_CHANNEL_COUNT; ++i) {
        int index = POSITION_CHANNEL_COUNT + i;
        if (m_graphs[index]->visible() && !m_velocities[i].isEmpty()) {
            hasData = true;

            // 查找Y轴范围
            for (const double& value : m_velocities[i]) {
                minY = qMin(minY, value);
                maxY = qMax(maxY, value);
            }

            // 查找X轴范围
            if (!m_velocityTimestamps[i].isEmpty()) {
                minX = qMin(minX, m_velocityTimestamps[i].first());
                maxX = qMax(maxX, m_velocityTimestamps[i].last());
            }
        }
    }

    if (hasData) {
        // 设置X轴范围，确保所有数据都可见
        if (minX < maxX) {
            // 添加一些边距，使曲线不会紧贴边缘
            double xMargin = (maxX - minX) * 0.05;
            if (xMargin < 1.0) xMargin = 1.0; // 确保至少有一些边距
            m_customPlot->xAxis->setRange(minX - xMargin, maxX + xMargin);
        } else {
            // 如果只有一个数据点，设置一个合理的范围
            double currentTime = QDateTime::currentDateTime().toMSecsSinceEpoch() - m_startTime;
            m_customPlot->xAxis->setRange(qMax(0.0, currentTime - TIME_WINDOW_MS), currentTime);
        }

        // 设置Y轴范围
        if (minY < maxY) {
            // 添加一些边距，使曲线不会紧贴边缘
            double yMargin = (maxY - minY) * 0.1;
            if (yMargin < 0.1) yMargin = 0.1; // 确保至少有一些边距
            m_customPlot->yAxis->setRange(minY - yMargin, maxY + yMargin);
        } else {
            // 如果所有数据点的Y值相同，设置一个合理的范围
            double yCenter = minY;
            double yRange = qAbs(yCenter) * 0.1;
            if (yRange < 0.1) yRange = 0.1; // 确保至少有一些范围
            m_customPlot->yAxis->setRange(yCenter - yRange, yCenter + yRange);
        }
    } else {
        // 如果没有数据，设置默认范围
        double currentTime = QDateTime::currentDateTime().toMSecsSinceEpoch() - m_startTime;
        m_customPlot->xAxis->setRange(qMax(0.0, currentTime - TIME_WINDOW_MS), currentTime);

        if (m_unitConverter->getCurrentUnitType() == UNIT_PULSE) {
            m_customPlot->yAxis->setRange(-10000, 10000); // pulse单位下的默认范围
        } else {
            m_customPlot->yAxis->setRange(-10, 10); // mm单位下的默认范围
        }
    }

    // 重绘图表
    m_customPlot->replot(QCustomPlot::rpQueuedReplot);

    emit apiStatusChanged("示波器视图已居中", true);
}

void OscilloscopeWidget::onClearButtonClicked()
{
    // 清空所有数据，但不重置时间
    clearData();

    // 清除所有图表上的数据
    for (int i = 0; i < TOTAL_CHANNEL_COUNT; ++i) {
        m_graphs[i]->data()->clear();
    }

    // 重置Y轴范围
    if (m_unitConverter->getCurrentUnitType() == UNIT_PULSE) {
        m_customPlot->yAxis->setRange(-10000, 10000); // pulse单位下的默认范围
    } else {
        m_customPlot->yAxis->setRange(-10, 10); // mm单位下的默认范围
    }

    // 重绘图表
    m_customPlot->replot(QCustomPlot::rpQueuedReplot);

    emit apiStatusChanged("示波器屏幕已清除", true);
}

void OscilloscopeWidget::onSaveButtonClicked()
{
    // 检查是否有数据可以保存
    bool hasData = false;
    for (int i = 0; i < POSITION_CHANNEL_COUNT; ++i) {
        if (!m_timestamps[i].isEmpty()) {
            hasData = true;
            break;
        }
    }

    if (!hasData) {
        QMessageBox::warning(this, "保存失败", "没有数据可以保存。");
        return;
    }

    // 获取保存文件路径
    QString defaultName = QString("oscilloscope_data_%1.csv").arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss"));
    QString filePath = QFileDialog::getSaveFileName(this, "保存数据", defaultName, "CSV文件 (*.csv)");

    if (filePath.isEmpty()) {
        return; // 用户取消了保存
    }

    // 保存数据到CSV文件
    saveDataToCSV(filePath);
}

void OscilloscopeWidget::onImportButtonClicked()
{
    // 获取导入文件路径
    QString filePath = QFileDialog::getOpenFileName(this, "导入数据", "", "CSV文件 (*.csv)");

    if (filePath.isEmpty()) {
        return; // 用户取消了导入
    }

    // 导入数据
    importDataFromCSV(filePath);
}

void OscilloscopeWidget::saveDataToCSV(const QString &filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QMessageBox::critical(this, "保存失败", "无法打开文件进行写入: " + file.errorString());
        return;
    }

    QTextStream out(&file);

    // 写入CSV头部
    out << "Time(ms),";
    for (int i = 0; i < POSITION_CHANNEL_COUNT; ++i) {
        out << QString("fbpos0%1,").arg(i+1);
    }
    for (int i = 0; i < VELOCITY_CHANNEL_COUNT; ++i) {
        out << QString("fbvel0%1").arg(i+1);
        if (i < VELOCITY_CHANNEL_COUNT - 1) {
            out << ",";
        }
    }
    out << "\n";

    // 找出所有通道中的最大数据点数
    int maxDataPoints = 0;
    for (int i = 0; i < POSITION_CHANNEL_COUNT; ++i) {
        maxDataPoints = qMax(maxDataPoints, m_timestamps[i].size());
    }
    for (int i = 0; i < VELOCITY_CHANNEL_COUNT; ++i) {
        maxDataPoints = qMax(maxDataPoints, m_velocityTimestamps[i].size());
    }

    // 写入数据
    for (int j = 0; j < maxDataPoints; ++j) {
        // 对于每个时间点
        double timePoint = 0;
        bool hasValidTime = false;

        // 找到这个索引的有效时间点
        for (int i = 0; i < POSITION_CHANNEL_COUNT; ++i) {
            if (j < m_timestamps[i].size()) {
                timePoint = m_timestamps[i][j];
                hasValidTime = true;
                break;
            }
        }
        if (!hasValidTime) {
            for (int i = 0; i < VELOCITY_CHANNEL_COUNT; ++i) {
                if (j < m_velocityTimestamps[i].size()) {
                    timePoint = m_velocityTimestamps[i][j];
                    hasValidTime = true;
                    break;
                }
            }
        }

        if (!hasValidTime) continue;

        // 写入时间点
        out << QString::number(timePoint, 'f', 1) << ",";

        // 写入位置数据
        for (int i = 0; i < POSITION_CHANNEL_COUNT; ++i) {
            if (j < m_positions[i].size() && j < m_timestamps[i].size()) {
                // 确保时间戳匹配
                if (qFuzzyCompare(m_timestamps[i][j], timePoint)) {
                    out << QString::number(m_positions[i][j], 'f', 2);
                }
            }
            out << ","; // 即使没有数据也需要逗号分隔
        }

        // 写入速度数据
        for (int i = 0; i < VELOCITY_CHANNEL_COUNT; ++i) {
            if (j < m_velocities[i].size() && j < m_velocityTimestamps[i].size()) {
                // 确保时间戳匹配
                if (qFuzzyCompare(m_velocityTimestamps[i][j], timePoint)) {
                    out << QString::number(m_velocities[i][j], 'f', 2);
                }
            }
            if (i < VELOCITY_CHANNEL_COUNT - 1) {
                out << ","; // 最后一列不需要逗号
            }
        }
        out << "\n";
    }

    file.close();

    if (out.status() != QTextStream::Ok) {
        QMessageBox::warning(this, "保存失败", "写入文件时发生错误。");
    } else {
        QMessageBox::information(this, "保存成功", "数据已成功保存到:\n" + filePath);
        emit apiStatusChanged("数据已保存到: " + filePath, true);
    }
}

void OscilloscopeWidget::importDataFromCSV(const QString &filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QMessageBox::critical(this, "导入失败", "无法打开文件进行读取: " + file.errorString());
        return;
    }

    QTextStream in(&file);

    // 清空当前数据
    clearData();

    // 清除所有图表上的数据
    for (int i = 0; i < TOTAL_CHANNEL_COUNT; ++i) {
        m_graphs[i]->data()->clear();
    }

    // 读取CSV头部
    QString header = in.readLine();
    QStringList headerFields = header.split(",");

    // 检查CSV格式是否正确
    if (headerFields.size() < 1 + POSITION_CHANNEL_COUNT + VELOCITY_CHANNEL_COUNT) {
        QMessageBox::critical(this, "导入失败", "CSV文件格式不正确，列数不匹配。");
        file.close();
        return;
    }

    int lineNumber = 1; // 头部行是第1行
    bool hasImportedData = false;

    // 读取数据行
    while (!in.atEnd()) {
        lineNumber++;
        QString line = in.readLine();
        QStringList fields = line.split(",");

        if (fields.size() < 1 + POSITION_CHANNEL_COUNT + VELOCITY_CHANNEL_COUNT) {
            qWarning() << "导入CSV: 第" << lineNumber << "行列数不匹配 ("
                      << fields.size() << "列, 期望"
                      << (1 + POSITION_CHANNEL_COUNT + VELOCITY_CHANNEL_COUNT) << "列)";
            continue; // 跳过格式不正确的行
        }

        // 解析时间点
        bool ok;
        double timePoint = fields[0].toDouble(&ok);
        if (!ok) {
            qWarning() << "导入CSV: 第" << lineNumber << "行时间戳转换失败:" << fields[0];
            continue; // 跳过时间点无效的行
        }

        // 解析位置数据
        for (int i = 0; i < POSITION_CHANNEL_COUNT; ++i) {
            if (i + 1 < fields.size()) {
                QString valueStr = fields[i + 1].trimmed();
                if (!valueStr.isEmpty()) {
                    double position = valueStr.toDouble(&ok);
                    if (ok) {
                        m_timestamps[i].append(timePoint);
                        m_positions[i].append(position);
                        hasImportedData = true;
                    } else {
                        qWarning() << "导入CSV: 第" << lineNumber << "行位置通道" << i + 1
                                  << "数据转换失败:" << valueStr;
                    }
                }
            }
        }

        // 解析速度数据
        for (int i = 0; i < VELOCITY_CHANNEL_COUNT; ++i) {
            int index = 1 + POSITION_CHANNEL_COUNT + i;
            if (index < fields.size()) {
                QString valueStr = fields[index].trimmed();
                if (!valueStr.isEmpty()) {
                    double velocity = valueStr.toDouble(&ok);
                    if (ok) {
                        m_velocityTimestamps[i].append(timePoint);
                        m_velocities[i].append(velocity);
                        hasImportedData = true;
                    } else {
                        qWarning() << "导入CSV: 第" << lineNumber << "行速度通道" << i + 1
                                  << "数据转换失败:" << valueStr;
                    }
                }
            }
        }
    }

    file.close();

    if (!hasImportedData) {
        QMessageBox::warning(this, "导入失败", "未能从CSV文件中导入任何有效数据。");
        return;
    }

    // 更新图表数据
    for (int i = 0; i < POSITION_CHANNEL_COUNT; ++i) {
        if (!m_timestamps[i].isEmpty() && !m_positions[i].isEmpty()) {
            m_graphs[i]->setData(m_timestamps[i], m_positions[i], true); // true表示数据已按key排序
        }
    }

    for (int i = 0; i < VELOCITY_CHANNEL_COUNT; ++i) {
        int index = POSITION_CHANNEL_COUNT + i;
        if (!m_velocityTimestamps[i].isEmpty() && !m_velocities[i].isEmpty()) {
            m_graphs[index]->setData(m_velocityTimestamps[i], m_velocities[i], true);
        }
    }

    // 更新时间起点
    bool hasValidTimestamp = false;
    double firstTimestamp = 0;
    double lastTimestamp = 0;

    // 查找第一个有效的时间戳
    for (int i = 0; i < POSITION_CHANNEL_COUNT; ++i) {
        if (!m_timestamps[i].isEmpty()) {
            if (!hasValidTimestamp || m_timestamps[i].first() < firstTimestamp) {
                firstTimestamp = m_timestamps[i].first();
            }
            if (!hasValidTimestamp || m_timestamps[i].last() > lastTimestamp) {
                lastTimestamp = m_timestamps[i].last();
            }
            hasValidTimestamp = true;
        }
    }

    for (int i = 0; i < VELOCITY_CHANNEL_COUNT; ++i) {
        if (!m_velocityTimestamps[i].isEmpty()) {
            if (!hasValidTimestamp || m_velocityTimestamps[i].first() < firstTimestamp) {
                firstTimestamp = m_velocityTimestamps[i].first();
            }
            if (!hasValidTimestamp || m_velocityTimestamps[i].last() > lastTimestamp) {
                lastTimestamp = m_velocityTimestamps[i].last();
            }
            hasValidTimestamp = true;
        }
    }

    if (hasValidTimestamp) {
        m_startTime = QDateTime::currentDateTime().toMSecsSinceEpoch() - lastTimestamp;

        // 设置X轴范围以显示所有数据
        double xMargin = (lastTimestamp - firstTimestamp) * 0.05; // 添加5%的边距
        if (xMargin < 1.0) xMargin = 1.0; // 确保至少有1ms的边距
        m_customPlot->xAxis->setRange(firstTimestamp - xMargin, lastTimestamp + xMargin);
    }

    // 居中显示
    onCenterButtonClicked();

    // 重绘图表
    m_customPlot->replot(QCustomPlot::rpQueuedReplot);

    QMessageBox::information(this, "导入成功", "数据已成功从CSV文件导入。");
    emit apiStatusChanged("数据已从文件导入: " + filePath, true);
}

void OscilloscopeWidget::onPlotMouseMove(QMouseEvent *event)
{
    // 如果正在拖动标记线
    if (m_isDraggingMarker && m_draggedMarkerIndex >= 0) {
        // 获取鼠标位置对应的坐标
        double x = m_customPlot->xAxis->pixelToCoord(event->pos().x());
        double y = m_customPlot->yAxis->pixelToCoord(event->pos().y());

        if (m_isDraggingHorizontal) {
            // 更新水平标记线位置
            m_horizontalMarkers[m_draggedMarkerIndex]->point1->setCoords(0, y);
            m_horizontalMarkers[m_draggedMarkerIndex]->point2->setCoords(1, y);

            // 更新相对位置
            double yMin = m_customPlot->yAxis->range().lower;
            double yMax = m_customPlot->yAxis->range().upper;
            double yRange = yMax - yMin;
            if (yRange > 0) {
                m_horizontalMarkerRelativePositions[m_draggedMarkerIndex] =
                    (y - yMin) / yRange;

                // 确保相对位置在0-1范围内
                m_horizontalMarkerRelativePositions[m_draggedMarkerIndex] =
                    qBound(0.0, m_horizontalMarkerRelativePositions[m_draggedMarkerIndex], 1.0);
            }
        } else {
            // 更新垂直标记线位置
            m_verticalMarkers[m_draggedMarkerIndex]->point1->setCoords(x, 0);
            m_verticalMarkers[m_draggedMarkerIndex]->point2->setCoords(x, 1);

            // 更新相对位置
            double xRange = m_customPlot->xAxis->range().upper - m_customPlot->xAxis->range().lower;
            if (xRange > 0) {
                m_verticalMarkerRelativePositions[m_draggedMarkerIndex] =
                    (x - m_customPlot->xAxis->range().lower) / xRange;

                // 确保相对位置在0-1范围内
                m_verticalMarkerRelativePositions[m_draggedMarkerIndex] =
                    qBound(0.0, m_verticalMarkerRelativePositions[m_draggedMarkerIndex], 1.0);
            }
        }

        // 更新标记线标签
        updateMarkerLabels();

        // 重绘图表
        m_customPlot->replot(QCustomPlot::rpQueuedReplot);
        return;
    }

    // 查找最近的数据点
    double x = m_customPlot->xAxis->pixelToCoord(event->pos().x());
    double y = m_customPlot->yAxis->pixelToCoord(event->pos().y());

    // 查找最近的可见图表和数据点
    QCPGraph *closestGraph = nullptr;
    int closestDataPoint = -1;
    double minDistance = std::numeric_limits<double>::max();

    for (int i = 0; i < TOTAL_CHANNEL_COUNT; ++i) {
        if (!m_graphs[i]->visible() || m_graphs[i]->dataCount() == 0) {
            continue;
        }

        // 获取图表数据
        QCPGraphDataContainer::const_iterator begin = m_graphs[i]->data()->constBegin();
        QCPGraphDataContainer::const_iterator end = m_graphs[i]->data()->constEnd();

        // 查找最近的数据点
        QCPGraphDataContainer::const_iterator it = m_graphs[i]->data()->findBegin(x);
        while (it != end) {
            double dist = qSqrt(qPow(it->key - x, 2) + qPow(it->value - y, 2));
            if (dist < minDistance) {
                minDistance = dist;
                closestGraph = m_graphs[i];
                closestDataPoint = it - begin;
            }

            ++it;
            if (it != end && it->key > x) {
                break;
            }
        }
    }

    // 鼠标移动不影响固定的标签，只更新浮动跟踪器

    // 如果找到最近的数据点，显示跟踪器
    if (closestGraph && closestDataPoint >= 0 && minDistance < 100) { // 增加距离阈值，使数据点更容易显示
        // 获取数据点坐标
        QCPGraphDataContainer::const_iterator it = closestGraph->data()->constBegin() + closestDataPoint;
        double pointX = it->key;
        double pointY = it->value;

        // 更新跟踪器位置
        m_tracer->setGraph(closestGraph);
        m_tracer->setGraphKey(pointX);
        m_tracer->updatePosition();

        // 更新跟踪器标签
        m_tracerLabel->setText(QString("时间: %1 ms\n值: %2").arg(pointX, 0, 'f', 1).arg(pointY, 0, 'f', 2));
        m_tracerLabel->position->setCoords(pointX, pointY);

        // 显示跟踪器和标签
        m_tracer->setVisible(true);
        m_tracerLabel->setVisible(true);

        // 重绘图表
        m_customPlot->replot(QCustomPlot::rpQueuedReplot);
    } else {
        // 如果没有找到最近的数据点，隐藏跟踪器
        if (m_tracer->visible() || m_tracerLabel->visible()) {
            m_tracer->setVisible(false);
            m_tracerLabel->setVisible(false);
            m_customPlot->replot(QCustomPlot::rpQueuedReplot);
        }
    }
}

void OscilloscopeWidget::onPlotMouseWheel(QWheelEvent *event)
{
    // 默认的滚轮事件处理已经完成了缩放
    // 在缩放后，我们需要更新标记线位置，使其保持在相对固定的位置
    Q_UNUSED(event); // 防止未使用参数警告

    // 更新垂直标记线位置
    if (m_verticalMarkerCheckBox->isChecked()) {
        double xMin = m_customPlot->xAxis->range().lower;
        double xMax = m_customPlot->xAxis->range().upper;
        double xRange = xMax - xMin;
        for (int i = 0; i < 2; ++i) {
            double xPos = xMin + xRange * m_verticalMarkerRelativePositions[i];
            m_verticalMarkers[i]->point1->setCoords(xPos, 0);
            m_verticalMarkers[i]->point2->setCoords(xPos, 1);
        }
    }

    // 更新水平标记线位置
    if (m_horizontalMarkerCheckBox->isChecked()) {
        double yMin = m_customPlot->yAxis->range().lower;
        double yMax = m_customPlot->yAxis->range().upper;
        double yRange = yMax - yMin;
        for (int i = 0; i < 2; ++i) {
            double yPos = yMin + yRange * m_horizontalMarkerRelativePositions[i];
            m_horizontalMarkers[i]->point1->setCoords(0, yPos);
            m_horizontalMarkers[i]->point2->setCoords(1, yPos);
        }
    }

    // 更新标记线标签
    updateMarkerLabels();

    // 重绘图表
    m_customPlot->replot(QCustomPlot::rpQueuedReplot);
}

void OscilloscopeWidget::onPlotMousePress(QMouseEvent *event)
{
    // 获取鼠标位置对应的坐标
    double x = m_customPlot->xAxis->pixelToCoord(event->pos().x());
    double y = m_customPlot->yAxis->pixelToCoord(event->pos().y());

    // 检查是否点击了标记线 - 左键拖动
    if (event->button() == Qt::LeftButton) {
        // 检查水平标记线
        for (int i = 0; i < 2; ++i) {
            if (m_horizontalMarkers[i]->visible()) {
                double markerY = m_horizontalMarkers[i]->point1->coords().y();

                // 计算鼠标位置到水平标记线的像素距离
                double pixelY = m_customPlot->yAxis->coordToPixel(markerY);
                double mousePixelY = event->pos().y();
                double pixelDistance = qAbs(mousePixelY - pixelY);

                // 使用像素距离判断，允许更大的误差范围
                if (pixelDistance < 15) { // 15像素的误差范围
                    m_isDraggingMarker = true;
                    m_draggedMarkerIndex = i;
                    m_isDraggingHorizontal = true;
                    return;
                }
            }
        }

        // 检查垂直标记线
        for (int i = 0; i < 2; ++i) {
            if (m_verticalMarkers[i]->visible()) {
                double markerX = m_verticalMarkers[i]->point1->coords().x();

                // 计算鼠标位置到垂直标记线的像素距离
                double pixelX = m_customPlot->xAxis->coordToPixel(markerX);
                double mousePixelX = event->pos().x();
                double pixelDistance = qAbs(mousePixelX - pixelX);

                // 使用像素距离判断，允许更大的误差范围
                if (pixelDistance < 15) { // 15像素的误差范围
                    m_isDraggingMarker = true;
                    m_draggedMarkerIndex = i;
                    m_isDraggingHorizontal = false;
                    return;
                }
            }
        }

        // 查找最近的数据点
        QCPGraph *closestGraph = nullptr;
        int closestDataPoint = -1;
        double minDistance = std::numeric_limits<double>::max();

        for (int i = 0; i < TOTAL_CHANNEL_COUNT; ++i) {
            if (!m_graphs[i]->visible() || m_graphs[i]->dataCount() == 0) {
                continue;
            }

            // 获取图表数据
            QCPGraphDataContainer::const_iterator begin = m_graphs[i]->data()->constBegin();
            QCPGraphDataContainer::const_iterator end = m_graphs[i]->data()->constEnd();

            // 查找最近的数据点
            QCPGraphDataContainer::const_iterator it = m_graphs[i]->data()->findBegin(x);
            while (it != end) {
                double dist = qSqrt(qPow(it->key - x, 2) + qPow(it->value - y, 2));
                if (dist < minDistance) {
                    minDistance = dist;
                    closestGraph = m_graphs[i];
                    closestDataPoint = it - begin;
                }

                ++it;
                if (it != end && it->key > x) {
                    break;
                }
            }
        }

        // 如果找到最近的数据点，创建固定标签
        if (closestGraph && closestDataPoint >= 0 && minDistance < 50) { // 50个单位的距离阈值
            // 获取数据点坐标
            QCPGraphDataContainer::const_iterator it = closestGraph->data()->constBegin() + closestDataPoint;
            double pointX = it->key;
            double pointY = it->value;

            // 创建固定标签
            createFixedLabel(closestGraph, pointX, pointY);
            return;
        } else {
            // 如果点击的是空白处，清除所有固定标签
            clearAllFixedLabels();
            return;
        }
    }
    // 右键点击 - 固定标记线位置
    else if (event->button() == Qt::RightButton) {
        // 如果正在拖动标记线，则停止拖动并固定位置
        if (m_isDraggingMarker && m_draggedMarkerIndex >= 0) {
            m_isDraggingMarker = false;
            m_draggedMarkerIndex = -1;

            // 更新标记线标签
            updateMarkerLabels();

            // 重绘图表
            m_customPlot->replot(QCustomPlot::rpQueuedReplot);
            return;
        }
    }

    // 如果松开左键，停止拖动
    if (event->button() == Qt::LeftButton && m_isDraggingMarker) {
        m_isDraggingMarker = false;
        m_draggedMarkerIndex = -1;
    }
}

// 创建固定标签
void OscilloscopeWidget::createFixedLabel(QCPGraph *graph, double key, double value)
{
    // 检查是否已经有相同位置的标签
    for (int i = 0; i < m_fixedTracers.size(); ++i) {
        QCPItemTracer *tracer = m_fixedTracers[i];
        if (qAbs(tracer->position->key() - key) < 1.0 && qAbs(tracer->position->value() - value) < 1.0) {
            // 已经有相同位置的标签，不重复创建
            return;
        }
    }

    // 创建新的跟踪器
    QCPItemTracer *tracer = new QCPItemTracer(m_customPlot);
    tracer->setStyle(QCPItemTracer::tsCircle);
    tracer->setSize(9);
    tracer->setPen(QPen(Qt::black, 2));
    tracer->setBrush(graph->pen().color());
    tracer->setGraph(graph);
    tracer->setGraphKey(key);
    tracer->updatePosition();

    // 创建新的标签
    QCPItemText *label = new QCPItemText(m_customPlot);
    label->setPositionAlignment(Qt::AlignLeft | Qt::AlignTop);
    label->setTextAlignment(Qt::AlignLeft);
    label->setPadding(QMargins(5, 5, 5, 5));
    label->setBrush(QBrush(QColor(255, 255, 255, 200)));
    label->setPen(QPen(Qt::black));
    label->setText(QString("时间: %1 ms\n值: %2\n通道: %3").arg(key, 0, 'f', 1).arg(value, 0, 'f', 2).arg(graph->name()));
    label->position->setCoords(key, value);

    // 添加到列表中
    m_fixedTracers.append(tracer);
    m_fixedLabels.append(label);

    // 重绘图表
    m_customPlot->replot(QCustomPlot::rpQueuedReplot);
}

// 清除所有固定标签
void OscilloscopeWidget::clearAllFixedLabels()
{
    // 删除所有固定跟踪器和标签
    for (QCPItemTracer *tracer : m_fixedTracers) {
        m_customPlot->removeItem(tracer);
    }
    for (QCPItemText *label : m_fixedLabels) {
        m_customPlot->removeItem(label);
    }

    m_fixedTracers.clear();
    m_fixedLabels.clear();

    // 重绘图表
    m_customPlot->replot(QCustomPlot::rpQueuedReplot);
}



void OscilloscopeWidget::onHorizontalMarkerCheckBoxChanged(int state)
{
    bool visible = (state == Qt::Checked);

    if (visible) {
        // 获取当前Y轴范围
        double yMin = m_customPlot->yAxis->range().lower;
        double yMax = m_customPlot->yAxis->range().upper;
        double yRange = yMax - yMin;

        // 使用相对位置设置水平标记线位置
        for (int i = 0; i < 2; ++i) {
            double yPos = yMin + yRange * m_horizontalMarkerRelativePositions[i];
            m_horizontalMarkers[i]->point1->setCoords(0, yPos);
            m_horizontalMarkers[i]->point2->setCoords(1, yPos);
        }
    }

    // 显示或隐藏水平标记线
    for (int i = 0; i < 2; ++i) {
        m_horizontalMarkers[i]->setVisible(visible);
        m_horizontalMarkerLabels[i]->setVisible(visible);
    }

    // 更新差值标签
    updateMarkerLabels();

    // 重绘图表
    m_customPlot->replot(QCustomPlot::rpQueuedReplot);
}

void OscilloscopeWidget::onVerticalMarkerCheckBoxChanged(int state)
{
    bool visible = (state == Qt::Checked);

    if (visible) {
        // 获取当前X轴范围
        double xMin = m_customPlot->xAxis->range().lower;
        double xMax = m_customPlot->xAxis->range().upper;
        double xMiddle = (xMin + xMax) / 2.0;
        double xRange = xMax - xMin;

        // 设置垂直标记线位置在中间线的左右各1/4处
        double xPos1 = xMiddle - xRange * 0.25;
        double xPos2 = xMiddle + xRange * 0.25;

        // 更新垂直标记线位置
        m_verticalMarkers[0]->point1->setCoords(xPos1, 0);
        m_verticalMarkers[0]->point2->setCoords(xPos1, 1);
        m_verticalMarkers[1]->point1->setCoords(xPos2, 0);
        m_verticalMarkers[1]->point2->setCoords(xPos2, 1);

        // 更新相对位置
        m_verticalMarkerRelativePositions[0] = (xPos1 - xMin) / xRange;
        m_verticalMarkerRelativePositions[1] = (xPos2 - xMin) / xRange;
    }

    // 显示或隐藏垂直标记线
    for (int i = 0; i < 2; ++i) {
        m_verticalMarkers[i]->setVisible(visible);
        m_verticalMarkerLabels[i]->setVisible(visible);
    }

    // 更新差值标签
    updateMarkerLabels();

    // 重绘图表
    m_customPlot->replot(QCustomPlot::rpQueuedReplot);
}

// 降采样数据 - 使用简单的均匀采样
QVector<double> OscilloscopeWidget::downsampleData(const QVector<double> &data, int targetSize)
{
    if (data.size() <= targetSize || targetSize <= 0) {
        return data; // 如果数据点数量不超过目标大小，或目标大小无效，直接返回原始数据
    }

    QVector<double> result;
    result.reserve(targetSize);

    // 计算采样间隔
    double step = static_cast<double>(data.size() - 1) / (targetSize - 1);

    // 均匀采样
    for (int i = 0; i < targetSize; ++i) {
        double pos = i * step;
        int index = static_cast<int>(pos);
        double fraction = pos - index;

        if (index >= data.size() - 1) {
            result.append(data.last());
        } else {
            // 线性插值
            double value = data[index] * (1 - fraction) + data[index + 1] * fraction;
            result.append(value);
        }
    }

    return result;
}

// 准备图表数据 - 将时间和值数组转换为QCPGraphData数组，并进行降采样
QVector<QCPGraphData> OscilloscopeWidget::prepareGraphData(const QVector<double> &keys, const QVector<double> &values, int maxPoints)
{
    if (keys.size() != values.size() || keys.isEmpty()) {
        return QVector<QCPGraphData>(); // 如果输入无效，返回空数组
    }

    // 如果数据点数量超过最大值，进行降采样
    QVector<double> sampledKeys;
    QVector<double> sampledValues;

    if (keys.size() > maxPoints) {
        sampledKeys = downsampleData(keys, maxPoints);
        sampledValues = downsampleData(values, maxPoints);
    } else {
        sampledKeys = keys;
        sampledValues = values;
    }

    // 创建QCPGraphData数组
    QVector<QCPGraphData> result;
    result.reserve(sampledKeys.size());

    for (int i = 0; i < sampledKeys.size(); ++i) {
        QCPGraphData point;
        point.key = sampledKeys[i];
        point.value = sampledValues[i];
        result.append(point);
    }

    return result;
}
