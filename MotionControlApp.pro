QT       += core gui concurrent svg printsupport

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

TARGET = MotionControlApp
TEMPLATE = app
VERSION = 1.0.0
CONFIG += c++14
#CONFIG += resources_big

CONFIG += debug_and_release
CONFIG += build_all

# 设置基础目录
BASE_DIR = $$PWD
BUILD_DIR = $$BASE_DIR/build
BIN_DIR = $$BASE_DIR/bin

# 根据构建模式设置
CONFIG(debug, debug|release) {
    DESTDIR = $$BIN_DIR/debug
    OBJECTS_DIR = $$BUILD_DIR/debug
    MOC_DIR = $$BUILD_DIR/debug/moc
    RCC_DIR = $$BUILD_DIR/debug/rcc
    UI_DIR = $$BUILD_DIR/debug/ui
    TARGET = $$join(TARGET,,,_debug)
} else {
    DESTDIR = $$BIN_DIR/release
    OBJECTS_DIR = $$BUILD_DIR/release
    MOC_DIR = $$BUILD_DIR/release/moc
    RCC_DIR = $$BUILD_DIR/release/rcc
    UI_DIR = $$BUILD_DIR/release/ui
}



# 添加includeMotion目录到包含路径
#INCLUDEPATH += $$PWD/DllMotion/include                              #旧库
INCLUDEPATH += $$PWD/../ADAMotion/ADMotion/include                   #新库

# Windows平台下的系统库链接
win32 {
    LIBS += -lws2_32 -luser32 -lwinmm

#    # 根据构建配置链接不同版本的库    #旧版本库引用
#    CONFIG(debug, debug|release) {
#        # Debug模式下链接Debug版本的库
#        LIBS += -LI:\ZhanTing\Anda.Mc.Replica02-picture\Anda.Mc.Replica02\Anda.Mc.Replica-qtlib\Anda.Mc.Replica\DllMotion\debug
#    } else {
#        # Release模式下链接Release版本的库
#        #LIBS += -L..ADAMotion/ADMotion/motiondll/release/release
#        LIBS += -LI:\ZhanTing\Anda.Mc.Replica02-picture\Anda.Mc.Replica02\Anda.Mc.Replica-qtlib\Anda.Mc.Replica\DllMotion\release
#    }
    #新版本库引用
    CONFIG(debug, debug|release) {
        LIBS += -L$$PWD/../ADAMotion/ADMotion/bin/debug -lADMotiond_debug
        # 复制dll到exe目录
        #QMAKE_POST_LINK += $$quote(cmd /c copy /Y \"$$PWD/../ADAMotion/ADMotion/bin/debug/ADMotiond_debug.dll\" \"$$PWD/bin/debug\")$$escape_expand(\\n\\t)
    } else {
        LIBS += -L$$PWD/../ADAMotion/ADMotion/bin/release -lADMotion
        #QMAKE_POST_LINK += $$quote(cmd /c copy /Y \"$$PWD/../ADAMotion/ADMotion/bin/release/ADMotion.dll\" \"$$PWD/bin/release\")$$escape_expand(\\n\\t)
    }

    # 添加Lua库链接
    LIBS += -L$$PWD/luadll -llua
}
DEPENDPATH += $$PWD/../ADAMotion/ADMotion

SOURCES += \
    main.cpp \
    mainwindow.cpp \
    axiscontrolwidget.cpp \
    homewidget.cpp \
    trapwidget.cpp \
    statuswidget.cpp \
    connectiondialog.cpp \
    admc_api_wrapper.cpp \
    interpolationwidget.cpp \
    axisinitwidget.cpp \
    axismappingwidget.cpp \
    iowidget.cpp \
    unitconverter.cpp \
    programwidget.cpp \
    programexecutor.cpp \
    luaexecutor.cpp \
    luaprogramwidget.cpp \
    oscilloscopewidget.cpp \
    qcustomplot.cpp

HEADERS += \
    DllMotion/include/ADOSType.h \
    DllMotion/include/adconfig.h \
    DllMotion/include/admc_pci.h \
    DllMotion/include/motion_enums.h \
    DllMotion/include/motion_types.h \
    includeMotion/ADOSType.h \
    includeMotion/adconfig.h \
    includeMotion/admc_info.h \
    includeMotion/admc_pci.h \
    includeMotion/systemopt.h \
    mainwindow.h \
    axiscontrolwidget.h \
    homewidget.h \
    trapwidget.h \
    statuswidget.h \
    connectiondialog.h \
    admc_api_wrapper.h \
    interpolationwidget.h \
    axisinitwidget.h \
    axismappingwidget.h \
    iowidget.h \
    unitconverter.h \
    programexecutor.h \
    programwidget.h \
    luaexecutor.h \
    luaprogramwidget.h \
    oscilloscopewidget.h \
    qcustomplot.h

FORMS += \
    mainwindow.ui \
    axiscontrolwidget.ui \
    homewidget.ui \
    trapwidget.ui \
    statuswidget.ui \
    interpolationwidget.ui \
    programwidget.ui

# 资源文件
RESOURCES += resources.qrc

# 默认规则
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

