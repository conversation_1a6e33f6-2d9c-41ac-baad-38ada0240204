UI文件修改指南

在AxisControlWidget.ui中需要添加Jog控制子界面，具体修改如下：

1. 在"运动控制"组框(groupBox)和"实时状态"组框(groupBox_2)之间添加一个新的组框(groupBox_jog)，命名为"Jog控制"。

2. 在"Jog控制"组框中添加以下控件：
   a. 添加一个水平布局(horizontalLayout_jog)
   b. 在水平布局中添加一个网格布局(gridLayout_jog)
   c. 在网格布局中添加以下控件：
      - 行0列0: 标签(label_jog_speed)，文本为"速度:"
      - 行0列1: 双精度数字输入框(spinJogSpeed)，范围1-1000，后缀" mm/s"，默认值50
      - 行1列0: 标签(label_jog_acc)，文本为"加速度:"
      - 行1列1: 双精度数字输入框(spinJogAcc)，范围10-10000，后缀" mm/s²"，默认值500
      - 行2列0: 标签，文本为"操作:"
      - 行2列1: 水平布局，包含以下按钮：
         * 获取参数按钮(btnJogParams)，文本为"获取参数"
         * 负向点动按钮(btnJogNegative)，文本为"←"
         * 正向点动按钮(btnJogPositive)，文本为"→"

完成以上修改后，将对应的控件ID与AxisControlWidget.cpp中的代码连接起来。

注意：确保所有新添加的控件都有明确的对象名称，以便在源代码中引用。
