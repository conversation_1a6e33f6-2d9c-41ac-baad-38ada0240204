#include "unitconverter.h"

// 初始化静态成员变量
UnitConverter* UnitConverter::m_instance = nullptr;

// 单例实现
UnitConverter* UnitConverter::getInstance()
{
    if (m_instance == nullptr) {
        m_instance = new UnitConverter();
    }
    return m_instance;
}

UnitConverter::UnitConverter(QObject* parent)
    : QObject(parent),
      m_currentUnitType(UNIT_PULSE) // 默认使用脉冲单位
{
}

UnitConverter::~UnitConverter()
{
}

// 获取当前单位类型
UnitType UnitConverter::getCurrentUnitType() const
{
    return m_currentUnitType;
}

// 设置当前单位类型
void UnitConverter::setCurrentUnitType(UnitType type)
{
    if (m_currentUnitType != type) {
        m_currentUnitType = type;
        emit unitTypeChanged(type);
    }
}

// 位置转换函数
double UnitConverter::pulseToMm(double pulse) const
{
    return pulse * PULSE_TO_MM;
}

double UnitConverter::mmToPulse(double mm) const
{
    return mm * MM_TO_PULSE;
}

// 速度转换函数
double UnitConverter::pulsePerMsToMmPerS(double pulsePerMs) const
{
    // 1 pulse/ms = 1 mm/s (因为1000 pulse = 1 mm, 1000 ms = 1 s)
    return pulsePerMs;
}

double UnitConverter::mmPerSToPulsePerMs(double mmPerS) const
{
    // 1 mm/s = 1 pulse/ms
    return mmPerS;
}

// 加速度转换函数
double UnitConverter::pulsePerMsSquaredToMmPerSSquared(double pulsePerMsSquared) const
{
    // 1 pulse/ms^2 = 1000 mm/s^2
    return pulsePerMsSquared * 1000.0;
}

double UnitConverter::mmPerSSquaredToPulsePerMsSquared(double mmPerSSquared) const
{
    // 1 mm/s^2 = 0.001 pulse/ms^2
    return mmPerSSquared * 0.001;
}

// 获取单位字符串
QString UnitConverter::getPositionUnitString() const
{
    return (m_currentUnitType == UNIT_PULSE) ? "pulse" : "mm";
}

QString UnitConverter::getVelocityUnitString() const
{
    return (m_currentUnitType == UNIT_PULSE) ? "pulse/ms" : "mm/s";
}

QString UnitConverter::getAccelerationUnitString() const
{
    return (m_currentUnitType == UNIT_PULSE) ? "pulse/ms²" : "mm/s²";
}

// 格式化数值显示
QString UnitConverter::formatPosition(double value) const
{
    if (m_currentUnitType == UNIT_PULSE) {
        return QString::number(static_cast<int>(value)); // 脉冲显示为整数
    } else {
        return QString::number(pulseToMm(value), 'f', 3); // 毫米显示为3位小数
    }
}

QString UnitConverter::formatVelocity(double value) const
{
    if (m_currentUnitType == UNIT_PULSE) {
        return QString::number(value, 'f', 0); // 脉冲/毫秒显示为整数
    } else {
        return QString::number(pulsePerMsToMmPerS(value), 'f', 1); // 毫米/秒显示为1位小数
    }
}

QString UnitConverter::formatAcceleration(double value) const
{
    if (m_currentUnitType == UNIT_PULSE) {
        return QString::number(value, 'f', 0); // 脉冲/毫秒²显示为整数
    } else {
        return QString::number(pulsePerMsSquaredToMmPerSSquared(value), 'f', 1); // 毫米/秒²显示为1位小数
    }
}
