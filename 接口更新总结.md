# ADMC_PCI 接口更新总结

## 概述
本次更新将原有的 `DllMotion/include/admc_pci.h` 接口替换为新的 `newMotionlib/include/admc_pci.h` 接口。主要变化是将结构体参数改为直接参数传递，并统一了轴和坐标系的概念。

## 主要变化

### 1. 接口参数变化

#### JOG 参数设置
- **旧接口**: `API_SetJogPrm(handle, crd, TJogPrm* pPrm)`
- **新接口**: `API_SetJogPrm(handle, crd, int32_t Maxvel, int32_t acc, int32_t dec, int32_t rate)`

#### 坐标系点位参数设置
- **旧接口**: `API_SetCrdTrapPrm(handle, crd, TTrapPrm* pPrm)`
- **新接口**: `API_SetCrdTrapPrm(handle, crd, double startPos[2], double posTarget[2], double velMax, double acc, short rat)`

#### 轴点位参数设置
- **旧接口**: `API_SetAxisTrapPrm(handle, crd, axis, TTrapPrm* pPrm)`
- **新接口**: `API_SetAxisTrapPrm(handle, axis, double IncrPos, double velMax, double acc, short rat)`

#### 坐标系参数设置
- **旧接口**: `API_SetCrdPrm(handle, crd, TCrdPrm* pCrdPrm)`
- **新接口**: `API_SetCrdPrm(handle, crd, double synVelMax, double synAccMax)`

#### 错误码获取
- **旧接口**: `API_GetErrorCode(handle, crd, axis, ErrorCode)`
- **新接口**: `API_GetErrorCode(handle, axis, ErrorCode)` - 移除了crd参数

#### 插补指令
- **旧接口**: `API_Ln(handle, crd, x, y, synVel, synAcc, velEnd)`
- **新接口**: `API_Ln(handle, crd, x, y, synVel, synAcc)` - 移除了velEnd参数
- **旧接口**: `API_ArcXYR(handle, crd, x, y, radius, circleDir, synVel, synAcc, velEnd)`
- **新接口**: `API_ArcXYR(handle, crd, x, y, radius, circleDir, synVel, synAcc)` - 移除了velEnd参数

### 2. 轴和坐标系统一
新接口统一了轴和坐标系的概念，axis和crd之间的计算在接口内部处理：
- 轴点位模式：`API_SetAxisTrapMode(handle, axis)` - 只需要轴号
- 轴点位更新：`API_AxisTrapUpdate(handle, crd)` - 仍使用坐标系号，但内部会处理轴映射

## 修改的文件

### 1. 核心接口文件
- `admc_api_wrapper.h` - 更新了方法签名
- `admc_api_wrapper.cpp` - 更新了所有相关方法的实现

### 2. 界面模块
- `axiscontrolwidget.cpp` - 更新JOG参数设置
- `jogwidget.cpp` - 更新轴点位和坐标系点位参数设置
- `trapwidget.cpp` - 更新点位运动参数设置
- `homewidget.cpp` - 无需修改（未直接使用相关接口）
- `statuswidget.cpp` - 更新错误码获取调用
- `interpolationwidget.cpp` - 更新坐标系参数设置

### 3. 编程模块
- `programexecutor.cpp` - 更新AxisTrapCommand和LinearMoveCommand类
- `luaexecutor.cpp` - 更新Lua脚本中的API调用

## 保持的兼容性

### 1. 废弃但保留的方法
以下方法已标记为废弃，但为了向后兼容仍然保留：
- `TrapWidget::getAxisTrapParameters()`
- `TrapWidget::getCrdTrapParameters()`
- `JogWidget::getAxisTrapParameters()`
- `JogWidget::getCrdTrapParameters()`

### 2. 轴映射功能
`API_AxisMapping` 函数在新接口中可能不存在，相关功能可能需要通过其他API实现。

## 注意事项

### 1. 单位转换
所有涉及单位转换的地方都已经适配新接口，确保mm和pulse单位之间的正确转换。

### 2. 增量位置计算
轴点位运动现在使用增量位置（目标位置 - 当前位置），而不是绝对位置。

### 3. 错误处理
保持了原有的错误处理逻辑，只是调用方式有所改变。

### 4. 编程模块逻辑
严格按照要求保持了编程模块的原有逻辑不变，只替换了接口函数调用。

## 测试建议

1. **基本功能测试**
   - JOG运动测试
   - 轴点位运动测试
   - 坐标系点位运动测试
   - 直线插补测试
   - 圆弧插补测试

2. **编程模块测试**
   - 程序编辑功能
   - 程序执行功能
   - Lua脚本执行功能

3. **单位转换测试**
   - mm和pulse单位切换
   - 参数显示正确性

4. **错误处理测试**
   - 错误码获取和显示
   - 异常情况处理

## 总结
本次更新成功将所有相关代码适配到新的接口，保持了原有功能的完整性，特别是编程模块的逻辑完整性。新接口的使用更加直观，减少了结构体的使用，提高了代码的可维护性。
