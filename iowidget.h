#ifndef IOWIDGET_H
#define IOWIDGET_H

#include <QWidget>
#include <QTableWidget>
#include <QRadioButton>
#include <QButtonGroup>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGroupBox>
#include <QLabel>
#include <QTimer>
#include <QHeaderView>
#include "admc_api_wrapper.h"

class IOWidget : public QWidget
{
    Q_OBJECT

public:
    explicit IOWidget(QWidget *parent = nullptr);
    ~IOWidget();

private slots:
    void onInputRadioToggled(bool checked);
    void onOutputRadioToggled(bool checked);
    void updateIOStatus();
    void onCellClicked(int row, int column);

private:
    // UI组件
    QRadioButton* m_radioInput;
    QRadioButton* m_radioOutput;
    QButtonGroup* m_buttonGroup;
    QTableWidget* m_tableWidget;
    QTimer* m_refreshTimer;

    // 数据
    int32_t m_deviceInput[16];
    int m_deviceOutput[16];
    bool m_showingInput;

    // 初始化UI
    void setupUI();
    
    // 更新表格
    void updateTable();
    
    // 设置输出
    void setDeviceOutput(int index, bool value);
    
    // 获取输入
    void getDeviceInput();

signals:
    void apiStatusChanged(const QString& message, bool isSuccess);
};

#endif // IOWIDGET_H
