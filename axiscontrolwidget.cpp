#include "axiscontrolwidget.h"
#include "ui_axiscontrolwidget.h"
#include <QMessageBox>
#include <QRandomGenerator>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QLabel>
#include <QDoubleSpinBox>
#include <QPushButton>
//#include "admc_info.h"

AxisControlWidget::AxisControlWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::AxisControlWidget),
    m_apiWrapper(AdmcApiWrapper::getInstance()),
    m_unitConverter(UnitConverter::getInstance()),
    m_isJogging(false),
    m_jogDirection(0),
    m_currentAxis(0),
    m_currentCrd(0)
{
    ui->setupUi(this);

    // 初始化控件
    for (int i = 0; i < 4; ++i) {
        ui->comboAxis->addItem(QString("轴 %1").arg(i));
    }

    // 轴选择与控制信号槽已移除

    // 添加Jog控制界面 - 如果无法直接修改UI文件，可以通过代码动态创建
    createJogInterface();

    // 连接单位转换器的信号
    connect(m_unitConverter, &UnitConverter::unitTypeChanged, this, &AxisControlWidget::onUnitTypeChanged);

    // 创建单位切换按钮
    QGroupBox* unitGroupBox = new QGroupBox("单位设置");
    QVBoxLayout* unitLayout = new QVBoxLayout(unitGroupBox);

    QComboBox* unitComboBox = new QComboBox();
    unitComboBox->addItem("pulse");
    unitComboBox->addItem("mm");
    unitComboBox->setCurrentIndex(m_unitConverter->getCurrentUnitType());

    QPushButton* unitSwitchButton = new QPushButton("切换单位");
    connect(unitSwitchButton, &QPushButton::clicked, this, &AxisControlWidget::onUnitSwitchClicked);

    unitLayout->addWidget(new QLabel("选择单位类型："));
    unitLayout->addWidget(unitComboBox);
    unitLayout->addWidget(unitSwitchButton);

    // 将单位设置模块添加到主布局
    QVBoxLayout *mainLayout = qobject_cast<QVBoxLayout*>(layout());
    if (mainLayout) {
        mainLayout->addWidget(unitGroupBox);
    }

    connect(unitComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), [this, unitComboBox](int index) {
        m_unitConverter->setCurrentUnitType(static_cast<UnitType>(index));
    });

    // 设置默认参数值
    setDefaultParameters();

    // 更新单位显示
    updateUnitDisplay();

    // 不再使用定时器更新状态
}

AxisControlWidget::~AxisControlWidget()
{
    delete ui;
}

void AxisControlWidget::enableAxis()
{
    int axis = ui->comboAxis->currentIndex();

    // 在实际应用中，这里应该调用底层API使能轴
    QString status = QString("轴%1已使能").arg(axis);

    // 发送API状态信号
    emit apiStatusChanged(QString("轴%1已成功使能").arg(axis), true);

    QMessageBox::information(this, "使能轴", QString("轴%1已成功使能").arg(axis));
}

void AxisControlWidget::disableAxis()
{
    int axis = ui->comboAxis->currentIndex();

    if (m_isJogging) {
        QMessageBox::warning(this, "警告", "轴正在JOG运动中，请先停止运动!");
        return;
    }

    // 在实际应用中，这里应该调用底层API禁用轴
    QString status = QString("轴%1已禁用").arg(axis);

    // 发送API状态信号
    emit apiStatusChanged(QString("轴%1已成功禁用").arg(axis), true);

    QMessageBox::information(this, "禁用轴", QString("轴%1已成功禁用").arg(axis));
}

void AxisControlWidget::resetAxis()
{
    int axis = ui->comboAxis->currentIndex();

    if (m_isJogging) {
        QMessageBox::warning(this, "警告", "轴正在JOG运动中，请先停止运动!");
        return;
    }

    // 在实际应用中，这里应该调用底层API复位轴
    QString status = QString("轴%1已复位").arg(axis);

    // 发送API状态信号
    emit apiStatusChanged(QString("轴%1已成功复位").arg(axis), true);

    QMessageBox::information(this, "复位轴", QString("轴%1已成功复位").arg(axis));
}



void AxisControlWidget::startJogPositive()
{
    m_currentAxis = ui->comboAxis->currentIndex();
    m_currentCrd = m_currentAxis/2;
    m_currentAxis = m_currentAxis%2;
    if (m_isJogging) {
        return;
    }

    // 检查设备连接状态
    if (!m_apiWrapper || !m_apiWrapper->isConnected()) {
        QMessageBox::warning(this, "错误", "设备未连接，无法执行Jog运动");
        return;
    }

    m_isJogging = true;
    m_jogDirection = 1;

    double speed = m_spinJogSpeed->value();
    double acc = m_spinJogAcc->value();

    // 如果当前单位是mm，需要转换为pulse再传给API
    if (m_unitConverter->getCurrentUnitType() == UNIT_MM) {
        // 速度保持不变，因为1mm/s = 1pulse/ms
        // 加速度需要转换，因为1mm/s^2 = 0.001pulse/ms^2
        acc = m_unitConverter->mmPerSSquaredToPulsePerMsSquared(acc);
    }

    // 设置Jog模式
    short result = m_apiWrapper->setJogMode(m_currentCrd);
    if (result != 0) {
        QMessageBox::warning(this, "错误",
            QString("设置Jog模式失败，错误码: %1").arg(result));
        m_isJogging = false;
        return;
    }

    // 设置Jog参数 - 新接口直接传递参数
    int32_t maxvel = static_cast<int32_t>(speed);
    int32_t acceleration = static_cast<int32_t>(acc);
    int32_t deceleration = static_cast<int32_t>(acc); // 使用相同的加减速度
    int32_t rate = 100; // 默认倍率

    result = m_apiWrapper->setJogParameters(m_currentCrd, maxvel, acceleration, deceleration, rate);
    if (result != 0) {
        QMessageBox::warning(this, "错误",
            QString("设置Jog参数失败，错误码: %1").arg(result));
        m_isJogging = false;
        return;
    }

    // 执行Jog运动

    result = m_apiWrapper->jogUpdate(ui->comboAxis->currentIndex(), 1); // 1表示正向
    if (result != 0) {
        QMessageBox::warning(this, "错误",
            QString("执行Jog运动失败，错误码: %1").arg(result));
        m_isJogging = false;
        return;
    }

    // 使用当前显示单位的值来显示状态信息
    QString velUnit = m_unitConverter->getVelocityUnitString();
    QString accUnit = m_unitConverter->getAccelerationUnitString();

    QString status = QString("轴%1正在正向Jog，速度: %2 %3, 加速度: %4 %5")
                   .arg(m_currentAxis)
                   .arg(m_spinJogSpeed->value())
                   .arg(velUnit)
                   .arg(m_spinJogAcc->value())
                   .arg(accUnit);

    // 发送API状态信号
    emit apiStatusChanged(QString("轴%1正向Jog运动已启动").arg(m_currentAxis), true);
}

void AxisControlWidget::startJogNegative()
{
    //m_currentAxis = ui->comboAxis->currentIndex();
    m_currentAxis = ui->comboAxis->currentIndex();
    m_currentCrd = m_currentAxis/2;
    m_currentAxis = m_currentAxis%2;
    if (m_isJogging) {
        return;
    }

    // 检查设备连接状态
    if (!m_apiWrapper || !m_apiWrapper->isConnected()) {
        QMessageBox::warning(this, "错误", "设备未连接，无法执行Jog运动");
        return;
    }

    m_isJogging = true;
    m_jogDirection = -1;

    double speed = m_spinJogSpeed->value();
    double acc = m_spinJogAcc->value();

    // 如果当前单位是mm，需要转换为pulse再传给API
    if (m_unitConverter->getCurrentUnitType() == UNIT_MM) {
        // 速度保持不变，因为1mm/s = 1pulse/ms
        // 加速度需要转换，因为1mm/s^2 = 0.001pulse/ms^2
        acc = m_unitConverter->mmPerSSquaredToPulsePerMsSquared(acc);
    }

    // 设置Jog模式
    short result = m_apiWrapper->setJogMode(m_currentCrd);
    if (result != 0) {
        QMessageBox::warning(this, "错误",
            QString("设置Jog模式失败，错误码: %1").arg(result));
        m_isJogging = false;
        return;
    }

    // 设置Jog参数 - 新接口直接传递参数
    int32_t maxvel = static_cast<int32_t>(speed);
    int32_t acceleration = static_cast<int32_t>(acc);
    int32_t deceleration = static_cast<int32_t>(acc); // 使用相同的加减速度
    int32_t rate = 100; // 默认倍率

    result = m_apiWrapper->setJogParameters(m_currentCrd, maxvel, acceleration, deceleration, rate);
    if (result != 0) {
        QMessageBox::warning(this, "错误",
            QString("设置Jog参数失败，错误码: %1").arg(result));
        m_isJogging = false;
        return;
    }

    // 执行Jog运动
    result = m_apiWrapper->jogUpdate(ui->comboAxis->currentIndex(), -1); // -1表示负向
    if (result != 0) {
        QMessageBox::warning(this, "错误",
            QString("执行Jog运动失败，错误码: %1").arg(result));
        m_isJogging = false;
        return;
    }

    // 使用当前显示单位的值来显示状态信息
    QString velUnit = m_unitConverter->getVelocityUnitString();
    QString accUnit = m_unitConverter->getAccelerationUnitString();

    QString status = QString("轴%1正在负向Jog，速度: %2 %3, 加速度: %4 %5")
                   .arg(m_currentAxis)
                   .arg(m_spinJogSpeed->value())
                   .arg(velUnit)
                   .arg(m_spinJogAcc->value())
                   .arg(accUnit);

    // 发送API状态信号
    emit apiStatusChanged(QString("轴%1负向Jog运动已启动").arg(m_currentAxis), true);
}

void AxisControlWidget::stopJog()
{
    if (!m_isJogging) {
        return;
    }

    // 检查设备连接状态
    if (m_apiWrapper && m_apiWrapper->isConnected()) {
        // 调用底层API停止Jog操作
        short result = m_apiWrapper->jogUpdate(ui->comboAxis->currentIndex(), 0); // 0表示停止
        if (result != 0) {
            QMessageBox::warning(this, "错误",
                QString("停止Jog运动失败，错误码: %1").arg(result));
        } else {
            // 发送API状态信号
            emit apiStatusChanged(QString("轴%1 Jog运动已停止").arg(m_currentAxis), true);
        }
    }

    QString status = QString("轴%1已停止Jog").arg(m_currentAxis);

    // 更新状态
    m_isJogging = false;
    m_jogDirection = 0;
}



// 移除实时状态更新方法

// 创建Jog控制界面
void AxisControlWidget::updateUnitDisplay()
{
    // 获取单位字符串
    QString velUnit = m_unitConverter->getVelocityUnitString();
    QString accUnit = m_unitConverter->getAccelerationUnitString();

    // 更新速度和加速度输入框的单位显示
    if (m_spinJogSpeed) {
        m_spinJogSpeed->setSuffix(QString(" %1").arg(velUnit));
    }

    if (m_spinJogAcc) {
        m_spinJogAcc->setSuffix(QString(" %1").arg(accUnit));
    }
}

void AxisControlWidget::setDefaultParameters()
{
    // 设置JOG运动模块的默认参数值
    if (m_unitConverter->getCurrentUnitType() == UNIT_PULSE) {
        // pulse单位下的默认值
        if (m_spinJogSpeed) m_spinJogSpeed->setValue(100); // 100 pulse/ms
        if (m_spinJogAcc) m_spinJogAcc->setValue(10);     // 10 pulse/ms^2
    } else {
        // mm单位下的默认值
        if (m_spinJogSpeed) m_spinJogSpeed->setValue(100); // 100 mm/s
        if (m_spinJogAcc) m_spinJogAcc->setValue(10000);  // 10000 mm/s^2
    }
}

void AxisControlWidget::onUnitTypeChanged(UnitType type)
{
    // 当单位类型变化时更新界面显示
    updateUnitDisplay();

    // 转换JOG运动参数的值
    if (type == UNIT_PULSE) {
        // 从 mm 转换到 pulse
        // 速度保持不变，因为1mm/s = 1pulse/ms
        // 加速度需要转换，因为1mm/s^2 = 0.001pulse/ms^2
        if (m_spinJogAcc) m_spinJogAcc->setValue(m_spinJogAcc->value() * 0.001);
    } else {
        // 从 pulse 转换到 mm
        // 速度保持不变，因为1pulse/ms = 1mm/s
        // 加速度需要转换，因为1pulse/ms^2 = 1000mm/s^2
        if (m_spinJogAcc) m_spinJogAcc->setValue(m_spinJogAcc->value() * 1000);
    }
}

void AxisControlWidget::onUnitSwitchClicked()
{
    // 切换单位类型
    UnitType currentType = m_unitConverter->getCurrentUnitType();
    UnitType newType = (currentType == UNIT_PULSE) ? UNIT_MM : UNIT_PULSE;

    // 设置新的单位类型
    m_unitConverter->setCurrentUnitType(newType);
}

void AxisControlWidget::createJogInterface()
{
    // 创建一个Jog控制组
    QGroupBox *groupJog = new QGroupBox("Jog控制", this);

    // 创建网格布局
    QGridLayout *gridLayoutJog = new QGridLayout(groupJog);

    // 添加速度控制
    QLabel *labelSpeed = new QLabel("速度:", groupJog);
    m_spinJogSpeed = new QDoubleSpinBox(groupJog);
    m_spinJogSpeed->setRange(1, 1000);
    m_spinJogSpeed->setValue(100);
    m_spinJogSpeed->setSuffix(QString(" %1").arg(m_unitConverter->getVelocityUnitString()));
    m_spinJogSpeed->setObjectName("spinJogSpeed");
    // 设置固定宽度以保持对齐
    m_spinJogSpeed->setMinimumWidth(120);

    // 添加加速度控制
    QLabel *labelAcc = new QLabel("加速度:", groupJog);
    m_spinJogAcc = new QDoubleSpinBox(groupJog);
    m_spinJogAcc->setRange(10, 10000);
    m_spinJogAcc->setValue(10);
    m_spinJogAcc->setSuffix(QString(" %1").arg(m_unitConverter->getAccelerationUnitString()));
    // 设置固定宽度以保持对齐
    m_spinJogAcc->setMinimumWidth(120);
    m_spinJogAcc->setObjectName("spinJogAcc");

    // 添加操作按钮
    QLabel *labelOperation = new QLabel("操作:", groupJog);
    QHBoxLayout *hboxButtons = new QHBoxLayout();

    m_btnJogNegative = new QPushButton("←", groupJog);
    m_btnJogNegative->setObjectName("btnJogNegative");
    m_btnJogNegative->setMinimumWidth(40);

    m_btnJogPositive = new QPushButton("→", groupJog);
    m_btnJogPositive->setObjectName("btnJogPositive");
    m_btnJogPositive->setMinimumWidth(40);

    hboxButtons->addWidget(m_btnJogNegative);
    hboxButtons->addWidget(m_btnJogPositive);

    // 添加控件到网格布局
    gridLayoutJog->addWidget(labelSpeed, 0, 0);
    gridLayoutJog->addWidget(m_spinJogSpeed, 0, 1);
    gridLayoutJog->addWidget(labelAcc, 1, 0);
    gridLayoutJog->addWidget(m_spinJogAcc, 1, 1);
    gridLayoutJog->addWidget(labelOperation, 2, 0);
    gridLayoutJog->addLayout(hboxButtons, 2, 1);

    // 将Jog控制组添加到主布局中，在轴选择和实时状态之间
    QVBoxLayout *mainLayout = qobject_cast<QVBoxLayout*>(layout());
    if (mainLayout) {
        // 找到"实时状态"组框的索引
        int statusGroupIndex = -1;
        for (int i = 0; i < mainLayout->count(); ++i) {
            QWidget *widget = mainLayout->itemAt(i)->widget();
            if (widget && widget->objectName() == "groupBox_2") {
                statusGroupIndex = i;
                break;
            }
        }

        if (statusGroupIndex != -1) {
            mainLayout->insertWidget(statusGroupIndex, groupJog);
        } else {
            mainLayout->addWidget(groupJog);
        }
    }

    // 连接信号槽
    connect(m_btnJogPositive, &QPushButton::pressed, this, &AxisControlWidget::startJogPositive);
    connect(m_btnJogPositive, &QPushButton::released, this, &AxisControlWidget::stopJog);
    connect(m_btnJogNegative, &QPushButton::pressed, this, &AxisControlWidget::startJogNegative);
    connect(m_btnJogNegative, &QPushButton::released, this, &AxisControlWidget::stopJog);
}
