#ifndef CONNECTIONDIALOG_H
#define CONNECTIONDIALOG_H

#include <QDialog>
#include <QLineEdit>
#include <QSpinBox>
#include <QPushButton>
#include <QLabel>
#include <QGridLayout>
#include <QTimer>
#include "admc_api_wrapper.h"

class ConnectionDialog : public QDialog
{
    Q_OBJECT

public:
    explicit ConnectionDialog(QWidget *parent = nullptr);
    ~ConnectionDialog();

    // 获取连接信息
    QString getIpAddress() const;
    int getPort() const;
    bool isConnected() const;

public slots:
    // 连接断开操作
    void connectDevice();
    void disconnectDevice();
    void resetDevice();

private slots:
    // 状态更新
    void updateConnectionStatus(bool connected);
    void handleApiError(short errorCode, const QString& errorMessage);

private:
    // UI初始化
    void setupUi();

    // 成员控件
    QLineEdit *m_ipEdit;
    QSpinBox *m_portSpin;
    QPushButton *m_connectBtn;
    QPushButton *m_disconnectBtn;
    QPushButton *m_resetBtn;
    QLabel *m_statusLabel;

    // API接口
    AdmcApiWrapper *m_apiWrapper;
    
    // 状态
    bool m_connected;
    QTimer *m_statusTimer;
};

#endif // CONNECTIONDIALOG_H 