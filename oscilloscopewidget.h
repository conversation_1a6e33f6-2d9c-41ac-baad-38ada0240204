#ifndef OSCILLOSCOPEWIDGET_H
#define OSCILLOSCOPEWIDGET_H

#include <QWidget>
#include <QTimer>
#include <QCheckBox>
#include <QVector>
#include <QDateTime>
#include <QLabel>
#include "qcustomplot.h"
#include "admc_api_wrapper.h"
#include "unitconverter.h"

// 通道数量定义
#define POSITION_CHANNEL_COUNT 4
#define VELOCITY_CHANNEL_COUNT 4
#define TOTAL_CHANNEL_COUNT (POSITION_CHANNEL_COUNT + VELOCITY_CHANNEL_COUNT)

class OscilloscopeWidget : public QWidget
{
    Q_OBJECT

public:
    explicit OscilloscopeWidget(QWidget *parent = nullptr);
    ~OscilloscopeWidget();

signals:
    void apiStatusChanged(const QString& message, bool isSuccess);

private slots:
    void updatePlot();
    void onChannelVisibilityChanged(int state);
    void onUnitTypeChanged(UnitType type);
    void onResetButtonClicked();
    void onPauseButtonClicked();
    void onCenterButtonClicked();
    void onClearButtonClicked();
    void onSaveButtonClicked();
    void onImportButtonClicked();
    void onPlotMouseMove(QMouseEvent *event);
    void onPlotMousePress(QMouseEvent *event);
    void onPlotMouseWheel(QWheelEvent *event);
    void onHorizontalMarkerCheckBoxChanged(int state);
    void onVerticalMarkerCheckBoxChanged(int state);

private:
    void setupUI();
    void setupPlot();
    void setupMarkers();
    void fetchAxisPositions();
    void calculateVelocities();
    void clearData();
    void updateMarkerLabels();
    void saveDataToCSV(const QString &filePath);
    void importDataFromCSV(const QString &filePath);
    QVector<double> downsampleData(const QVector<double> &data, int targetSize);
    QVector<QCPGraphData> prepareGraphData(const QVector<double> &keys, const QVector<double> &values, int maxPoints);
    void createFixedLabel(QCPGraph *graph, double key, double value);
    void clearAllFixedLabels();

    // QCustomPlot实例
    QCustomPlot *m_customPlot;

    // 图表对象 - 位置和速度
    QCPGraph *m_graphs[TOTAL_CHANNEL_COUNT];

    // 通道可见性复选框 - 位置和速度
    QCheckBox *m_channelCheckBoxes[TOTAL_CHANNEL_COUNT];

    // 控制按钮
    QPushButton *m_resetButton;
    QPushButton *m_pauseButton;
    QPushButton *m_centerButton;
    QPushButton *m_clearButton;
    QPushButton *m_saveButton;
    QPushButton *m_importButton;

    // 标记线控制
    QCheckBox *m_horizontalMarkerCheckBox;
    QCheckBox *m_verticalMarkerCheckBox;

    // 标记线对象
    QCPItemStraightLine *m_horizontalMarkers[2];
    QCPItemStraightLine *m_verticalMarkers[2];

    // 标记线坐标标签
    QCPItemText *m_horizontalMarkerLabels[2];
    QCPItemText *m_verticalMarkerLabels[2];

    // 标记线差值标签
    QCPItemText *m_deltaTimeLabel;  // 垂直标记线时间差
    QCPItemText *m_deltaValueLabel; // 水平标记线值差

    // 存储垂直标记线的相对位置（0-1范围内）
    double m_verticalMarkerRelativePositions[2];

    // 存储水平标记线的相对位置（0-1范围内）
    double m_horizontalMarkerRelativePositions[2];

    // 鼠标跟踪器
    QCPItemTracer *m_tracer;
    QCPItemText *m_tracerLabel;

    // 更新定时器
    QTimer *m_updateTimer;

    // API包装器
    AdmcApiWrapper *m_apiWrapper;

    // 单位转换器
    UnitConverter *m_unitConverter;

    // 数据存储 - 位置
    QVector<double> m_timestamps[POSITION_CHANNEL_COUNT];
    QVector<double> m_positions[POSITION_CHANNEL_COUNT];

    // 数据存储 - 速度
    QVector<double> m_velocityTimestamps[VELOCITY_CHANNEL_COUNT];
    QVector<double> m_velocities[VELOCITY_CHANNEL_COUNT];

    // 上一次位置 - 用于计算速度
    double m_lastPositions[POSITION_CHANNEL_COUNT];
    double m_lastTimestamps[POSITION_CHANNEL_COUNT];

    // 配置参数
    const int MAX_DATA_POINTS = 1000;  // 每个通道最大数据点数
    const double TIME_WINDOW_MS = 30000.0;  // X轴显示的时间窗口（毫秒）

    // 状态标志
    bool m_isPaused;
    double m_startTime;  // 记录开始时间（毫秒）
    bool m_isDraggingMarker;
    int m_draggedMarkerIndex;
    bool m_isDraggingHorizontal;

    // 固定跟踪器相关
    QList<QCPItemTracer*> m_fixedTracers;
    QList<QCPItemText*> m_fixedLabels;
};

#endif // OSCILLOSCOPEWIDGET_H
