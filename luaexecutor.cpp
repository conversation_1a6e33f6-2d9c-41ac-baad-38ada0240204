#include "luaexecutor.h"
#include <QDebug>
#include <QMutexLocker>

// 包含Lua头文件
extern "C" {
#include "luadll/lua.h"
#include "luadll/lauxlib.h"
#include "luadll/lualib.h"
#include "luadll/luaconf.h"
#include "luadll/lualib.h"
}

// 初始化静态成员
LuaExecutor* LuaExecutor::m_instance = nullptr;

// 获取单例实例
LuaExecutor* LuaExecutor::getInstance()
{
    if (!m_instance) {
        // 注册CoordSystem和State类型，使其可以在信号槽中使用
        qRegisterMetaType<CoordSystem>("CoordSystem");
        qRegisterMetaType<State>("State");

        m_instance = new LuaExecutor();
    }
    return m_instance;
}

// 构造函数
LuaExecutor::LuaExecutor(QObject* parent)
    : QObject(parent),
      m_apiWrapper(AdmcApiWrapper::getInstance())
{
    qDebug() << "LuaExecutor构造函数被调用";
    // 初始化Lua状态
    for (int i = 0; i < 2; ++i) {
        qDebug() << "初始化坐标系" << i+1 << "的Lua状态";
        m_luaState[i] = luaL_newstate();
        luaL_openlibs(m_luaState[i]);
        m_state[i] = State::STOPPED;
        m_executionThread[i] = nullptr;
        m_errorMessage[i] = "";
        m_stopRequested[i] = false;

        // 注册C++函数到Lua
        registerFunctions(static_cast<CoordSystem>(i));
        qDebug() << "坐标系" << i+1 << "的Lua状态初始化完成";
    }
    qDebug() << "LuaExecutor构造函数执行完毕";
}

// 析构函数
LuaExecutor::~LuaExecutor()
{
    // 停止所有执行线程
    stopAllExecution();

    // 关闭Lua状态
    for (int i = 0; i < 2; ++i) {
        if (m_luaState[i]) {
            lua_close(m_luaState[i]);
            m_luaState[i] = nullptr;
        }
    }
}

// 注册C++函数到Lua
void LuaExecutor::registerFunctions(CoordSystem coord)
{
    int coordIdx = static_cast<int>(coord);
    lua_State* L = m_luaState[coordIdx];

    qDebug() << "注册坐标系" << coordIdx+1 << "的C++函数到Lua";

    // 创建坐标系表
    lua_newtable(L);

    // 注册直线插补函数
    lua_pushcfunction(L, lua_linearMove);
    lua_setfield(L, -2, "move_linear");

    // 注册圆弧插补函数
    lua_pushcfunction(L, lua_arcMove);
    lua_setfield(L, -2, "move_arc");

    // 注册轴点位运动函数
    lua_pushcfunction(L, lua_axisTrapMove);
    lua_setfield(L, -2, "move_axis");

    // 将坐标系表设置为全局变量
    QString coordName = QString("coord%1").arg(coordIdx + 1);
    lua_pushvalue(L, -1);  // 复制坐标系表，因为lua_setglobal会弹出栈顶值
    lua_setglobal(L, coordName.toUtf8().constData());

    // 将坐标系表也设置为全局变量coord，方便脚本使用
    lua_setglobal(L, "coord");

    // 创建轴表
    lua_newtable(L);

    // 注册轴点位运动函数
    lua_pushcfunction(L, lua_axisTrapMove);
    lua_setfield(L, -2, "move_trap");

    // 将轴表设置为全局变量axis
    lua_setglobal(L, "axis");

    // 注册延时函数
    lua_pushcfunction(L, lua_delay);
    lua_setglobal(L, "delay");

    // 注册共享变量函数
    lua_pushcfunction(L, lua_setSharedVariable);
    lua_setglobal(L, "set_shared_variable");

    lua_pushcfunction(L, lua_getSharedVariable);
    lua_setglobal(L, "get_shared_variable");

    // 重定义print函数，将输出重定向到Qt信号
    qDebug() << "注册print函数到Lua";
    lua_pushcfunction(L, lua_print);
    lua_setglobal(L, "print");
    qDebug() << "print函数注册完成";

    // 注册等待运动完成函数
    lua_pushcfunction(L, lua_waitMotionComplete);
    lua_setglobal(L, "wait_motion_complete");

    // 注册等待条件函数
    lua_pushcfunction(L, lua_waitCondition);
    lua_setglobal(L, "wait");

    // 注册设置IO输出函数
    lua_pushcfunction(L, lua_setIoOutput);
    lua_setglobal(L, "set_io_output");

    qDebug() << "坐标系" << coordIdx+1 << "的C++函数注册完成";
}

// 加载脚本
bool LuaExecutor::loadScript(CoordSystem coord, const QString& script)
{
    int coordIdx = static_cast<int>(coord);

    // 检查状态
    if (m_state[coordIdx] == State::RUNNING || m_state[coordIdx] == State::PAUSED) {
        m_errorMessage[coordIdx] = "无法加载脚本：脚本正在执行中";
        return false;
    }

    // 加载脚本
    int result = luaL_loadstring(m_luaState[coordIdx], script.toUtf8().constData());
    if (result != LUA_OK) {
        m_errorMessage[coordIdx] = QString("脚本加载错误：%1").arg(lua_tostring(m_luaState[coordIdx], -1));
        lua_pop(m_luaState[coordIdx], 1);
        return false;
    }

    m_state[coordIdx] = State::STOPPED;
    m_errorMessage[coordIdx] = "";
    return true;
}

// 启动脚本执行
bool LuaExecutor::startExecution(CoordSystem coord)
{
    int coordIdx = static_cast<int>(coord);

    // 检查状态
    if (m_state[coordIdx] == State::RUNNING) {
        m_errorMessage[coordIdx] = "脚本已经在执行中";
        return false;
    }

    // 如果之前有执行线程，等待其结束
    if (m_executionThread[coordIdx] && m_executionThread[coordIdx]->isRunning()) {
        m_executionThread[coordIdx]->wait();
        delete m_executionThread[coordIdx];
    }

    // 设置coord全局变量指向当前坐标系表
    lua_State* L = m_luaState[coordIdx];
    QString coordName = QString("coord%1").arg(coordIdx + 1);
    lua_getglobal(L, coordName.toUtf8().constData());  // 获取坐标系表
    lua_setglobal(L, "coord");  // 设置为全局变量coord

    qDebug() << "设置全局变量coord指向坐标系" << (coordIdx + 1);

    // 创建新的执行线程
    m_executionThread[coordIdx] = new QThread();
    connect(m_executionThread[coordIdx], &QThread::started, [this, coord]() {
        executionThreadFunc(coord);
    });

    // 启动线程
    m_state[coordIdx] = State::RUNNING;
    m_executionThread[coordIdx]->start();

    emit stateChanged(coord, State::RUNNING);
    return true;
}

// 启动所有脚本执行
bool LuaExecutor::startAllExecution()
{
    bool result1 = startExecution(CoordSystem::COORD1);
    bool result2 = startExecution(CoordSystem::COORD2);
    return result1 && result2;
}

// 执行线程函数
void LuaExecutor::executionThreadFunc(CoordSystem coord)
{
    int coordIdx = static_cast<int>(coord);
    qDebug() << "开始执行坐标系" << coordIdx+1 << "的脚本";

    try {
        // 重置停止标志
        m_stopRequested[coordIdx] = false;

        // 执行脚本
        lua_State* L = m_luaState[coordIdx];
        if (!L) {
            qDebug() << "错误：Lua状态为空";
            m_errorMessage[coordIdx] = "内部错误：Lua状态为空";
            m_state[coordIdx] = State::ERROR;
            emit errorOccurred(coord, m_errorMessage[coordIdx]);
            emit stateChanged(coord, State::ERROR);
            return;
        }

        // 设置Lua钩子函数，每条指令执行前都会调用
        lua_sethook(L, luaHook, LUA_MASKCOUNT, 1);  // 每执行1条指令调用一次钩子函数

        qDebug() << "调用lua_pcall执行脚本";
        int result = lua_pcall(L, 0, LUA_MULTRET, 0);
        qDebug() << "lua_pcall执行结果:" << result << "(0表示成功)";

        if (result != LUA_OK) {
            // 检查Lua状态是否有效
            if (L) {
                QString errorMsg = QString::fromUtf8(lua_tostring(L, -1));
                qDebug() << "脚本执行结果:" << errorMsg;

                // 检查是否是用户停止请求或错误停止
                if (errorMsg == "USER_STOP_REQUEST") {
                    qDebug() << "用户停止脚本执行";
                    m_state[coordIdx] = State::STOPPED;
                    emit stateChanged(coord, State::STOPPED);
                    lua_pop(L, 1);
                } else if (errorMsg.startsWith("ERROR_STOP: ")) {
                    // 错误停止
                    QString actualErrorMsg = errorMsg.mid(12); // 去掉"ERROR_STOP: "前缀
                    qDebug() << "错误停止脚本执行:" << actualErrorMsg;
                    m_errorMessage[coordIdx] = actualErrorMsg;
                    m_state[coordIdx] = State::ERROR;
                    emit errorOccurred(coord, m_errorMessage[coordIdx]);
                    emit stateChanged(coord, State::ERROR);
                    lua_pop(L, 1);
                } else {
                    // 真正的错误
                    qDebug() << "脚本执行错误:" << errorMsg;

                    // 尝试从错误信息中提取行号
                    // 错误信息格式通常是 "[string "..."]:行号: 错误信息"
                    int lineNumber = -1;
                    int colonPos1 = errorMsg.indexOf("]:");
                    if (colonPos1 != -1) {
                        int colonPos2 = errorMsg.indexOf(":", colonPos1 + 2);
                        if (colonPos2 != -1) {
                            QString lineStr = errorMsg.mid(colonPos1 + 2, colonPos2 - colonPos1 - 2);
                            bool ok;
                            int line = lineStr.toInt(&ok);
                            if (ok) {
                                lineNumber = line;
                            }
                        }
                    }

                    if (lineNumber > 0) {
                        m_errorMessage[coordIdx] = QString("第 %1 行: %2").arg(lineNumber).arg(errorMsg);
                    } else {
                        m_errorMessage[coordIdx] = QString("脚本执行错误：%1").arg(errorMsg);
                    }

                    lua_pop(L, 1);

                    m_state[coordIdx] = State::ERROR;
                    emit errorOccurred(coord, m_errorMessage[coordIdx]);
                    emit stateChanged(coord, State::ERROR);
                }
            } else {
                qDebug() << "脚本执行错误，但Lua状态已无效";
                m_errorMessage[coordIdx] = "脚本执行错误，但无法获取详细信息";
                m_state[coordIdx] = State::ERROR;
                emit errorOccurred(coord, m_errorMessage[coordIdx]);
                emit stateChanged(coord, State::ERROR);
            }
        } else {
            qDebug() << "脚本执行成功";
            m_state[coordIdx] = State::STOPPED;
            emit stateChanged(coord, State::STOPPED);
        }
    } catch (const std::exception& e) {
        qDebug() << "脚本执行过程中发生C++异常:" << e.what();
        m_errorMessage[coordIdx] = QString("脚本执行过程中发生异常：%1").arg(e.what());
        m_state[coordIdx] = State::ERROR;
        emit errorOccurred(coord, m_errorMessage[coordIdx]);
        emit stateChanged(coord, State::ERROR);
    } catch (...) {
        qDebug() << "脚本执行过程中发生未知异常";
        m_errorMessage[coordIdx] = "脚本执行过程中发生未知异常";
        m_state[coordIdx] = State::ERROR;
        emit errorOccurred(coord, m_errorMessage[coordIdx]);
        emit stateChanged(coord, State::ERROR);
    }

    // 清理线程资源
    m_executionThread[coordIdx] = nullptr; // 线程会自动删除自己
    qDebug() << "坐标系" << coordIdx+1 << "的脚本执行完毕";
}

// 停止脚本执行
bool LuaExecutor::stopExecution(CoordSystem coord)
{
    int coordIdx = static_cast<int>(coord);
    qDebug() << "停止坐标系" << coordIdx+1 << "的脚本执行";

    // 检查轴状态，判断是否真的在运动
    bool isAnyAxisMoving = false;
    for (short axis = 0; axis < 4; ++axis) {
        short status = 0;
        short result = m_apiWrapper->getAxisStatus(axis, status);
        if (result == 0) {
            // 检查运动中状态位（MOV，第5位）
            bool isMoving = (status & (1 << 5)) != 0;
            if (isMoving) {
                isAnyAxisMoving = true;
                break;
            }
        }
    }

    // 如果状态不是RUNNING，但有轴在运动，强制设置状态为RUNNING
    if (m_state[coordIdx] != State::RUNNING && isAnyAxisMoving) {
        qDebug() << "轴在运动但状态不是RUNNING，强制设置为RUNNING";
        m_state[coordIdx] = State::RUNNING;
    }

    // 设置停止标志，无论当前状态如何
    m_stopRequested[coordIdx] = true;

    // 如果状态是RUNNING或有轴在运动，则停止执行
    if (m_state[coordIdx] == State::RUNNING || isAnyAxisMoving) {
        qDebug() << "坐标系" << coordIdx+1 << "正在运行或有轴在运动，停止执行";

        // 设置状态为停止
        m_state[coordIdx] = State::STOPPED;

        // 等待一段时间，让钩子函数有机会检测到停止标志
        QThread::msleep(100);

        // 发送状态变化信号
        emit stateChanged(coord, State::STOPPED);

        // 等待执行线程结束
        if (m_executionThread[coordIdx] && m_executionThread[coordIdx]->isRunning()) {
            // 最多等待3秒
            if (!m_executionThread[coordIdx]->wait(3000)) {
                qDebug() << "警告：执行线程未在3秒内结束，将强制终止";
                m_executionThread[coordIdx]->terminate();
                m_executionThread[coordIdx]->wait();
            }
        }

        // 创建新的Lua状态（不关闭旧的，避免访问可能正在使用的状态）
        lua_State* newState = luaL_newstate();
        if (newState) {
            luaL_openlibs(newState);

            // 安全地替换旧的Lua状态
            m_luaState[coordIdx] = newState;

            // 重新注册C++函数到Lua
            registerFunctions(coord);

            qDebug() << "创建了新的Lua状态，替换了旧的状态";
        } else {
            qDebug() << "警告：无法创建新的Lua状态";
        }

        // 重置错误信息
        m_errorMessage[coordIdx] = "";

        return true;
    }

    // 如果状态已经是STOPPED或ERROR，只需要确保UI状态更新
    if (m_state[coordIdx] == State::STOPPED || m_state[coordIdx] == State::ERROR) {
        qDebug() << "坐标系" << coordIdx+1 << "已经停止或出错，更新UI状态";

        // 不再等待线程结束，而是直接重置Lua状态
        // 这样可以避免在等待线程结束时出现问题

        // 创建新的Lua状态（不关闭旧的，避免访问可能正在使用的状态）
        lua_State* newState = luaL_newstate();
        if (newState) {
            luaL_openlibs(newState);

            // 安全地替换旧的Lua状态
            m_luaState[coordIdx] = newState;

            // 重新注册C++函数到Lua
            registerFunctions(coord);

            qDebug() << "创建了新的Lua状态，替换了旧的状态";
        } else {
            qDebug() << "警告：无法创建新的Lua状态";
        }

        // 重置错误信息
        m_errorMessage[coordIdx] = "";

        // 发送状态变化信号，确保UI更新
        emit stateChanged(coord, m_state[coordIdx]);
        return true;
    }

    return false;
}

// 停止所有脚本执行
bool LuaExecutor::stopAllExecution()
{
    qDebug() << "停止所有脚本执行";

    // 检查轴状态，判断是否真的在运动
    bool isAnyAxisMoving = false;
    for (short axis = 0; axis < 4; ++axis) {
        short status = 0;
        short result = m_apiWrapper->getAxisStatus(axis, status);
        if (result == 0) {
            // 检查运动中状态位（MOV，第5位）
            bool isMoving = (status & (1 << 5)) != 0;
            if (isMoving) {
                isAnyAxisMoving = true;
                break;
            }
        }
    }

    // 如果有轴在运动，但状态不是RUNNING，强制设置状态为RUNNING
    if (isAnyAxisMoving) {
        for (int i = 0; i < 2; ++i) {
            if (m_state[i] != State::RUNNING) {
                qDebug() << "轴在运动但坐标系" << i+1 << "状态不是RUNNING，强制设置为RUNNING";
                m_state[i] = State::RUNNING;
            }
        }
    }

    // 设置所有坐标系的停止标志
    for (int i = 0; i < 2; ++i) {
        m_stopRequested[i] = true;
    }

    // 等待一段时间，让钩子函数有机会检测到停止标志
    QThread::msleep(100);

    // 对每个坐标系执行停止操作
    for (int i = 0; i < 2; ++i) {
        try {
            // 设置状态为STOPPED
            m_state[i] = State::STOPPED;

            // 发送状态变化信号
            emit stateChanged(static_cast<CoordSystem>(i), State::STOPPED);

            // 等待执行线程结束
            if (m_executionThread[i] && m_executionThread[i]->isRunning()) {
                // 最多等待3秒
                if (!m_executionThread[i]->wait(3000)) {
                    qDebug() << "警告：坐标系" << i+1 << "的执行线程未在3秒内结束，将强制终止";
                    m_executionThread[i]->terminate();
                    m_executionThread[i]->wait();
                }
            }

            // 创建新的Lua状态（不关闭旧的，避免访问可能正在使用的状态）
            lua_State* newState = luaL_newstate();
            if (newState) {
                luaL_openlibs(newState);

                // 安全地替换旧的Lua状态
                m_luaState[i] = newState;

                // 重新注册C++函数到Lua
                registerFunctions(static_cast<CoordSystem>(i));

                qDebug() << "创建了新的Lua状态，替换了坐标系" << i+1 << "的旧状态";
            } else {
                qDebug() << "警告：无法为坐标系" << i+1 << "创建新的Lua状态";
            }

            // 重置错误信息
            m_errorMessage[i] = "";
        } catch (...) {
            qDebug() << "停止坐标系" << i+1 << "时发生异常";
        }
    }

    qDebug() << "所有脚本执行已停止";
    return true;
}

// 获取当前状态
LuaExecutor::State LuaExecutor::getState(CoordSystem coord) const
{
    int coordIdx = static_cast<int>(coord);
    return m_state[coordIdx];
}

// 获取错误信息
QString LuaExecutor::getErrorMessage(CoordSystem coord) const
{
    int coordIdx = static_cast<int>(coord);
    return m_errorMessage[coordIdx];
}

// 设置共享变量
bool LuaExecutor::setSharedVariable(const QString& name, const QVariant& value)
{
    QMutexLocker locker(&m_sharedVariablesMutex);
    m_sharedVariables[name] = value;
    return true;
}

// 获取共享变量
QVariant LuaExecutor::getSharedVariable(const QString& name)
{
    QMutexLocker locker(&m_sharedVariablesMutex);
    return m_sharedVariables.value(name, QVariant());
}

// 获取所有共享变量
QMap<QString, QVariant> LuaExecutor::getAllSharedVariables()
{
    QMutexLocker locker(&m_sharedVariablesMutex);
    return m_sharedVariables;
}

// 重置Lua状态
void LuaExecutor::resetLuaState(CoordSystem coord)
{
    int coordIdx = static_cast<int>(coord);
    qDebug() << "开始重置坐标系" << (coordIdx + 1) << "的Lua状态";

    try {
        // 不再尝试关闭当前Lua状态，而是直接创建一个新的Lua状态
        // 这样可以避免在关闭Lua状态时出现问题

        // 创建新的Lua状态
        qDebug() << "创建坐标系" << (coordIdx + 1) << "的新Lua状态";
        lua_State* newState = luaL_newstate();
        if (!newState) {
            qDebug() << "错误：无法创建新的Lua状态";
            return;
        }

        luaL_openlibs(newState);

        // 安全地替换旧的Lua状态
        m_luaState[coordIdx] = newState;

        // 重置错误信息
        m_errorMessage[coordIdx] = "";

        // 重新注册C++函数到Lua
        registerFunctions(coord);

        qDebug() << "重置坐标系" << (coordIdx + 1) << "的Lua状态完成";
    } catch (...) {
        qDebug() << "重置Lua状态过程中发生异常";
    }
}

// Lua函数实现
// 直线插补
int LuaExecutor::lua_linearMove(lua_State* L)
{
    // 添加调试输出
    qDebug() << "lua_linearMove函数被调用";

    // 获取参数
    double x = luaL_checknumber(L, 1);
    double y = luaL_checknumber(L, 2);
    double speed = luaL_checknumber(L, 3);
    double acc = luaL_optnumber(L, 4, 10.0);

    // 输出参数信息
    qDebug() << "直线插补参数: x=" << x << "y=" << y << "speed=" << speed << "acc=" << acc;

    // 获取当前Lua状态对应的坐标系ID
    int crd = -1;

    // 遍历所有坐标系，找到当前Lua状态对应的坐标系
    for (int i = 0; i < 2; ++i) {
        if (LuaExecutor::getInstance()->m_luaState[i] == L) {
            crd = i;
            break;
        }
    }

    if (crd == -1) {
        qWarning() << "无法确定当前Lua状态对应的坐标系";
        lua_pushboolean(L, false);
        lua_pushstring(L, "无法确定当前坐标系");
        return 2;
    }

    qDebug() << "当前使用坐标系:" << crd;

    // 在执行运动指令前，自动等待上一段运动完成
    bool autoWait = true;  // 默认启用自动等待

    // 获取API包装器实例
    AdmcApiWrapper* apiWrapper = AdmcApiWrapper::getInstance();

    // 首先等待上一段运动完成
    if (autoWait) {
        qDebug() << "等待上一段运动完成";

        // 计算当前坐标系对应的轴范围
        short startAxis = crd * 2;  // 坐标系0对应轴0和1，坐标系1对应轴2和3
        short endAxis = startAxis + 2;   // 每个坐标系有2个轴

        // 等待所有轴停止运动并到位
        bool allComplete = false;
        int maxAttempts = 1000;  // 最大尝试次数，防止无限循环
        int attempts = 0;

        while (!allComplete && attempts < maxAttempts) {
            // 检查停止请求
            if (LuaExecutor::getInstance()->m_stopRequested[crd]) {
                lua_pushboolean(L, false);
                lua_pushstring(L, "收到停止请求");
                return 2;
            }

            allComplete = true;

            // 检查当前坐标系的所有轴
            for (short axis = startAxis; axis < endAxis; ++axis) {
                short status = 0;
                short result = apiWrapper->getAxisStatus(axis, status);

                if (result != 0) {
                    lua_pushboolean(L, false);
                    lua_pushstring(L, QString("获取轴%1状态失败，错误码:%2").arg(axis).arg(result).toUtf8().constData());
                    return 2;
                }

                // 检查运动中状态位（MOV，第5位）和到位状态位（INP，第4位）
                //bool isMoving = (status & (1 << 5)) != 0;
                bool isInPosition = (status & (1 << 4)) != 0;
                qDebug() << "测试::轴" << axis <<":isInPosition: "<<isInPosition;

                // 只有当轴不在运动中且已到位时，才认为该轴完成
                if (!isInPosition) {
                    allComplete = false;
                    if (attempts % 100 == 0) {  // 每100次循环输出一次日志，避免日志过多
//                        if (isMoving) {
//                            qDebug() << "轴" << axis << "仍在运动中";
//                        } else if (!isInPosition) {
//                            qDebug() << "轴" << axis << "未到位";
//                        }
                        if (!isInPosition) {
                           qDebug() << "轴" << axis << "未到位";
                         }
                    }
                    break;
                }
            }

            if (!allComplete) {
                // 等待一小段时间再检查
                QThread::msleep(1);
                attempts++;
            }
        }

        if (attempts >= maxAttempts) {
            qWarning() << "等待运动完成超时";
            lua_pushboolean(L, false);
            lua_pushstring(L, "等待运动完成超时");
            return 2;
        }

        qDebug() << "上一段运动已完成，开始执行新的运动指令";
    }

    // 设置坐标系参数 - 新接口直接传递参数
    // 注意：新接口不支持轴映射设置，轴映射需要通过其他API设置
    double synVelMax = 3000.0;  // 最大合成速度
    double synAccMax = 30.0;    // 最大合成加速度
    short result = apiWrapper->setCrdPrm(crd, synVelMax, synAccMax);
    if (result != 0) {
        QString errorMsg = QString("设置坐标系参数失败，错误代码: %1").arg(result);
        qDebug() << "Failed to set Crd parameters for crd" << crd << ", error code:" << result;

        // 获取当前Lua调用的堆栈信息，以便获取行号
        lua_Debug ar;
        if (lua_getstack(L, 1, &ar) && lua_getinfo(L, "Sl", &ar)) {
            int currentLine = ar.currentline;
            if (currentLine > 0) {
                errorMsg = QString("第 %1 行: %2").arg(currentLine).arg(errorMsg);
            }
        }

        // 处理错误并停止脚本执行
        handleError(L, errorMsg);
        return 0; // 这行代码实际上不会执行，因为handleError会调用lua_error
    }

    // 再次检查停止请求
    if (LuaExecutor::getInstance()->m_stopRequested[crd]) {
        lua_pushboolean(L, false);
        lua_pushstring(L, "收到停止请求");
        return 2;
    }

    // 添加直线段
    result = apiWrapper->ln(crd, static_cast<int32_t>(x), static_cast<int32_t>(y), speed, acc, 0);
    if (result != 0) {
        QString errorMsg = QString("添加直线段失败，错误代码: %1").arg(result);

        // 获取当前Lua调用的堆栈信息，以便获取行号
        lua_Debug ar;
        if (lua_getstack(L, 1, &ar) && lua_getinfo(L, "Sl", &ar)) {
            int currentLine = ar.currentline;
            if (currentLine > 0) {
                errorMsg = QString("第 %1 行: %2").arg(currentLine).arg(errorMsg);
            }
        }

        // 处理错误并停止脚本执行
        handleError(L, errorMsg);
        return 0; // 这行代码实际上不会执行，因为handleError会调用lua_error
    }

    // 再次检查停止请求
    if (LuaExecutor::getInstance()->m_stopRequested[crd]) {
        lua_pushboolean(L, false);
        lua_pushstring(L, "收到停止请求");
        return 2;
    }

    // 启动插补运动
    result = apiWrapper->crdStart(crd);
    if (result != 0) {
        QString errorMsg = QString("启动插补运动失败，错误代码: %1").arg(result);

        // 获取当前Lua调用的堆栈信息，以便获取行号
        lua_Debug ar;
        if (lua_getstack(L, 1, &ar) && lua_getinfo(L, "Sl", &ar)) {
            int currentLine = ar.currentline;
            if (currentLine > 0) {
                errorMsg = QString("第 %1 行: %2").arg(currentLine).arg(errorMsg);
            }
        }

        // 处理错误并停止脚本执行
        handleError(L, errorMsg);
        return 0; // 这行代码实际上不会执行，因为handleError会调用lua_error
    }

    lua_pushboolean(L, true);
    return 1;
}

// 圆弧插补
int LuaExecutor::lua_arcMove(lua_State* L)
{
    // 添加调试输出
    qDebug() << "lua_arcMove函数被调用";

    // 获取参数
    double x = luaL_checknumber(L, 1);
    double y = luaL_checknumber(L, 2);
    double radius = luaL_checknumber(L, 3);
    short circleDir = static_cast<short>(luaL_checkinteger(L, 4));
    double speed = luaL_checknumber(L, 5);
    double acc = luaL_optnumber(L, 6, 10.0);
    double velEnd = luaL_optnumber(L, 7, 0.0);

    // 输出参数信息
    qDebug() << "圆弧插补参数: x=" << x << "y=" << y << "radius=" << radius
             << "circleDir=" << circleDir << "speed=" << speed << "acc=" << acc << "velEnd=" << velEnd;

    // 获取当前Lua状态对应的坐标系ID
    int crd = -1;

    // 遍历所有坐标系，找到当前Lua状态对应的坐标系
    for (int i = 0; i < 2; ++i) {
        if (LuaExecutor::getInstance()->m_luaState[i] == L) {
            crd = i;
            break;
        }
    }

    if (crd == -1) {
        qWarning() << "无法确定当前Lua状态对应的坐标系";
        lua_pushboolean(L, false);
        lua_pushstring(L, "无法确定当前坐标系");
        return 2;
    }

    qDebug() << "当前使用坐标系:" << crd;

    // 在执行运动指令前，自动等待上一段运动完成
    bool autoWait = true;  // 默认启用自动等待

    // 获取API包装器实例
    AdmcApiWrapper* apiWrapper = AdmcApiWrapper::getInstance();

    // 首先等待上一段运动完成
    if (autoWait) {
        qDebug() << "等待上一段运动完成";

        // 计算当前坐标系对应的轴范围
        short startAxis = crd * 2;  // 坐标系0对应轴0和1，坐标系1对应轴2和3
        short endAxis = startAxis + 2;   // 每个坐标系有2个轴

        // 等待所有轴停止运动并到位
        bool allComplete = false;
        int maxAttempts = 1000;  // 最大尝试次数，防止无限循环
        int attempts = 0;

        while (!allComplete && attempts < maxAttempts) {
            // 检查停止请求
            if (LuaExecutor::getInstance()->m_stopRequested[crd]) {
                lua_pushboolean(L, false);
                lua_pushstring(L, "收到停止请求");
                return 2;
            }

            allComplete = true;

            // 检查当前坐标系的所有轴
            for (short axis = startAxis; axis < endAxis; ++axis) {
                short status = 0;
                short result = apiWrapper->getAxisStatus(axis, status);

                if (result != 0) {
                    lua_pushboolean(L, false);
                    lua_pushstring(L, QString("获取轴%1状态失败，错误码:%2").arg(axis).arg(result).toUtf8().constData());
                    return 2;
                }

                // 检查运动中状态位（MOV，第5位）和到位状态位（INP，第4位）
                bool isMoving = (status & (1 << 5)) != 0;
                bool isInPosition = (status & (1 << 4)) != 0;

                // 只有当轴不在运动中且已到位时，才认为该轴完成
                if (isMoving || !isInPosition) {
                    allComplete = false;
                    if (attempts % 100 == 0) {  // 每100次循环输出一次日志，避免日志过多
                        if (isMoving) {
                            qDebug() << "轴" << axis << "仍在运动中";
                        } else if (!isInPosition) {
                            qDebug() << "轴" << axis << "未到位";
                        }
                    }
                    break;
                }
            }

            if (!allComplete) {
                // 等待一小段时间再检查
                QThread::msleep(10);
                attempts++;
            }
        }

        if (attempts >= maxAttempts) {
            qWarning() << "等待运动完成超时";
            lua_pushboolean(L, false);
            lua_pushstring(L, "等待运动完成超时");
            return 2;
        }

        qDebug() << "上一段运动已完成，开始执行新的运动指令";
    }

    // 添加圆弧段
    short result = apiWrapper->ArcXYR(crd, static_cast<int32_t>(x), static_cast<int32_t>(y),
                                     radius, circleDir, speed, acc, velEnd);
    if (result != 0) {
        lua_pushboolean(L, false);
        QString errorMsg = QString("添加圆弧段失败，错误代码: %1").arg(result);

        // 获取当前Lua调用的堆栈信息，以便获取行号
        lua_Debug ar;
        if (lua_getstack(L, 1, &ar) && lua_getinfo(L, "Sl", &ar)) {
            int currentLine = ar.currentline;
            if (currentLine > 0) {
                errorMsg = QString("第 %1 行: %2").arg(currentLine).arg(errorMsg);
            }
        }

        lua_pushstring(L, errorMsg.toUtf8().constData());
        return 2;
    }

    // 启动插补运动
    result = apiWrapper->crdStart(crd);
    if (result != 0) {
        lua_pushboolean(L, false);
        QString errorMsg = QString("启动插补运动失败，错误代码: %1").arg(result);
        lua_pushstring(L, errorMsg.toUtf8().constData());
        return 2;
    }

    lua_pushboolean(L, true);
    return 1;
}

// 轴点位运动
int LuaExecutor::lua_axisTrapMove(lua_State* L)
{
    // 添加调试输出
    qDebug() << "lua_axisTrapMove函数被调用";

    // 获取参数
    short axis = static_cast<short>(luaL_checkinteger(L, 1));
    double pos = luaL_checknumber(L, 2);
    double speed = luaL_checknumber(L, 3);
    double acc = luaL_optnumber(L, 4, 10.0);
    double accRatio = luaL_optnumber(L, 5, 1.0);

    // 输出参数信息
    qDebug() << "轴点位运动参数: axis=" << axis << "pos=" << pos
             << "speed=" << speed << "acc=" << acc << "accRatio=" << accRatio;

    // 获取当前Lua状态对应的坐标系ID
    int crd = -1;

    // 遍历所有坐标系，找到当前Lua状态对应的坐标系
    for (int i = 0; i < 2; ++i) {
        if (LuaExecutor::getInstance()->m_luaState[i] == L) {
            crd = i;
            break;
        }
    }

    if (crd == -1) {
        qWarning() << "无法确定当前Lua状态对应的坐标系";
        lua_pushboolean(L, false);
        lua_pushstring(L, "无法确定当前坐标系");
        return 2;
    }

    qDebug() << "当前使用坐标系:" << crd;

    // 在执行运动指令前，自动等待上一段运动完成
    bool autoWait = true;  // 默认启用自动等待

    // 获取API包装器实例
    AdmcApiWrapper* apiWrapper = AdmcApiWrapper::getInstance();

    // 首先等待上一段运动完成
    if (autoWait) {
        qDebug() << "等待上一段运动完成";

        // 计算当前坐标系对应的轴范围
        short startAxis = crd * 2;  // 坐标系0对应轴0和1，坐标系1对应轴2和3
        short endAxis = startAxis + 2;   // 每个坐标系有2个轴

        // 等待所有轴停止运动并到位
        bool allComplete = false;
        int maxAttempts = 1000;  // 最大尝试次数，防止无限循环
        int attempts = 0;

        while (!allComplete && attempts < maxAttempts) {
            // 检查停止请求
            if (LuaExecutor::getInstance()->m_stopRequested[crd]) {
                lua_pushboolean(L, false);
                lua_pushstring(L, "收到停止请求");
                return 2;
            }

            allComplete = true;

            // 检查当前坐标系的所有轴
            for (short axis = startAxis; axis < endAxis; ++axis) {
                short status = 0;
                short result = apiWrapper->getAxisStatus(axis, status);

                if (result != 0) {
                    lua_pushboolean(L, false);
                    lua_pushstring(L, QString("获取轴%1状态失败，错误码:%2").arg(axis).arg(result).toUtf8().constData());
                    return 2;
                }

                // 检查运动中状态位（MOV，第5位）和到位状态位（INP，第4位）
                bool isMoving = (status & (1 << 5)) != 0;
                bool isInPosition = (status & (1 << 4)) != 0;

                // 只有当轴不在运动中且已到位时，才认为该轴完成
                if (isMoving || !isInPosition) {
                    allComplete = false;
                    if (attempts % 100 == 0) {  // 每100次循环输出一次日志，避免日志过多
                        if (isMoving) {
                            qDebug() << "轴" << axis << "仍在运动中";
                        } else if (!isInPosition) {
                            qDebug() << "轴" << axis << "未到位";
                        }
                    }
                    break;
                }
            }

            if (!allComplete) {
                // 等待一小段时间再检查
                QThread::msleep(10);
                attempts++;
            }
        }

        if (attempts >= maxAttempts) {
            qWarning() << "等待运动完成超时";
            lua_pushboolean(L, false);
            lua_pushstring(L, "等待运动完成超时");
            return 2;
        }

        qDebug() << "上一段运动已完成，开始执行新的运动指令";
    }

    // 获取当前轴位置作为起始位置
    double currentPos = 0.0;
    if (apiWrapper->isConnected()) {
        short posResult = apiWrapper->getAxisPosition(axis, currentPos);
        if (posResult != 0) {
            qDebug() << "Failed to get axis position, using default start position";
        }
    }

    // 计算增量位置（目标位置 - 当前位置）
    double incrPos = pos - currentPos;

    // 设置点位模式 - 新接口只需要轴号
    short result = apiWrapper->setAxisTrapMode(axis);
    if (result != 0) {
        lua_pushboolean(L, false);
        QString errorMsg = QString("设置点位模式失败，错误代码: %1").arg(result);

        // 获取当前Lua调用的堆栈信息，以便获取行号
        lua_Debug ar;
        if (lua_getstack(L, 1, &ar) && lua_getinfo(L, "Sl", &ar)) {
            int currentLine = ar.currentline;
            if (currentLine > 0) {
                errorMsg = QString("第 %1 行: %2").arg(currentLine).arg(errorMsg);
            }
        }

        lua_pushstring(L, errorMsg.toUtf8().constData());
        return 2;
    }

    // 设置点位参数 - 新接口直接传递参数
    result = apiWrapper->setAxisTrapParameters(axis, incrPos, speed, acc, static_cast<short>(accRatio));
    if (result != 0) {
        lua_pushboolean(L, false);
        QString errorMsg = QString("设置点位参数失败，错误代码: %1").arg(result);

        // 获取当前Lua调用的堆栈信息，以便获取行号
        lua_Debug ar;
        if (lua_getstack(L, 1, &ar) && lua_getinfo(L, "Sl", &ar)) {
            int currentLine = ar.currentline;
            if (currentLine > 0) {
                errorMsg = QString("第 %1 行: %2").arg(currentLine).arg(errorMsg);
            }
        }

        lua_pushstring(L, errorMsg.toUtf8().constData());
        return 2;
    }

    // 更新点位运动 - 新接口使用轴号
    result = apiWrapper->axisTrapUpdate(axis);
    if (result != 0) {
        lua_pushboolean(L, false);
        QString errorMsg = QString("更新点位运动失败，错误代码: %1").arg(result);

        // 获取当前Lua调用的堆栈信息，以便获取行号
        lua_Debug ar;
        if (lua_getstack(L, 1, &ar) && lua_getinfo(L, "Sl", &ar)) {
            int currentLine = ar.currentline;
            if (currentLine > 0) {
                errorMsg = QString("第 %1 行: %2").arg(currentLine).arg(errorMsg);
            }
        }

        lua_pushstring(L, errorMsg.toUtf8().constData());
        return 2;
    }

    lua_pushboolean(L, true);
    return 1;
}

// 延时
int LuaExecutor::lua_delay(lua_State* L)
{
    // 获取参数
    int ms = static_cast<int>(luaL_checkinteger(L, 1));

    // 获取当前坐标系
    int coordIdx = -1;
    for (int i = 0; i < 2; ++i) {
        if (LuaExecutor::getInstance()->m_luaState[i] == L) {
            coordIdx = i;
            break;
        }
    }

    if (coordIdx == -1) {
        lua_pushboolean(L, false);
        lua_pushstring(L, "无法确定当前坐标系");
        return 2;
    }

    // 分段延时，每10毫秒检查一次停止标志
    int remaining = ms;
    while (remaining > 0) {
        // 检查停止标志
        if (LuaExecutor::getInstance()->m_stopRequested[coordIdx]) {
            lua_pushboolean(L, false);
            lua_pushstring(L, "收到停止请求");
            return 2;
        }

        // 延时10毫秒或剩余时间（取较小值）
        int sleepTime = qMin(1, remaining);
        QThread::msleep(sleepTime);
        remaining -= sleepTime;
    }

    lua_pushboolean(L, true);
    return 1;
}

// 设置共享变量
int LuaExecutor::lua_setSharedVariable(lua_State* L)
{
    // 获取参数
    const char* name = luaL_checkstring(L, 1);

    // 根据类型设置值
    QVariant value;
    if (lua_isboolean(L, 2)) {
        value = QVariant(lua_toboolean(L, 2) != 0);
    } else if (lua_isnumber(L, 2)) {
        value = QVariant(lua_tonumber(L, 2));
    } else if (lua_isstring(L, 2)) {
        value = QVariant(QString::fromUtf8(lua_tostring(L, 2)));
    } else {
        lua_pushboolean(L, false);
        lua_pushstring(L, "不支持的变量类型");
        return 2;
    }

    // 设置共享变量
    LuaExecutor::getInstance()->setSharedVariable(QString::fromUtf8(name), value);

    lua_pushboolean(L, true);
    return 1;
}

// 获取共享变量
int LuaExecutor::lua_getSharedVariable(lua_State* L)
{
    // 获取参数
    const char* name = luaL_checkstring(L, 1);

    // 获取共享变量
    QVariant value = LuaExecutor::getInstance()->getSharedVariable(QString::fromUtf8(name));

    // 根据类型返回值
    if (!value.isValid()) {
        lua_pushnil(L);
    } else if (value.type() == QVariant::Bool) {
        lua_pushboolean(L, value.toBool());
    } else if (value.type() == QVariant::Int || value.type() == QVariant::Double) {
        lua_pushnumber(L, value.toDouble());
    } else {
        lua_pushstring(L, value.toString().toUtf8().constData());
    }

    return 1;
}

// Lua钩子函数，用于检查停止标志
void LuaExecutor::luaHook(lua_State* L, lua_Debug* ar)
{
    // 获取当前坐标系
    int coordIdx = -1;
    for (int i = 0; i < 2; ++i) {
        if (LuaExecutor::getInstance()->m_luaState[i] == L) {
            coordIdx = i;
            break;
        }
    }

    if (coordIdx == -1) {
        qWarning() << "无法确定当前Lua状态对应的坐标系";
        return;
    }

    // 获取当前执行的Lua代码行号
    lua_getinfo(L, "l", ar);
    int currentLine = ar->currentline;

    // 检查停止标志
    if (LuaExecutor::getInstance()->m_stopRequested[coordIdx]) {
        qDebug() << "检测到停止请求，强制终止脚本执行，当前行号:" << currentLine;

        // 设置错误信息
        LuaExecutor::getInstance()->m_errorMessage[coordIdx] = "用户停止脚本执行";

        // 设置状态为STOPPED
        LuaExecutor::getInstance()->m_state[coordIdx] = State::STOPPED;

        // 发送状态变化信号
        emit LuaExecutor::getInstance()->stateChanged(static_cast<CoordSystem>(coordIdx), State::STOPPED);

        // 使用特殊的错误消息来标识用户停止
        lua_pushstring(L, "USER_STOP_REQUEST");
        lua_error(L);
    }
}

// 处理错误并停止脚本执行
void LuaExecutor::handleError(lua_State* L, const QString& errorMsg)
{
    // 获取当前坐标系
    int coordIdx = -1;
    for (int i = 0; i < 2; ++i) {
        if (LuaExecutor::getInstance()->m_luaState[i] == L) {
            coordIdx = i;
            break;
        }
    }

    if (coordIdx == -1) {
        qWarning() << "无法确定当前Lua状态对应的坐标系";
        return;
    }

    // 设置错误信息
    LuaExecutor::getInstance()->m_errorMessage[coordIdx] = errorMsg;

    // 设置状态为ERROR
    LuaExecutor::getInstance()->m_state[coordIdx] = State::ERROR;

    // 设置停止标志
    LuaExecutor::getInstance()->m_stopRequested[coordIdx] = true;

    // 发送错误信号
    emit LuaExecutor::getInstance()->errorOccurred(static_cast<CoordSystem>(coordIdx), errorMsg);

    // 发送状态变化信号
    emit LuaExecutor::getInstance()->stateChanged(static_cast<CoordSystem>(coordIdx), State::ERROR);

    // 使用特殊的错误消息前缀来标识错误停止
    lua_pushstring(L, QString("ERROR_STOP: %1").arg(errorMsg).toUtf8().constData());
    lua_error(L);
}

// 打印输出
int LuaExecutor::lua_print(lua_State* L)
{
    qDebug() << "lua_print函数被调用";
    int n = lua_gettop(L);  // 参数数量
    qDebug() << "参数数量:" << n;
    QString output;

    for (int i = 1; i <= n; i++) {
        if (i > 1) output += "\t";

        if (lua_isstring(L, i)) {
            QString param = QString::fromUtf8(lua_tostring(L, i));
            qDebug() << "参数" << i << "是字符串:" << param;
            output += param;
        } else if (lua_isnil(L, i)) {
            qDebug() << "参数" << i << "是nil";
            output += "nil";
        } else if (lua_isboolean(L, i)) {
            bool value = lua_toboolean(L, i);
            qDebug() << "参数" << i << "是布尔值:" << value;
            output += value ? "true" : "false";
        } else {
            QString type = QString::fromUtf8(lua_typename(L, lua_type(L, i)));
            qDebug() << "参数" << i << "是其他类型:" << type;
            output += type;
        }
    }

    // 获取当前坐标系
    int coordIdx = -1;
    for (int i = 0; i < 2; ++i) {
        if (LuaExecutor::getInstance()->m_luaState[i] == L) {
            coordIdx = i;
            break;
        }
    }

    qDebug() << "当前坐标系索引:" << coordIdx;

    if (coordIdx != -1) {
        // 发送输出信号
        qDebug() << "发送输出信号，坐标系:" << coordIdx << "输出:" << output;
        LuaExecutor::getInstance()->outputProduced(static_cast<CoordSystem>(coordIdx), output);
    } else {
        // 如果无法确定坐标系，默认使用坐标系1
        qDebug() << "无法确定坐标系，使用坐标系1，输出:" << output;
        LuaExecutor::getInstance()->outputProduced(CoordSystem::COORD1, output);
    }

    return 0;
}

// 等待运动完成
int LuaExecutor::lua_waitMotionComplete(lua_State* L)
{
    // 获取当前坐标系
    int coordIdx = -1;
    for (int i = 0; i < 2; ++i) {
        if (LuaExecutor::getInstance()->m_luaState[i] == L) {
            coordIdx = i;
            break;
        }
    }

    if (coordIdx == -1) {
        lua_pushboolean(L, false);
        lua_pushstring(L, "无法确定当前坐标系");
        return 2;
    }

    // 获取API包装器
    AdmcApiWrapper* apiWrapper = LuaExecutor::getInstance()->m_apiWrapper;
    if (!apiWrapper || !apiWrapper->isConnected()) {
        lua_pushboolean(L, false);
        lua_pushstring(L, "设备未连接");
        return 2;
    }

    // 计算当前坐标系对应的轴范围
    short startAxis = coordIdx * 2;  // 坐标系0对应轴0和1，坐标系1对应轴2和3
    short endAxis = startAxis + 2;   // 每个坐标系有2个轴

    // 输出调试信息
    qDebug() << "等待坐标系" << coordIdx << "的轴" << startAxis << "到" << (endAxis - 1) << "运动完成";

    // 等待所有轴停止运动并到位
    bool allComplete = false;
    int maxAttempts = 5000;  // 最大尝试次数，防止无限循环
    int attempts = 0;

    while (!allComplete && attempts < maxAttempts) {
        // 检查停止请求
        if (LuaExecutor::getInstance()->m_stopRequested[coordIdx]) {
            lua_pushboolean(L, false);
            lua_pushstring(L, "收到停止请求");
            return 2;
        }

        allComplete = true;

        // 检查当前坐标系的所有轴
        for (short axis = startAxis; axis < endAxis; ++axis) {
            short status = 0;
            short result = apiWrapper->getAxisStatus(axis, status);

            if (result != 0) {
                lua_pushboolean(L, false);
                lua_pushstring(L, QString("获取轴%1状态失败，错误码:%2").arg(axis).arg(result).toUtf8().constData());
                return 2;
            }

            // 检查运动中状态位（MOV，第5位）和到位状态位（INP，第4位）
            //bool isMoving = (status & (1 << 5)) != 0;
            bool isInPosition = (status & (1 << 4)) != 0;
            // 只有当轴不在运动中且已到位时，才认为该轴完成
            //if (isMoving || !isInPosition) {
            if (!isInPosition) {
                allComplete = false;
                if (attempts % 100 == 0) {  // 每100次循环输出一次日志，避免日志过多
//                    if (isMoving) {
//                        qDebug() << "轴" << axis << "仍在运动中";
//                    } else if (!isInPosition) {
//                        qDebug() << "轴" << axis << "未到位";
//                    }
                   if (!isInPosition) {
                        qDebug() << "轴" << axis << "未到位";
                    }
                }
                break;
            }
        }

        if (!allComplete) {
            // 等待一小段时间再检查
            QThread::msleep(2);
            attempts++;
        }
    }

    if (attempts >= maxAttempts) {
        qWarning() << "等待运动完成超时";
        lua_pushboolean(L, false);
        lua_pushstring(L, "等待运动完成超时");
        return 2;
    }

    qDebug() << "所有轴运动完成并到位";
    lua_pushboolean(L, true);
    return 1;
}

// 设置IO输出
int LuaExecutor::lua_setIoOutput(lua_State* L)
{
    // 获取参数
    int ioNumber = static_cast<int>(luaL_checkinteger(L, 1));
    const char* ioValueStr = luaL_checkstring(L, 2);
    QString ioValueQStr = QString::fromUtf8(ioValueStr);
    bool ioValue = (ioValueQStr.toLower() == "true");

    // 获取API包装器
    AdmcApiWrapper* apiWrapper = LuaExecutor::getInstance()->m_apiWrapper;
    if (!apiWrapper || !apiWrapper->isConnected()) {
        lua_pushboolean(L, false);
        lua_pushstring(L, "设备未连接");
        return 2;
    }

    qDebug() << "设置IO" << ioNumber << "输出值为" << (ioValue ? "true" : "false");

    // 设置IO输出
    // 创建一个整数数组，用于存储IO状态
    int deviceOutput[32] = {0}; // 假设最多有32个IO

    // 设置指定IO的状态
    if (ioValue) {
        deviceOutput[ioNumber] = 1; // 设置为高电平
    } else {
        deviceOutput[ioNumber] = 0; // 设置为低电平
    }

    // 调用API设置IO输出
    short result = apiWrapper->setDeviceOutput(deviceOutput);

    if (result != 0) {
        lua_pushboolean(L, false);
        QString errorMsg = QString("设置IO输出失败，错误代码: %1").arg(result);

        // 获取当前Lua调用的堆栈信息，以便获取行号
        lua_Debug ar;
        if (lua_getstack(L, 1, &ar) && lua_getinfo(L, "Sl", &ar)) {
            int currentLine = ar.currentline;
            if (currentLine > 0) {
                errorMsg = QString("第 %1 行: %2").arg(currentLine).arg(errorMsg);
            }
        }

        lua_pushstring(L, errorMsg.toUtf8().constData());
        return 2;
    }

    lua_pushboolean(L, true);
    return 1;
}

// 等待条件满足
int LuaExecutor::lua_waitCondition(lua_State* L)
{
    // 获取参数
    const char* type = luaL_checkstring(L, 1);
    QString typeStr = QString::fromUtf8(type);

    // 获取当前坐标系
    int coordIdx = -1;
    for (int i = 0; i < 2; ++i) {
        if (LuaExecutor::getInstance()->m_luaState[i] == L) {
            coordIdx = i;
            break;
        }
    }

    if (coordIdx == -1) {
        lua_pushboolean(L, false);
        lua_pushstring(L, "无法确定当前坐标系");
        return 2;
    }

    // 获取API包装器
    AdmcApiWrapper* apiWrapper = LuaExecutor::getInstance()->m_apiWrapper;
    if (!apiWrapper || !apiWrapper->isConnected()) {
        lua_pushboolean(L, false);
        lua_pushstring(L, "设备未连接");
        return 2;
    }

    qDebug() << "等待条件满足，类型:" << typeStr;

    // 等待IO输入
    if (typeStr.toLower() == "input") {
        int ioNumber = static_cast<int>(luaL_checkinteger(L, 2));
        const char* expectedValueStr = luaL_checkstring(L, 3);
        QString expectedValueQStr = QString::fromUtf8(expectedValueStr);
        bool expectedValue = (expectedValueQStr.toLower() == "true");

        qDebug() << "等待IO" << ioNumber << "变为" << (expectedValue ? "true" : "false");

        // 循环检查IO状态
        int maxAttempts = 5000;  // 最大尝试次数，防止无限循环
        int attempts = 0;

        while (attempts < maxAttempts) {
            // 检查停止请求
            if (LuaExecutor::getInstance()->m_stopRequested[coordIdx]) {
                lua_pushboolean(L, false);
                lua_pushstring(L, "收到停止请求");
                return 2;
            }

            // 读取IO状态
            int32_t deviceInput = 0;
            short result = apiWrapper->getDeviceInput(&deviceInput);

            if (result != 0) {
                lua_pushboolean(L, false);
                lua_pushstring(L, QString("读取IO状态失败，错误码:%1").arg(result).toUtf8().constData());
                return 2;
            }

            // 检查指定IO的状态是否符合预期
            // 假设deviceInput是一个位图，每一位代表一个IO的状态
            bool ioState = ((deviceInput >> ioNumber) & 1) != 0;
            bool expectedState = (expectedValue != 0);

            if (ioState == expectedState) {
                qDebug() << "IO" << ioNumber << "状态已变为" << (ioState ? "高" : "低");
                lua_pushboolean(L, true);
                return 1;
            }

            // 等待一小段时间再检查
            QThread::msleep(5);
            attempts++;
        }

        // 超时
        qWarning() << "等待IO" << ioNumber << "状态变化超时";
        lua_pushboolean(L, false);
        lua_pushstring(L, "等待IO状态变化超时");
        return 2;
    }
    // 等待共享变量
    else if (typeStr.toLower() == "var") {
        const char* varName = luaL_checkstring(L, 2);
        const char* op = luaL_checkstring(L, 3);
        QString opStr = QString::fromUtf8(op);
        QString varNameStr = QString::fromUtf8(varName);

        // 获取期望值
        QVariant expectedValue;
        if (lua_isboolean(L, 4)) {
            expectedValue = QVariant(lua_toboolean(L, 4) != 0);
        } else if (lua_isnumber(L, 4)) {
            expectedValue = QVariant(lua_tonumber(L, 4));
        } else if (lua_isstring(L, 4)) {
            expectedValue = QVariant(QString::fromUtf8(lua_tostring(L, 4)));
        } else {
            lua_pushboolean(L, false);
            lua_pushstring(L, "不支持的变量类型");
            return 2;
        }

        qDebug() << "等待变量" << varNameStr << opStr << expectedValue.toString();

        // 循环检查变量值
        int maxAttempts = 1000;  // 最大尝试次数，防止无限循环
        int attempts = 0;

        while (attempts < maxAttempts) {
            // 检查停止请求
            if (LuaExecutor::getInstance()->m_stopRequested[coordIdx]) {
                lua_pushboolean(L, false);
                lua_pushstring(L, "收到停止请求");
                return 2;
            }

            // 获取变量当前值
            QVariant currentValue = LuaExecutor::getInstance()->getSharedVariable(varNameStr);

            // 检查变量值是否符合预期
            bool conditionMet = false;

            if (opStr == "==") {
                conditionMet = (currentValue == expectedValue);
            } else if (opStr == "!=") {
                conditionMet = (currentValue != expectedValue);
            } else if (opStr == ">") {
                conditionMet = (currentValue.toDouble() > expectedValue.toDouble());
            } else if (opStr == "<") {
                conditionMet = (currentValue.toDouble() < expectedValue.toDouble());
            } else if (opStr == ">=") {
                conditionMet = (currentValue.toDouble() >= expectedValue.toDouble());
            } else if (opStr == "<=") {
                conditionMet = (currentValue.toDouble() <= expectedValue.toDouble());
            } else {
                lua_pushboolean(L, false);
                lua_pushstring(L, QString("不支持的操作符: %1").arg(opStr).toUtf8().constData());
                return 2;
            }

            if (conditionMet) {
                qDebug() << "变量" << varNameStr << "条件已满足";
                lua_pushboolean(L, true);
                return 1;
            }

            // 等待一小段时间再检查
            QThread::msleep(10);
            attempts++;
        }

        // 超时
        qWarning() << "等待变量" << varNameStr << "值变化超时";
        lua_pushboolean(L, false);
        lua_pushstring(L, "等待变量值变化超时");
        return 2;
    }
    else {
        lua_pushboolean(L, false);
        lua_pushstring(L, QString("不支持的等待类型: %1").arg(typeStr).toUtf8().constData());
        return 2;
    }
}
