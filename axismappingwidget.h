#ifndef AXISMAPPINGWIDGET_H
#define AXISMAPPINGWIDGET_H

#include <QWidget>
#include <QSpinBox>
#include <QPushButton>
#include <QLabel>
#include <QGridLayout>
#include <QGroupBox>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QMessageBox>
#include "admc_api_wrapper.h"

class AxisMappingWidget : public QWidget
{
    Q_OBJECT

public:
    explicit AxisMappingWidget(QWidget *parent = nullptr);
    ~AxisMappingWidget();

signals:
    void apiStatusChanged(const QString& message, bool isSuccess);

private slots:
    void onSetButtonClicked();

private:
    // 轴映射输入框
    QLabel* m_labelX1;
    QSpinBox* m_spinX1;
    
    QLabel* m_labelY1;
    QSpinBox* m_spinY1;
    
    QLabel* m_labelX2;
    QSpinBox* m_spinX2;
    
    QLabel* m_labelY2;
    QSpinBox* m_spinY2;

    // 设置按钮
    QPushButton* m_btnSet;

    // 初始化UI
    void setupUI();
};

#endif // AXISMAPPINGWIDGET_H
