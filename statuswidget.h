#ifndef STATUSWIDGET_H
#define STATUSWIDGET_H

#include <QWidget>
#include <QTimer>
#include <QMap>
#include "admc_api_wrapper.h"
#include "unitconverter.h"

namespace Ui {
class StatusWidget;
}

class StatusWidget : public QWidget
{
    Q_OBJECT

public:
    explicit StatusWidget(QWidget *parent = nullptr);
    ~StatusWidget();

    // 获取单例实例
    static StatusWidget* getInstance();

    // 获取轴状态
    short getAxisStatus(short axis, short& status) const;

    // 检查轴是否有报警
    bool hasAxisAlarm(short axis) const;

    // 获取轴位置
    double getAxisPosition(short axis) const;

    // 获取轴使能状态
    bool isAxisEnabled(short axis) const;

    // 检查轴是否在原点
    bool isAxisAtOrigin(short axis) const;

    // 检查轴是否在复位中
    bool isAxisResetting(short axis) const;

signals:
    void axisAlarmStatusChanged(short axis, bool hasAlarm);
    void axisStatusChanged(short axis, short status);
    void axisPositionChanged(short axis, double position);

public slots:
    void refreshStatus();

private slots:
    void clearStatus();
    void exportStatus();
    void updateDisplayMode();
    void onUnitTypeChanged(UnitType type);

private:
    Ui::StatusWidget *ui;
    QTimer *m_refreshTimer;
    AdmcApiWrapper* m_apiWrapper;
    UnitConverter* m_unitConverter; // 单位转换器

    // 存储轴状态数据
    QMap<short, short> m_axisStatus;      // 轴号 -> 状态
    QMap<short, bool> m_axisAlarmStatus;  // 轴号 -> 报警状态
    QMap<short, double> m_axisPosition;   // 轴号 -> 位置

    // 单例实例
    static StatusWidget* m_instance;

    // 更新表格数据
    void updateStatusTable();

    // 解析轴状态位
    QString getStatusBitDescription(short bit);
    QColor getStatusBitColor(bool isSet);
};

#endif // STATUSWIDGET_H