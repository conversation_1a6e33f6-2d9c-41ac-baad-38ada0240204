#include "connectiondialog.h"
#include <QMessageBox>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QGroupBox>
#include <QRegExpValidator>
#include <QRegExp>

ConnectionDialog::ConnectionDialog(QWidget *parent)
    : QDialog(parent),
      m_connected(false),
      m_statusTimer(new QTimer(this))
{
    setWindowTitle("设备连接");
    setMinimumWidth(350);
    
    // 获取API接口实例
    m_apiWrapper = AdmcApiWrapper::getInstance();
    
    // 初始化UI
    setupUi();
    
    // 连接信号槽
    connect(m_connectBtn, &QPushButton::clicked, this, &ConnectionDialog::connectDevice);
    connect(m_disconnectBtn, &QPushButton::clicked, this, &ConnectionDialog::disconnectDevice);
    connect(m_resetBtn, &QPushButton::clicked, this, &ConnectionDialog::resetDevice);
    
    // 连接API接口信号
    connect(m_apiWrapper, &AdmcApiWrapper::connectionStatusChanged, 
            this, &ConnectionDialog::updateConnectionStatus);
    connect(m_apiWrapper, &AdmcApiWrapper::errorOccurred,
            this, &ConnectionDialog::handleApiError);
    
    // 初始化连接状态
    m_connected = m_apiWrapper->isConnected();
    updateConnectionStatus(m_connected);
}

ConnectionDialog::~ConnectionDialog()
{
    // API接口由单例实现，无需删除
    if (m_statusTimer) {
        m_statusTimer->stop();
    }
}

QString ConnectionDialog::getIpAddress() const
{
    return m_ipEdit->text();
}

int ConnectionDialog::getPort() const
{
    return m_portSpin->value();
}

bool ConnectionDialog::isConnected() const
{
    return m_connected;
}

void ConnectionDialog::setupUi()
{
    // 创建主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    
    // 创建连接参数组
    QGroupBox *paramsGroup = new QGroupBox("连接参数");
    QFormLayout *paramsLayout = new QFormLayout(paramsGroup);
    
    // IP地址输入
    m_ipEdit = new QLineEdit("***********");
    // IP地址验证器
    QRegExpValidator *ipValidator = new QRegExpValidator(
        QRegExp("^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$"), 
        this);
    m_ipEdit->setValidator(ipValidator);
    paramsLayout->addRow("IP地址:", m_ipEdit);
    
    // 端口号输入
    m_portSpin = new QSpinBox();
    m_portSpin->setRange(1, 65535);
    m_portSpin->setValue(6666);
    paramsLayout->addRow("端口号:", m_portSpin);
    
    // 添加参数组到主布局
    mainLayout->addWidget(paramsGroup);
    
    // 创建按钮组
    QGroupBox *btnGroup = new QGroupBox("操作");
    QHBoxLayout *btnLayout = new QHBoxLayout(btnGroup);
    
    m_connectBtn = new QPushButton("连接");
    m_disconnectBtn = new QPushButton("断开");
    m_resetBtn = new QPushButton("复位");
    
    // 初始状态
    m_disconnectBtn->setEnabled(false);
    m_resetBtn->setEnabled(false);
    
    btnLayout->addWidget(m_connectBtn);
    btnLayout->addWidget(m_disconnectBtn);
    btnLayout->addWidget(m_resetBtn);
    
    // 添加按钮组到主布局
    mainLayout->addWidget(btnGroup);
    
    // 创建状态显示
    m_statusLabel = new QLabel("未连接");
    m_statusLabel->setAlignment(Qt::AlignCenter);
    m_statusLabel->setStyleSheet("font-weight: bold; color: red;");
    
    // 添加状态标签到主布局
    mainLayout->addWidget(m_statusLabel);
    
    // 设置对话框大小策略
    setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
}

void ConnectionDialog::connectDevice()
{
    if (m_connected) {
        return;
    }
    
    // 获取连接参数
    QString ip = m_ipEdit->text();
    int port = m_portSpin->value();
    
    // 使用API接口连接设备
    short result = m_apiWrapper->openBoard(ip, port);
    
    if (result == 0) {
        accept(); // 连接成功，关闭对话框
    }
    // 失败情况会通过API信号处理
}

void ConnectionDialog::disconnectDevice()
{
    if (!m_connected) {
        return;
    }
    
    // 使用API接口断开设备
    short result = m_apiWrapper->closeBoard();
    
    if (result == 0) {
        updateConnectionStatus(false);
    }
    // 失败情况会通过API信号处理
}

void ConnectionDialog::resetDevice()
{
    if (!m_connected) {
        QMessageBox::warning(this, "警告", "设备未连接，无法复位!");
        return;
    }
    
    // 使用API接口复位设备
    short result = m_apiWrapper->resetBoard();
    
    if (result == 0) {
        QMessageBox::information(this, "复位完成", "设备已复位完成");
    }
    // 失败情况会通过API信号处理
}

void ConnectionDialog::updateConnectionStatus(bool connected)
{
    m_connected = connected;
    
    // 更新按钮状态
    m_connectBtn->setEnabled(!m_connected);
    m_disconnectBtn->setEnabled(m_connected);
    m_resetBtn->setEnabled(m_connected);
    
    // 更新状态文本和样式
    if (m_connected) {
        m_statusLabel->setText("已连接");
        m_statusLabel->setStyleSheet("font-weight: bold; color: green;");
    } else {
        m_statusLabel->setText("未连接");
        m_statusLabel->setStyleSheet("font-weight: bold; color: red;");
    }
}

void ConnectionDialog::handleApiError(short errorCode, const QString& errorMessage)
{
    QMessageBox::critical(this, "API错误", QString("错误代码: %1\n%2").arg(errorCode).arg(errorMessage));
} 