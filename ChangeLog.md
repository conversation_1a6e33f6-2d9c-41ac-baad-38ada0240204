# 安达运动控制系统 - 变更日志

## 版本 1.0.2 (2023-09-15)

### 功能更新

#### 1. 运动控制库集成
- 集成ADMC_PCI运动控制库，实现底层硬件控制
- 实现了以下API与界面的集成：
  - 设备连接接口：`API_OpenBoard`/`API_CloseBoard`/`API_ResetBoard`
  - 轴控制接口：`API_AxisOn`/`API_AxisOff`
  - Jog控制接口：`API_SetJogMode`/`API_SetJogPrm`/`API_New_JogUpdate`
  - 点位运动接口：`API_SetCrdTrapMode`/`API_SetCrdTrapPrm`/`API_CrdTrapUpdate`
  - 轴状态获取：`API_GetAixsPos`/`API_GetAxisStatus`

#### 2. 数据结构适配
- 采用运动控制卡标准参数结构：
  - `TJogPrm`结构用于Jog参数设置
  - `TTrapPrm`结构用于点位运动参数设置
  - 适配了轴索引、坐标系参数设置

#### 3. 错误处理机制
- 添加API错误码处理和显示
- 运动控制API调用结果验证和异常处理

### 技术实现

#### 文件修改
1. **ConnectionDialog类修改**
   - 集成`API_OpenBoard`/`API_CloseBoard`/`API_ResetBoard`函数
   - 添加连接状态错误处理

2. **MainWindow类修改**
   - 轴使能/禁用功能与`API_AxisOn`/`API_AxisOff`函数绑定
   - 错误码显示和处理

3. **AxisControlWidget类修改**
   - Jog控制与`API_SetJogMode`/`API_SetJogPrm`/`API_New_JogUpdate`集成
   - 点位运动控制与相关API集成

4. **项目文件修改**
   - 添加ADMC_PCI库的引用和链接
   - 包含运动控制库头文件

#### 相关API函数接口
- 设备连接：`API_OpenBoard(const char* ip, int port)`
- 设备关闭：`API_CloseBoard()`
- 设备复位：`API_ResetBoard()`
- 轴使能：`API_AxisOn(short axis)`
- 轴禁用：`API_AxisOff(short axis)`
- Jog控制：`API_New_JogUpdate(short axis, short dir)`

## 版本 1.0.1 (2023-09-12)

### 新增功能

#### 1. 轴Jog控制功能
- 在轴控制界面中添加了Jog子界面，位于回零和点动功能之间
- 实现了以下Jog控制元素：
  - 轴选择下拉框（0-4轴索引选择）
  - 速度设置（1-1000 mm/s范围）
  - 加速度设置（10-10000 mm/s²范围）
  - 左右箭头控制按钮
    - 左箭头按下时轴向负方向Jog（值为-1）
    - 右箭头按下时轴向正方向Jog（值为1）
  - 获取参数按钮，用于从控制器获取当前Jog参数
- 实现了Jog点动控制逻辑：
  - 按下按钮开始Jog移动，松开按钮停止
  - 实时显示当前位置和速度
  - 支持速度和加速度实时调整

#### 2. 设备列表右键菜单功能增强
- 轴设备右键菜单改进
  - 添加"使能开"和"使能关"两个功能
  - 根据轴的当前状态更新图标显示
  - 只有在控制器连接状态下才能操作轴
- 控制器右键菜单改进
  - 添加"连接"选项，点击后弹出连接对话框
  - 保留原有的"断开"和"刷新"功能

#### 3. 控制器连接对话框
- 创建了专用的控制器连接子界面
- 实现了以下功能：
  - IP地址输入框（带格式验证）
  - 端口号设置（范围1-65535）
  - 连接按钮 - 建立与控制器的通信
  - 断开按钮 - 断开与控制器的连接
  - 复位按钮 - 复位控制器状态
  - 连接状态显示（已连接/未连接）
- 根据连接状态自动调整界面元素可用性

### 技术实现

#### 文件修改
1. **修改现有文件**
   - `mainwindow.cpp` - 添加右键菜单功能，集成连接对话框
   - `axiscontrolwidget.h` - 添加Jog相关函数和成员变量
   - `axiscontrolwidget.cpp` - 实现Jog控制功能和UI生成
   - `MotionControlApp.pro` - 添加新类到项目文件

2. **新增文件**
   - `connectiondialog.h` - 定义连接对话框类
   - `connectiondialog.cpp` - 实现连接对话框功能
   - `jog_ui_changes.txt` - 记录UI修改指南（可选使用）

#### 代码设计
1. **Jog控制实现方式**
   - 方案一：通过UI文件定义界面（需要Qt Designer修改）
   - 方案二：通过代码动态创建界面（已实现）
   - 主要类：`AxisControlWidget`
   - 主要方法：`createJogInterface()`、`startJogPositive()`、`startJogNegative()`等

2. **设备连接实现**
   - 独立对话框类：`ConnectionDialog`
   - 通过lambda表达式连接控制器右键菜单事件
   - 使用信号槽机制更新主窗口状态

### 使用说明

#### Jog控制使用方法
1. 从"轴选择"下拉框中选择目标轴（0-4）
2. 设置期望的Jog速度和加速度
3. 按住左箭头按钮进行负向Jog移动，按住右箭头进行正向Jog移动
4. 松开按钮自动停止Jog移动
5. 可以通过"获取参数"按钮从控制器读取当前参数设置

#### 轴使能控制
1. 在设备列表中找到目标轴
2. 右键点击轴项目
3. 从弹出菜单中选择"使能开"启用轴，或"使能关"禁用轴

#### 控制器连接
1. 右键点击设备列表中的"运动控制器"项
2. 选择"连接"选项，弹出连接对话框
3. 输入控制器的IP地址和端口号
4. 点击"连接"按钮建立连接
5. 连接成功后可以通过"断开"按钮断开连接，或"复位"按钮重置控制器

### 后续改进计划
- 轴参数配置功能
- 多轴联动控制
- 控制器固件更新功能
- 运动轨迹规划与执行
- 系统诊断与错误恢复 