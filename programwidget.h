#ifndef PROGRAMWIDGET_H
#define PROGRAMWIDGET_H

#include <QWidget>
#include <QTabWidget>
#include <QPlainTextEdit>
#include <QTextEdit>
#include <QComboBox>
#include <QTableWidget>
#include <QPushButton>
#include <QLabel>
#include <QFileDialog>
#include <QMessageBox>
#include <QTimer>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QFormLayout>
#include <QLineEdit>
#include <QHeaderView>
#include <QTableWidgetItem>
#include <QStringList>
#include <QMap>
#include <QVariant>
#include <QDebug>

#include "luaexecutor.h" // Include LuaExecutor header
#include "luaprogramwidget.h" // Include LuaSyntaxHighlighter header
#include "unitconverter.h" // Keep UnitConverter if still needed

namespace Ui {
class ProgramWidget;
}

class LuaSyntaxHighlighter; // Forward declaration

class ProgramWidget : public QWidget
{
    Q_OBJECT

public:
    explicit ProgramWidget(QWidget *parent = nullptr);
    ~ProgramWidget();

signals:
    void apiStatusChanged(const QString& message, bool isSuccess);

private slots:
    // File operations
    void onLoadScriptCoord1Clicked();
    void onSaveScriptCoord1Clicked();
    void onLoadScriptCoord2Clicked();
    void onSaveScriptCoord2Clicked();

    // Execution control
    void onStartScriptCoord1Clicked();
    void onStopScriptCoord1Clicked();
    void onStartScriptCoord2Clicked();
    void onStopScriptCoord2Clicked();
    void onStartAllScriptsClicked();
    void onStopAllScriptsClicked();

    // LuaExecutor callbacks
    void onLuaOutputProduced(LuaExecutor::CoordSystem coord, const QString& output);
    void onLuaErrorOccurred(LuaExecutor::CoordSystem coord, const QString& errorMessage);
    void onLuaStateChanged(LuaExecutor::CoordSystem coord, LuaExecutor::State state);

    // Command/Code snippet insertion
    void onCommandSelected(int index);
    void onInsertCommandClicked();

    // Output window operations
    void onClearOutputClicked();

    // Shared variables monitoring
    void onUpdateSharedVariables();

    // Unit conversion (keep if still relevant)
    void onUnitTypeChanged(UnitType type);


private:
    Ui::ProgramWidget *ui;

    // Lua Executor
    LuaExecutor* m_luaExecutor;

    // UI Controls
    QTabWidget* m_scriptTabWidget;
    QPlainTextEdit* m_scriptEditorCoord1;
    QPlainTextEdit* m_scriptEditorCoord2;
    QTextEdit* m_outputWindow;
    QPushButton* m_clearOutputButton; // 清除输出按钮
    QComboBox* m_commandComboBox;
    QTableWidget* m_sharedVariablesTable;

    // Syntax Highlighters
    LuaSyntaxHighlighter* m_highlighterCoord1;
    LuaSyntaxHighlighter* m_highlighterCoord2;

    // Shared variables timer
    QTimer* m_sharedVariablesTimer;

    // Unit converter (keep if still relevant)
    UnitConverter* m_unitConverter;

    // Helper function to get current script editor based on tab
    QPlainTextEdit* currentScriptEditor();

    // Initialize UI
    void setupUI();

    // Populate command combo box
    void populateCommandComboBox();

    // Update UI state based on LuaExecutor state
    void updateUIState(LuaExecutor::CoordSystem coord, LuaExecutor::State state);
};

#endif // PROGRAMWIDGET_H
